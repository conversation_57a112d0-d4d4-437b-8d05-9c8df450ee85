# 商品管理模块 - 库存调拨新增页面功能设计文档

## 1. 页面概述

### 1.1 页面目的
库存调拨新增页面是智能家装管理平台商品管理系统的核心业务页面，负责在两个仓库或门店之间发起内部商品调拨。该页面通过规范化的调拨流程，实现库存在不同仓库间的合理分配和高效流转。

### 1.2 业务价值
- 提供标准化的库存调拨创建功能，优化库存分布和资源配置
- 支持多种调拨场景，满足门店补货、库存平衡、积压减少等业务需求
- 建立完整的调拨记录，确保库存流转的可追溯性和准确性
- 实现调拨与库存的实时联动，确保库存数据的准确性
- 提供完整的调拨流程控制，规范内部库存流转业务

### 1.3 页面入口
- **主要入口**：商品管理 → 库存调拨 → 新增
- **快速入口**：库存管理相关页面的快速调拨链接
- **业务入口**：库存预警、门店补货等业务场景的调拨申请

### 1.4 功能架构
库存调拨新增页面包含四个核心功能区域：
- **基础信息管理**：调出调入仓库配置和调拨单元数据
- **商品选择管理**：可调拨商品的选择和库存展示
- **调拨明细管理**：调拨商品明细和数量管理
- **操作流程控制**：调拨单的保存、提交和流程控制

### 1.5 典型应用场景
- **门店库存紧张**：从总部仓调货补充门店库存
- **热销商品补仓**：热销商品的快速补货调拨
- **仓库平衡库存**：减少积压，优化库存分布
- **季节性调拨**：根据季节变化调整库存分布

### 1.6 业务流程
库存调拨的完整业务流程：
```
创建调拨单 → 保存 → 调拨出库（锁定出库库存） → 调入仓库确认 → 入库 → 调拨完成
```

## 2. 库存调拨新增页面操作流程图

```mermaid
flowchart TD
    A[用户访问库存调拨新增页面] --> B[权限验证]
    B --> C{权限验证通过?}
    C -->|否| D[显示权限不足提示]
    C -->|是| E[页面初始化]
    
    E --> F[加载基础信息区]
    E --> G[加载商品选择器]
    E --> H[初始化明细表格]
    E --> I[显示操作按钮]
    
    F --> J[自动填充默认信息]
    J --> K[调出门店默认当前组织]
    J --> L[调拨单号自动生成]
    J --> M[经手人默认当前用户]
    
    F --> N[调拨仓库配置]
    N --> O[调出仓库选择]
    N --> P[调入门店选择]
    N --> Q[调入仓库选择]
    
    P --> R{调入门店验证}
    R -->|与调出门店相同| S[显示门店冲突提示]
    R -->|门店不同| T[门店验证通过]
    
    O --> U[调出仓库库存加载]
    Q --> V{调入调出仓库验证}
    V -->|仓库相同| W[显示仓库冲突提示]
    V -->|仓库不同| X[仓库验证通过]
    
    G --> Y[商品选择器展示]
    Y --> Z[商品列表加载]
    Z --> AA[商品信息展示]
    AA --> BB[商品缩略图]
    AA --> CC[商品编码名称]
    AA --> DD[SN管理提示]
    AA --> EE[当前库存显示]
    
    Y --> FF[商品搜索功能]
    FF --> GG[扫码搜索]
    FF --> HH[手动搜索]
    
    GG --> II[扫码枪输入]
    II --> JJ[商品识别]
    JJ --> KK{识别成功?}
    KK -->|否| LL[显示识别失败提示]
    KK -->|是| MM[添加到明细表格]
    
    HH --> NN[商品名称编码搜索]
    NN --> OO[搜索结果展示]
    OO --> PP[点击选择商品]
    PP --> MM
    
    Z --> QQ[点击选择商品]
    QQ --> RR{库存验证}
    RR -->|库存为0| SS[显示库存不足提示]
    RR -->|库存充足| MM
    
    MM --> TT[明细表格更新]
    TT --> UU[商品信息展示]
    UU --> VV[商品编码]
    UU --> WW[商品名称]
    UU --> XX[计量单位]
    UU --> YY[当前库存]
    
    TT --> ZZ[调拨信息录入]
    ZZ --> AAA[调拨数量输入]
    ZZ --> BBB[序列号管理]
    ZZ --> CCC[商品备注]
    
    AAA --> DDD{数量验证}
    DDD -->|数量≤0| EEE[显示数量错误提示]
    DDD -->|数量>库存| FFF[显示库存不足提示]
    DDD -->|数量合理| GGG[数量验证通过]
    
    BBB --> HHH{是否启用SN?}
    HHH -->|是| III[序列号选择绑定]
    HHH -->|否| JJJ[跳过序列号管理]
    
    III --> KKK[可用序列号列表]
    KKK --> LLL[选择具体序列号]
    LLL --> MMM{序列号数量匹配?}
    MMM -->|不匹配| NNN[显示数量不匹配提示]
    MMM -->|匹配| OOO[序列号绑定完成]
    
    TT --> PPP[商品操作]
    PPP --> QQQ[删除商品]
    PPP --> RRR[修改数量]
    
    I --> SSS[操作按钮功能]
    SSS --> TTT[返回操作]
    SSS --> UUU[保存草稿]
    SSS --> VVV[调拨出库]
    
    TTT --> WWW[返回调拨列表]
    
    UUU --> XXX{基础信息验证}
    XXX -->|验证失败| YYY[显示验证错误提示]
    XXX -->|验证通过| ZZZ[保存调拨草稿]
    ZZZ --> AAAA[草稿状态更新]
    AAAA --> BBBB[支持二次编辑]
    
    VVV --> CCCC{完整性验证}
    CCCC -->|验证失败| DDDD[显示完整性错误提示]
    CCCC -->|验证通过| EEEE[提交调拨出库]
    
    EEEE --> FFFF[生成正式调拨单号]
    FFFF --> GGGG[锁定出库库存]
    GGGG --> HHHH[更新调拨状态]
    HHHH --> IIII[发送调拨通知]
    IIII --> JJJJ[等待调入方确认]
    JJJJ --> WWW
    
    style A fill:#e1f5fe
    style C fill:#fff3e0
    style D fill:#ffebee
    style R fill:#fff3e0
    style S fill:#ffebee
    style V fill:#fff3e0
    style W fill:#ffebee
    style KK fill:#fff3e0
    style LL fill:#ffebee
    style RR fill:#fff3e0
    style SS fill:#fff8e1
    style DDD fill:#fff3e0
    style EEE fill:#ffebee
    style FFF fill:#fff8e1
    style MMM fill:#fff3e0
    style NNN fill:#ffebee
    style XXX fill:#fff3e0
    style YYY fill:#ffebee
    style CCCC fill:#fff3e0
    style DDDD fill:#ffebee
    style IIII fill:#e8f5e8
```

### 流程说明
库存调拨新增页面的操作流程主要包含以下几个核心环节：

1. **权限验证与页面初始化**：验证用户调拨权限，初始化页面各功能区域
2. **调拨仓库配置验证**：验证调出调入门店和仓库的合理性，防止同仓库调拨
3. **商品选择和库存验证**：多方式商品选择，实时验证库存可用性
4. **调拨明细管理**：数量录入、序列号绑定、商品备注等明细信息管理
5. **数据验证和保存**：完整性验证后保存草稿或提交调拨出库
6. **调拨流程启动**：锁定库存，发送通知，等待调入方确认

## 3. 详细功能设计

### 3.1 基础信息管理功能

#### 3.1.1 调拨仓库配置
**功能描述**：配置调拨的出库和入库仓库信息

**仓库配置字段**：
- **调出门店**：发货方组织
  - 显示字段，默认当前操作组织
  - 不可修改，基于用户权限确定
  - 影响调出仓库的可选范围
- **调出仓库**：选择出库仓位
  - 下拉选择，必填字段
  - 基于调出门店筛选可选仓库
  - 影响商品库存的显示范围
- **调入门店**：接收方门店或组织
  - 下拉选择，必填字段
  - 限制选项为非当前门店
  - 防止同门店内部调拨
- **调入仓库**：目标仓位
  - 下拉选择，必填字段
  - 基于调入门店筛选可选仓库
  - 与调出仓库不能相同

#### 3.1.2 调拨单基础信息
**功能描述**：管理调拨单的基础元数据

**基础信息字段**：
- **调拨单号**：自动生成唯一编号
  - 显示字段，系统自动生成（如DB...）
  - 唯一性保证
  - 支持自定义编号规则
- **经手人**：当前操作人
  - 下拉选择或系统识别
  - 默认当前登录账号
  - 支持修改为其他有权限用户
- **调拨日期**：调拨申请日期
  - 默认当前日期
  - 支持手动调整
- **备注信息**：自定义说明
  - 文本输入，非必填
  - 如"门店季节性补货"
  - 支持常用备注模板

#### 3.1.3 联动规则验证
**功能描述**：验证调拨配置的合理性

**验证规则**：
- **仓库冲突检查**：调入仓与调出仓不能一致
- **门店权限验证**：调入门店必须为非当前门店
- **库存可用性**：调出仓库必须有可用库存
- **权限范围验证**：用户对调入调出仓库的操作权限

### 3.2 商品选择管理功能

#### 3.2.1 商品选择器展示
**功能描述**：右侧商品选择器的展示和交互

**展示内容**：
- **商品缩略图**：商品图片展示
- **编码名称**：商品编码和名称信息
- **SN管理提示**：是否启用序列号管理的标识
- **当前库存**：在选定调出仓的可用库存
- **选择操作**：点击商品添加到明细表格

#### 3.2.2 商品搜索功能
**功能描述**：多种方式的商品搜索和定位

**搜索方式**：
- **扫码搜索**：支持扫码枪快速定位商品
  - 条码识别
  - 自动匹配商品
  - 快速添加到明细
- **手动搜索**：商品名称、编码搜索
  - 模糊匹配
  - 实时搜索建议
  - 搜索结果高亮

#### 3.2.3 库存可用性检查
**功能描述**：检查商品在调出仓库的可用性

**检查内容**：
- **库存数量显示**：实时显示调出仓库存
- **可用库存计算**：扣除已预占库存
- **零库存处理**：库存为0时不允许添加或高亮提示
- **库存预警**：接近安全库存的预警

### 3.3 调拨明细管理功能

#### 3.3.1 明细信息展示
**功能描述**：展示已选商品的调拨明细信息

**明细字段**：
- **商品编码**：系统内唯一标识
- **商品名称**：展示名称
- **计量单位**：个、台、套等计量单位
- **当前库存**：来自调出仓的实时可用库存
- **调拨数量**：用户输入调拨数量（不可超过库存）
- **序列号（SN）**：若启用序列号管理，需绑定具体SN列表
- **备注**：针对商品级别的备注说明
- **操作**：删除该商品记录

#### 3.3.2 调拨数量管理
**功能描述**：管理商品的调拨数量

**数量管理**：
- **数量输入验证**：
  - 非负数验证
  - 不能超过当前库存
  - 必须为整数
- **库存实时检查**：
  - 实时显示可用库存
  - 数量超限时即时提示
  - 库存变化时自动更新
- **批量数量设置**：
  - 支持批量设置调拨数量
  - 按比例分配调拨数量
  - 快速清零或最大值设置

#### 3.3.3 序列号管理
**功能描述**：管理启用SN控制的商品序列号

**SN管理**：
- **SN标识显示**：商品名称显示SN管理提示
- **序列号选择**：从调出仓可用序列号中选择
- **数量匹配验证**：序列号数量与调拨数量匹配
- **序列号绑定**：建立序列号与调拨记录的关联
- **序列号跟踪**：记录序列号的调拨流转轨迹

### 3.4 数据验证功能

#### 3.4.1 基础信息验证
**功能描述**：验证调拨单基础信息的完整性

**验证规则**：
- **必填字段验证**：调出调入仓库、经手人等必填
- **仓库冲突验证**：调出调入仓库不能相同
- **门店权限验证**：调入门店权限和范围验证
- **日期合理性验证**：调拨日期的合理性检查

#### 3.4.2 明细数据验证
**功能描述**：验证调拨明细的准确性

**验证内容**：
- **商品存在性**：验证商品在调出仓库存在
- **数量合理性**：验证调拨数量不超过可用库存
- **序列号匹配**：SN商品的序列号数量匹配验证
- **明细完整性**：至少包含一个调拨商品

#### 3.4.3 业务规则验证
**功能描述**：验证调拨业务规则的合理性

**业务验证**：
- **库存充足性**：确保调出仓库存充足
- **调拨权限**：验证商品的调拨权限
- **仓库状态**：验证调出调入仓库状态正常
- **商品状态**：验证商品状态允许调拨

### 3.5 操作流程控制

#### 3.5.1 保存草稿功能
**功能描述**：保存调拨单草稿

**草稿功能**：
- **基础信息验证**：验证基础信息完整性
- **草稿状态保存**：保存为草稿状态
- **二次编辑支持**：支持对草稿的二次编辑
- **自动保存**：定时自动保存草稿

#### 3.5.2 调拨出库功能
**功能描述**：提交调拨单并执行出库

**出库流程**：
- **完整性验证**：验证调拨信息完整性
- **业务规则检查**：检查所有业务规则
- **库存锁定**：锁定调出仓库存
- **状态更新**：更新调拨单状态
- **通知发送**：发送调拨通知给调入方

#### 3.5.3 返回操作功能
**功能描述**：返回调拨列表页面

**返回处理**：
- **数据保护**：提醒保存未提交的数据
- **页面跳转**：返回库存调拨列表页面
- **状态恢复**：恢复列表页面状态

### 3.6 调拨流程管理

#### 3.6.1 调拨状态管理
**功能描述**：管理调拨单的状态流转

**状态类型**：
- **草稿状态**：调拨单已保存但未提交
- **调拨中状态**：已提交出库，等待调入确认
- **已完成状态**：调拨流程完成
- **已取消状态**：调拨单已取消

#### 3.6.2 调拨通知管理
**功能描述**：管理调拨相关的通知

**通知类型**：
- **调拨申请通知**：通知调入方有新的调拨申请
- **出库完成通知**：通知调入方商品已出库
- **入库提醒通知**：提醒调入方及时确认入库
- **超期预警通知**：调拨超期未完成的预警

## 4. 用户界面设计

### 4.1 页面布局设计
- **顶部基础信息区**：调拨仓库配置和基础信息
- **右侧商品选择器**：可调拨商品列表和搜索
- **中部明细表格区**：调拨商品明细展示和编辑
- **底部操作按钮区**：返回、保存、调拨出库等操作

### 4.2 交互设计规范
- **智能验证**：仓库选择时实时验证冲突
- **库存提示**：实时显示库存状态和可用性
- **友好提示**：清晰的错误提示和操作指导
- **快捷操作**：支持扫码快速选择商品

### 4.3 响应式设计
- **PC端**：完整功能展示，多区域布局
- **平板端**：适配中等屏幕，关键功能优先
- **移动端**：简化布局，核心功能保留

## 5. 权限控制

### 5.1 功能权限
- **页面访问权限**：库存调拨新增页面访问权限
- **仓库操作权限**：调出调入仓库的操作权限
- **商品调拨权限**：商品调拨申请权限
- **调拨提交权限**：调拨单提交权限

### 5.2 数据权限
- **门店权限**：只能在有权限的门店间调拨
- **仓库权限**：只能操作有权限的仓库
- **商品权限**：只能调拨有权限的商品类别

## 6. 异常处理

### 6.1 业务异常
- **库存不足**：调拨数量超过可用库存的处理
- **仓库冲突**：调出调入仓库相同的处理
- **权限不足**：操作权限不足的处理
- **数据验证失败**：输入数据不符合业务规则的处理

### 6.2 系统异常
- **网络异常**：网络连接异常的处理和重试
- **数据保存失败**：数据保存失败的处理和恢复
- **库存同步异常**：库存数据同步异常的处理
- **系统错误**：系统错误的异常处理和用户提示

---

**文档版本**：v1.0  
**编写日期**：2025-07-02  
**编写人员**：AI系统架构师  
**审核状态**：待审核
