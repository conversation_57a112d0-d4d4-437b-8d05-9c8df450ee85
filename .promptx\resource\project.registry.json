{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-16T15:05:39.893Z", "updatedAt": "2025-07-16T15:05:39.914Z", "resourceCount": 36}, "resources": [{"id": "dependency-manager", "source": "project", "protocol": "role", "name": "Dependency Manager 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/dependency-manager/dependency-manager.role.md", "metadata": {"createdAt": "2025-07-16T15:05:39.895Z", "updatedAt": "2025-07-16T15:05:39.895Z", "scannedAt": "2025-07-16T15:05:39.895Z", "path": "role/dependency-manager/dependency-manager.role.md"}}, {"id": "dependency-management-workflow", "source": "project", "protocol": "execution", "name": "Dependency Management Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/dependency-manager/execution/dependency-management-workflow.execution.md", "metadata": {"createdAt": "2025-07-16T15:05:39.896Z", "updatedAt": "2025-07-16T15:05:39.896Z", "scannedAt": "2025-07-16T15:05:39.896Z", "path": "role/dependency-manager/execution/dependency-management-workflow.execution.md"}}, {"id": "dependency-management-mindset", "source": "project", "protocol": "thought", "name": "Dependency Management Mindset 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/dependency-manager/thought/dependency-management-mindset.thought.md", "metadata": {"createdAt": "2025-07-16T15:05:39.896Z", "updatedAt": "2025-07-16T15:05:39.897Z", "scannedAt": "2025-07-16T15:05:39.896Z", "path": "role/dependency-manager/thought/dependency-management-mindset.thought.md"}}, {"id": "basic-quality-checker", "source": "project", "protocol": "execution", "name": "Basic Quality Checker 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/java-tdd-architect/execution/basic-quality-checker.execution.md", "metadata": {"createdAt": "2025-07-16T15:05:39.898Z", "updatedAt": "2025-07-16T15:05:39.898Z", "scannedAt": "2025-07-16T15:05:39.898Z", "path": "role/java-tdd-architect/execution/basic-quality-checker.execution.md"}}, {"id": "java-tdd-workflow", "source": "project", "protocol": "execution", "name": "Java Tdd Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/java-tdd-architect/execution/java-tdd-workflow.execution.md", "metadata": {"createdAt": "2025-07-16T15:05:39.898Z", "updatedAt": "2025-07-16T15:05:39.898Z", "scannedAt": "2025-07-16T15:05:39.898Z", "path": "role/java-tdd-architect/execution/java-tdd-workflow.execution.md"}}, {"id": "java-tdd-architect", "source": "project", "protocol": "role", "name": "Java Tdd Architect 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/java-tdd-architect/java-tdd-architect.role.md", "metadata": {"createdAt": "2025-07-16T15:05:39.899Z", "updatedAt": "2025-07-16T15:05:39.899Z", "scannedAt": "2025-07-16T15:05:39.899Z", "path": "role/java-tdd-architect/java-tdd-architect.role.md"}}, {"id": "tdd-architect-mindset", "source": "project", "protocol": "thought", "name": "Tdd Architect Mindset 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/java-tdd-architect/thought/tdd-architect-mindset.thought.md", "metadata": {"createdAt": "2025-07-16T15:05:39.899Z", "updatedAt": "2025-07-16T15:05:39.899Z", "scannedAt": "2025-07-16T15:05:39.899Z", "path": "role/java-tdd-architect/thought/tdd-architect-mindset.thought.md"}}, {"id": "enhanced-mdc-workflow", "source": "project", "protocol": "execution", "name": "Enhanced Mdc Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/java-tdd-expert/execution/enhanced-mdc-workflow.execution.md", "metadata": {"createdAt": "2025-07-16T15:05:39.900Z", "updatedAt": "2025-07-16T15:05:39.900Z", "scannedAt": "2025-07-16T15:05:39.900Z", "path": "role/java-tdd-expert/execution/enhanced-mdc-workflow.execution.md"}}, {"id": "mdc-generation-workflow", "source": "project", "protocol": "execution", "name": "Mdc Generation Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/java-tdd-expert/execution/mdc-generation-workflow.execution.md", "metadata": {"createdAt": "2025-07-16T15:05:39.901Z", "updatedAt": "2025-07-16T15:05:39.901Z", "scannedAt": "2025-07-16T15:05:39.901Z", "path": "role/java-tdd-expert/execution/mdc-generation-workflow.execution.md"}}, {"id": "java-tdd-expert", "source": "project", "protocol": "role", "name": "Java Tdd Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/java-tdd-expert/java-tdd-expert.role.md", "metadata": {"createdAt": "2025-07-16T15:05:39.901Z", "updatedAt": "2025-07-16T15:05:39.901Z", "scannedAt": "2025-07-16T15:05:39.901Z", "path": "role/java-tdd-expert/java-tdd-expert.role.md"}}, {"id": "java-tdd-mindset", "source": "project", "protocol": "thought", "name": "Java Tdd Mindset 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/java-tdd-expert/thought/java-tdd-mindset.thought.md", "metadata": {"createdAt": "2025-07-16T15:05:39.902Z", "updatedAt": "2025-07-16T15:05:39.902Z", "scannedAt": "2025-07-16T15:05:39.902Z", "path": "role/java-tdd-expert/thought/java-tdd-mindset.thought.md"}}, {"id": "mdc-analysis-mindset", "source": "project", "protocol": "thought", "name": "Mdc Analysis Mindset 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/java-tdd-expert/thought/mdc-analysis-mindset.thought.md", "metadata": {"createdAt": "2025-07-16T15:05:39.903Z", "updatedAt": "2025-07-16T15:05:39.903Z", "scannedAt": "2025-07-16T15:05:39.902Z", "path": "role/java-tdd-expert/thought/mdc-analysis-mindset.thought.md"}}, {"id": "expert-coordination", "source": "project", "protocol": "execution", "name": "Expert Coordination 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/muzi/execution/expert-coordination.execution.md", "metadata": {"createdAt": "2025-07-16T15:05:39.903Z", "updatedAt": "2025-07-16T15:05:39.903Z", "scannedAt": "2025-07-16T15:05:39.903Z", "path": "role/muzi/execution/expert-coordination.execution.md"}}, {"id": "tdd-workflow", "source": "project", "protocol": "execution", "name": "Tdd Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/muzi/execution/tdd-workflow.execution.md", "metadata": {"createdAt": "2025-07-16T15:05:39.904Z", "updatedAt": "2025-07-16T15:05:39.904Z", "scannedAt": "2025-07-16T15:05:39.904Z", "path": "role/muzi/execution/tdd-workflow.execution.md"}}, {"id": "muzi", "source": "project", "protocol": "role", "name": "<PERSON>zi 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/muzi/muzi.role.md", "metadata": {"createdAt": "2025-07-16T15:05:39.904Z", "updatedAt": "2025-07-16T15:05:39.904Z", "scannedAt": "2025-07-16T15:05:39.904Z", "path": "role/muzi/muzi.role.md"}}, {"id": "coordination-thinking", "source": "project", "protocol": "thought", "name": "Coordination Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/muzi/thought/coordination-thinking.thought.md", "metadata": {"createdAt": "2025-07-16T15:05:39.904Z", "updatedAt": "2025-07-16T15:05:39.904Z", "scannedAt": "2025-07-16T15:05:39.904Z", "path": "role/muzi/thought/coordination-thinking.thought.md"}}, {"id": "system-orchestration", "source": "project", "protocol": "thought", "name": "System Orchestration 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/muzi/thought/system-orchestration.thought.md", "metadata": {"createdAt": "2025-07-16T15:05:39.905Z", "updatedAt": "2025-07-16T15:05:39.905Z", "scannedAt": "2025-07-16T15:05:39.905Z", "path": "role/muzi/thought/system-orchestration.thought.md"}}, {"id": "design-generation-workflow", "source": "project", "protocol": "execution", "name": "Design Generation Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/prd-design-expert/execution/design-generation-workflow.execution.md", "metadata": {"createdAt": "2025-07-16T15:05:39.905Z", "updatedAt": "2025-07-16T15:05:39.905Z", "scannedAt": "2025-07-16T15:05:39.905Z", "path": "role/prd-design-expert/execution/design-generation-workflow.execution.md"}}, {"id": "prd-analysis-workflow", "source": "project", "protocol": "execution", "name": "Prd Analysis Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/prd-design-expert/execution/prd-analysis-workflow.execution.md", "metadata": {"createdAt": "2025-07-16T15:05:39.906Z", "updatedAt": "2025-07-16T15:05:39.906Z", "scannedAt": "2025-07-16T15:05:39.906Z", "path": "role/prd-design-expert/execution/prd-analysis-workflow.execution.md"}}, {"id": "technical-design-template-workflow", "source": "project", "protocol": "execution", "name": "Technical Design Template Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/prd-design-expert/execution/technical-design-template-workflow.execution.md", "metadata": {"createdAt": "2025-07-16T15:05:39.906Z", "updatedAt": "2025-07-16T15:05:39.906Z", "scannedAt": "2025-07-16T15:05:39.906Z", "path": "role/prd-design-expert/execution/technical-design-template-workflow.execution.md"}}, {"id": "prd-design-expert", "source": "project", "protocol": "role", "name": "Prd Design Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/prd-design-expert/prd-design-expert.role.md", "metadata": {"createdAt": "2025-07-16T15:05:39.906Z", "updatedAt": "2025-07-16T15:05:39.906Z", "scannedAt": "2025-07-16T15:05:39.906Z", "path": "role/prd-design-expert/prd-design-expert.role.md"}}, {"id": "document-engineering-mindset", "source": "project", "protocol": "thought", "name": "Document Engineering Mindset 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/prd-design-expert/thought/document-engineering-mindset.thought.md", "metadata": {"createdAt": "2025-07-16T15:05:39.907Z", "updatedAt": "2025-07-16T15:05:39.907Z", "scannedAt": "2025-07-16T15:05:39.907Z", "path": "role/prd-design-expert/thought/document-engineering-mindset.thought.md"}}, {"id": "prd-analysis-mindset", "source": "project", "protocol": "thought", "name": "Prd Analysis Mindset 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/prd-design-expert/thought/prd-analysis-mindset.thought.md", "metadata": {"createdAt": "2025-07-16T15:05:39.907Z", "updatedAt": "2025-07-16T15:05:39.907Z", "scannedAt": "2025-07-16T15:05:39.907Z", "path": "role/prd-design-expert/thought/prd-analysis-mindset.thought.md"}}, {"id": "quality-assurance-workflow", "source": "project", "protocol": "execution", "name": "Quality Assurance Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/quality-assurance-expert/execution/quality-assurance-workflow.execution.md", "metadata": {"createdAt": "2025-07-16T15:05:39.908Z", "updatedAt": "2025-07-16T15:05:39.908Z", "scannedAt": "2025-07-16T15:05:39.908Z", "path": "role/quality-assurance-expert/execution/quality-assurance-workflow.execution.md"}}, {"id": "quality-assurance-expert", "source": "project", "protocol": "role", "name": "Quality Assurance Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/quality-assurance-expert/quality-assurance-expert.role.md", "metadata": {"createdAt": "2025-07-16T15:05:39.908Z", "updatedAt": "2025-07-16T15:05:39.908Z", "scannedAt": "2025-07-16T15:05:39.908Z", "path": "role/quality-assurance-expert/quality-assurance-expert.role.md"}}, {"id": "quality-assurance-mindset", "source": "project", "protocol": "thought", "name": "Quality Assurance Mindset 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/semi-structured-test-generator/thought/quality-assurance-mindset.thought.md", "metadata": {"createdAt": "2025-07-16T15:05:39.910Z", "updatedAt": "2025-07-16T15:05:39.910Z", "scannedAt": "2025-07-16T15:05:39.910Z", "path": "role/semi-structured-test-generator/thought/quality-assurance-mindset.thought.md"}}, {"id": "quality-assurance-system", "source": "project", "protocol": "execution", "name": "Quality Assurance System 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/semi-structured-test-generator/execution/quality-assurance-system.execution.md", "metadata": {"createdAt": "2025-07-16T15:05:39.909Z", "updatedAt": "2025-07-16T15:05:39.909Z", "scannedAt": "2025-07-16T15:05:39.909Z", "path": "role/semi-structured-test-generator/execution/quality-assurance-system.execution.md"}}, {"id": "semi-structured-conversion-workflow", "source": "project", "protocol": "execution", "name": "Semi Structured Conversion Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/semi-structured-test-generator/execution/semi-structured-conversion-workflow.execution.md", "metadata": {"createdAt": "2025-07-16T15:05:39.910Z", "updatedAt": "2025-07-16T15:05:39.910Z", "scannedAt": "2025-07-16T15:05:39.910Z", "path": "role/semi-structured-test-generator/execution/semi-structured-conversion-workflow.execution.md"}}, {"id": "semi-structured-test-generator", "source": "project", "protocol": "role", "name": "Semi Structured Test Generator 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/semi-structured-test-generator/semi-structured-test-generator.role.md", "metadata": {"createdAt": "2025-07-16T15:05:39.910Z", "updatedAt": "2025-07-16T15:05:39.910Z", "scannedAt": "2025-07-16T15:05:39.910Z", "path": "role/semi-structured-test-generator/semi-structured-test-generator.role.md"}}, {"id": "testing-mindset", "source": "project", "protocol": "thought", "name": "Testing Mindset 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/semi-structured-test-generator/thought/testing-mindset.thought.md", "metadata": {"createdAt": "2025-07-16T15:05:39.911Z", "updatedAt": "2025-07-16T15:05:39.911Z", "scannedAt": "2025-07-16T15:05:39.911Z", "path": "role/semi-structured-test-generator/thought/testing-mindset.thought.md"}}, {"id": "test-case-generation-workflow", "source": "project", "protocol": "execution", "name": "Test Case Generation Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/test-case-generator/execution/test-case-generation-workflow.execution.md", "metadata": {"createdAt": "2025-07-16T15:05:39.911Z", "updatedAt": "2025-07-16T15:05:39.911Z", "scannedAt": "2025-07-16T15:05:39.911Z", "path": "role/test-case-generator/execution/test-case-generation-workflow.execution.md"}}, {"id": "test-case-generator", "source": "project", "protocol": "role", "name": "Test Case Generator 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/test-case-generator/test-case-generator.role.md", "metadata": {"createdAt": "2025-07-16T15:05:39.912Z", "updatedAt": "2025-07-16T15:05:39.912Z", "scannedAt": "2025-07-16T15:05:39.912Z", "path": "role/test-case-generator/test-case-generator.role.md"}}, {"id": "structured-test-design-mindset", "source": "project", "protocol": "thought", "name": "Structured Test Design Mindset 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/test-case-generator/thought/structured-test-design-mindset.thought.md", "metadata": {"createdAt": "2025-07-16T15:05:39.912Z", "updatedAt": "2025-07-16T15:05:39.912Z", "scannedAt": "2025-07-16T15:05:39.912Z", "path": "role/test-case-generator/thought/structured-test-design-mindset.thought.md"}}, {"id": "tdd-integration-mindset", "source": "project", "protocol": "thought", "name": "Tdd Integration Mindset 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/test-case-generator/thought/tdd-integration-mindset.thought.md", "metadata": {"createdAt": "2025-07-16T15:05:39.913Z", "updatedAt": "2025-07-16T15:05:39.913Z", "scannedAt": "2025-07-16T15:05:39.913Z", "path": "role/test-case-generator/thought/tdd-integration-mindset.thought.md"}}, {"id": "test-case-design-mindset", "source": "project", "protocol": "thought", "name": "Test Case Design Mindset 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/test-case-generator/thought/test-case-design-mindset.thought.md", "metadata": {"createdAt": "2025-07-16T15:05:39.913Z", "updatedAt": "2025-07-16T15:05:39.913Z", "scannedAt": "2025-07-16T15:05:39.913Z", "path": "role/test-case-generator/thought/test-case-design-mindset.thought.md"}}, {"id": "test-coverage-optimization-mindset", "source": "project", "protocol": "thought", "name": "Test Coverage Optimization Mindset 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/test-case-generator/thought/test-coverage-optimization-mindset.thought.md", "metadata": {"createdAt": "2025-07-16T15:05:39.913Z", "updatedAt": "2025-07-16T15:05:39.913Z", "scannedAt": "2025-07-16T15:05:39.913Z", "path": "role/test-case-generator/thought/test-coverage-optimization-mindset.thought.md"}}], "stats": {"totalResources": 36, "byProtocol": {"role": 8, "execution": 14, "thought": 14}, "bySource": {"project": 36}}}