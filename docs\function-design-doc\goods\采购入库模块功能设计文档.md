# 商品管理模块 - 采购入库功能设计文档

## 1. 模块概述

### 1.1 模块目的
采购入库模块是智能家装管理平台商品管理系统的核心业务模块，负责采购单据管理、入库流程控制、供应商管理和库存补充。该模块通过完整的采购流程管理，确保商品采购的规范性、可追溯性和库存数据的准确性。

### 1.2 业务价值
- 建立完整的采购单据管理体系，规范采购流程和审批机制
- 提供多维度的采购数据查询和统计分析，支持采购决策
- 实现采购单生命周期管理，从新增到入库的全流程跟踪
- 支持供应商管理和物流信息跟踪，提升采购效率
- 建立采购数据汇总和记录查询，便于财务核算和库存管理

### 1.3 功能架构
采购入库模块包含六个核心功能：
- **采购单管理**: 采购单据的新增、编辑、审核和删除
- **多视图展示**: 采购单、商品汇总、商品记录、入库单四种视图
- **筛选查询功能**: 多维度采购数据筛选和快速查询
- **审核入库流程**: 采购单审核和自动入库处理
- **物流跟踪管理**: 采购商品物流信息跟踪和状态监控
- **数据统计分析**: 采购金额、实付金额、未入库金额统计

## 2. 采购入库模块操作流程图

```mermaid
flowchart TD
    A[用户访问采购入库页面] --> B[权限验证]
    B --> C{权限验证通过?}
    C -->|否| D[显示权限不足提示]
    C -->|是| E[加载采购入库数据]
    
    E --> F[显示顶部导航切换]
    E --> G[显示筛选操作区]
    E --> H[显示数据统计栏]
    E --> I[加载采购单数据表格]
    
    F --> J[采购单视图]
    F --> K[商品汇总视图]
    F --> L[商品记录视图]
    F --> M[入库单视图]
    
    J --> N[当前默认页面]
    K --> O[按商品统计汇总]
    L --> P[采购记录明细]
    M --> Q[入库单据管理]
    
    G --> R[筛选条件设置]
    R --> S[供应商筛选]
    R --> T[经手人筛选]
    R --> U[结算账户筛选]
    R --> V[状态筛选]
    R --> W[单号查询]
    R --> X[采购日期范围]
    
    S --> Y[组合筛选处理]
    T --> Y
    U --> Y
    V --> Y
    W --> Y
    X --> Y
    
    Y --> Z[点击查询按钮]
    Z --> AA[发送筛选请求]
    AA --> BB[更新数据表格]
    AA --> CC[更新统计数据]
    
    G --> DD[操作按钮功能]
    DD --> EE[新增采购单]
    DD --> FF[打印功能]
    DD --> GG[清空筛选]
    
    EE --> HH[跳转新增页面]
    HH --> II[填写采购信息]
    II --> JJ[选择供应商]
    II --> KK[添加采购商品]
    II --> LL[设置结算账户]
    II --> MM[保存采购单]
    MM --> NN{保存成功?}
    NN -->|否| OO[显示保存失败提示]
    NN -->|是| PP[返回采购单列表]
    
    FF --> QQ[选择打印内容]
    QQ --> RR[生成打印文件]
    
    GG --> SS[清空所有筛选条件]
    SS --> TT[重新加载数据]
    
    I --> UU[采购单数据展示]
    UU --> VV[行级操作按钮]
    VV --> WW[详情查看]
    VV --> XX[引用复制]
    VV --> YY[审核操作]
    VV --> ZZ[删除操作]
    VV --> AAA[待办标记]
    VV --> BBB[物流查看]
    
    WW --> CCC[显示采购单详情]
    CCC --> DDD[商品明细信息]
    CCC --> EEE[供应商信息]
    CCC --> FFF[物流信息]
    
    XX --> GGG[复制采购单信息]
    GGG --> HHH[生成新采购单]
    HHH --> III[允许修改调整]
    III --> JJJ[保存新采购单]
    
    YY --> KKK{审核权限检查}
    KKK -->|无权限| LLL[显示权限不足]
    KKK -->|有权限| MMM[审核确认]
    MMM --> NNN{确认审核?}
    NNN -->|否| OOO[取消审核]
    NNN -->|是| PPP[执行审核操作]
    PPP --> QQQ[更新单据状态]
    QQQ --> RRR[生成入库记录]
    RRR --> SSS[更新库存数据]
    SSS --> TTT[发送审核通知]
    
    ZZ --> UUU{删除权限检查}
    UUU -->|无权限| VVV[显示权限不足]
    UUU -->|状态不允许| WWW[显示状态限制提示]
    UUU -->|可删除| XXX[删除确认]
    XXX --> YYY{确认删除?}
    YYY -->|否| ZZZ[取消删除]
    YYY -->|是| AAAA[执行删除操作]
    AAAA --> BBBB[更新数据列表]
    
    AAA --> CCCC[添加到待办列表]
    CCCC --> DDDD[设置提醒时间]
    DDDD --> EEEE[保存待办任务]
    
    BBB --> FFFF[查询物流信息]
    FFFF --> GGGG[显示物流状态]
    GGGG --> HHHH[运输进度跟踪]
    GGGG --> IIII[签收状态确认]
    
    H --> JJJJ[统计数据展示]
    JJJJ --> KKKK[采购金额统计]
    JJJJ --> LLLL[实付金额统计]
    JJJJ --> MMMM[未入库金额统计]
    
    KKKK --> NNNN[实时数据更新]
    LLLL --> NNNN
    MMMM --> NNNN
    
    style A fill:#e1f5fe
    style C fill:#fff3e0
    style D fill:#ffebee
    style KKK fill:#fff3e0
    style LLL fill:#ffebee
    style NNN fill:#fff3e0
    style UUU fill:#fff3e0
    style VVV fill:#ffebee
    style WWW fill:#fff8e1
    style XXX fill:#fff3e0
    style YYY fill:#fff3e0
    style TTT fill:#e8f5e8
```

### 流程说明
采购入库模块的操作流程主要包含以下几个核心环节：

1. **权限验证与多视图加载**：验证用户访问权限，提供采购单、商品汇总、商品记录、入库单四种视图切换
2. **多维度筛选查询**：支持供应商、经手人、结算账户、状态、单号、日期等多条件组合筛选
3. **采购单生命周期管理**：从新增、编辑到审核、入库的完整流程管理
4. **审核入库流程**：采购单审核后自动生成入库记录，更新库存数据
5. **物流跟踪管理**：集成物流信息查询，支持运输进度和签收状态跟踪
6. **数据统计分析**：实时统计采购金额、实付金额、未入库金额等关键指标

## 3. 详细功能设计

### 3.1 多视图展示功能

#### 3.1.1 顶部导航切换
**功能描述**: 提供四种不同维度的采购数据视图

**视图类型**:
- **采购单视图**: 默认主视图，显示采购单据列表
  - 按采购单维度展示数据
  - 支持完整的采购单操作
  - 提供采购单生命周期管理
- **商品汇总视图**: 按商品统计采购数据
  - 统计每个商品的采购总量
  - 显示商品采购总金额
  - 便于采购决策分析
- **商品记录视图**: 显示每笔采购记录
  - 细颗粒度的采购记录
  - 支持采购历史追溯
  - 便于采购明细查询
- **入库单视图**: 已入库商品明细
  - 显示入库单据信息
  - 支持库存流转追踪
  - 便于入库状态管理

**交互特性**:
- **Tab切换**: 使用标签页形式切换视图
- **状态同步**: 保持各视图间的筛选条件同步
- **缓存机制**: 缓存当前查询条件，提升用户体验

#### 3.1.2 视图数据联动
**功能描述**: 各视图间的数据关联和联动展示

**数据关联**:
- **采购单→商品汇总**: 采购单数据聚合为商品汇总
- **采购单→商品记录**: 采购单拆分为商品记录明细
- **采购单→入库单**: 审核后的采购单生成入库单
- **跨视图查询**: 支持从一个视图跳转到相关视图

### 3.2 筛选查询功能

#### 3.2.1 多维度筛选条件
**功能描述**: 提供全面的采购数据筛选功能

**筛选字段**:
- **供应商筛选**: 按供应商进行筛选
  - 下拉选择供应商
  - 支持供应商名称搜索
  - 显示每个供应商的采购数量
- **经手人筛选**: 按采购经手人筛选
  - 下拉选择经手人员
  - 支持经手人姓名搜索
  - 显示每个经手人的采购单数量
- **结算账户筛选**: 按财务结算账户筛选
  - 选择结算账户类型
  - 支持多账户筛选
  - 便于财务核算分析
- **状态筛选**: 按采购单状态筛选
  - 未审核：待审核的采购单
  - 已审核：已通过审核的采购单
  - 已入库：已完成入库的采购单
  - 已取消：已取消的采购单

**查询功能**:
- **单号查询**: 采购单号的精确查询
  - 支持完整单号搜索
  - 支持单号模糊匹配
  - 支持批量单号查询
- **日期范围查询**: 采购日期范围筛选
  - 起始日期选择器
  - 结束日期选择器
  - 快速日期选择（今天、本周、本月、本年）

#### 3.2.2 筛选操作功能
**功能描述**: 筛选相关的操作和管理功能

**操作按钮**:
- **查询按钮**: 执行筛选条件查询
  - 组合所有筛选条件
  - 实时更新数据表格
  - 更新统计数据
- **清空按钮**: 清除所有筛选条件
  - 一键重置所有筛选
  - 恢复默认数据展示
  - 清除缓存的筛选条件
- **保存筛选**: 保存常用筛选条件
  - 自定义筛选方案
  - 快速应用保存的筛选
  - 筛选方案管理

### 3.3 采购单管理功能

#### 3.3.1 采购单新增功能
**功能描述**: 创建新的采购入库单据，支持多种商品录入方式和智能化的操作流程

**页面入口**:
- **主要入口**: 采购入库模块 → 点击【新增】按钮
- **智能入口**: 库存预警模块 → 点击【去采购】后跳转（自动带入预警商品）
- **快速入口**: 商品管理相关页面的快速采购链接

**功能架构**:
采购入库新增页面包含四个核心功能区域：
- **基础信息管理**: 采购单元数据的录入和管理
- **商品选择录入**: 多种方式的商品选择和信息录入
- **明细信息管理**: 已选商品的明细展示和编辑
- **操作流程控制**: 采购单的保存、提交和流程控制

**基础信息管理**:
- **门店信息**: 下拉选择采购归属组织/部门
  - 手动选择门店
  - 权限控制可选门店范围
  - 影响后续仓库选择范围
- **仓库信息**: 下拉选择商品最终入库位置
  - 基于门店筛选可选仓库
  - 显示仓库当前状态
  - 支持仓库容量提醒
- **采购单号**: 采购单的唯一标识
  - 系统自动生成（推荐）
  - 支持手动修改
  - 唯一性验证
- **采购日期**: 单据创建时间
  - 默认当前日期
  - 支持手动调整
  - 日期格式验证

**供应商信息管理**:
- **供应商选择**: 弹窗选择供应商
  - 供应商列表展示
  - 支持搜索和筛选
  - 显示供应商基本信息
- **联动信息自动填充**:
  - 负责人姓名自动带出
  - 联系电话自动填充
  - 欠款信息自动计算显示
- **结算信息**:
  - 结算账户下拉选择
  - 实付金额录入（默认0）
  - 支持多种结算方式

**商品选择录入功能**:
- **扫码录入功能**: 通过扫描商品条码快速录入商品
  - 扫码输入框：页面右上角扫码录入区域
  - 条码识别：自动识别商品条码信息
  - 商品匹配：匹配系统中的商品信息
  - 快速添加：识别成功后自动添加到明细表格
  - 错误处理：识别失败时提供友好提示
- **手动选择功能**: 从商品列表中手动选择商品
  - 商品选择器：右侧商品选择面板
  - 商品展示：图片 + 编码 + 品名 + SN标识 + 当前库存
  - 搜索筛选：支持商品名称、编码搜索
  - 分类筛选：按商品分类筛选
  - 库存参考：显示当前库存数量
  - 点击选择：点击商品后添加到明细表格
- **云单导入功能**: 从云平台或外部系统导入采购单信息
  - 云单选择：选择云端采购单或外部单据
  - 信息预览：导入前预览商品信息
  - 批量导入：一次性导入多个商品
  - 信息校验：导入后验证商品信息完整性
  - 冲突处理：处理重复商品和信息冲突

**明细信息管理**:
- **商品明细展示**:
  - 商品图片：商品缩略图展示
  - 商品编码：系统唯一标识编号
  - 商品名称：带SN标识，提示序列号需求
  - 计量单位：个/台/套等计量单位
  - 当前库存：显示当前库存数量（参考）
  - 上次采购价：显示历史采购价格（参考）
- **采购信息录入**:
  - 采购数量：当前录入采购数量
    - 数值输入验证
    - 非负数校验
    - 支持键盘快速录入
  - 入库位置：指定入库货架或区域
    - 下拉选择库位
    - 支持新建库位
    - 库位容量提醒
  - 商品备注：该商品特定说明
    - 如"展品"、"无包装"等
    - 支持常用备注选择
- **序列号管理**: 管理启用SN控制的商品序列号
  - SN标识显示：商品名称显示SN标识
  - 序列号录入：采购数量与序列号数量匹配
  - 唯一性验证：序列号唯一性检查
  - 批量录入：支持批量序列号录入

**数据验证功能**:
- **基础数据验证**:
  - 必填字段验证：门店、仓库、供应商等必填
  - 格式验证：日期格式、金额格式等
  - 业务规则验证：采购数量非负、重复商品检查
  - 权限验证：操作权限和数据权限验证
- **商品信息验证**:
  - 商品存在性：验证商品在系统中存在
  - 商品状态：验证商品启用状态
  - 数量合理性：验证采购数量合理性
  - 重复检查：同一商品不重复添加
  - SN匹配：序列号数量与采购数量匹配

**操作流程控制**:
- **保存功能**: 保存采购单草稿
  - 数据验证：基础数据完整性验证
  - 草稿保存：保存为草稿状态
  - 自动保存：定时自动保存功能
  - 保存提示：保存成功/失败提示
- **提交功能**: 正式提交采购单
  - 完整性验证：所有必填信息完整性检查
  - 业务规则验证：业务逻辑规则验证
  - 采购单生成：生成正式采购单号
  - 状态更新：更新采购单状态
  - 通知发送：发送提交成功通知
- **取消功能**: 取消当前采购单编辑
  - 确认提示：确认是否取消当前操作
  - 数据清空：清空页面编辑数据
  - 返回列表：返回采购入库列表页面

#### 3.3.2 采购单编辑功能
**功能描述**: 编辑未审核的采购单据

**编辑权限**:
- **状态限制**: 仅未审核状态可编辑
- **权限控制**: 需要采购编辑权限
- **经手人限制**: 主要允许经手人编辑

**编辑内容**:
- **供应商变更**: 修改供应商信息
- **商品调整**: 增删改采购商品
- **数量价格**: 调整采购数量和单价
- **仓库变更**: 修改入库仓库
- **备注更新**: 更新备注和说明信息

### 3.4 审核入库流程

#### 3.4.1 采购单审核功能
**功能描述**: 采购单的审核和批准流程

**审核流程**:
- **审核权限验证**: 检查用户审核权限
- **单据状态检查**: 确认单据可审核状态
- **审核信息录入**:
  - 审核意见填写
  - 审核结果选择（通过/拒绝）
  - 审核时间记录
- **审核结果处理**:
  - 通过：更新状态为已审核
  - 拒绝：返回未审核状态，记录拒绝原因

#### 3.4.2 自动入库处理
**功能描述**: 审核通过后的自动入库流程

**入库流程**:
- **入库单生成**: 基于采购单自动生成入库单
- **库存数据更新**: 更新商品库存数量
- **成本价格更新**: 更新商品成本价格
- **财务数据同步**: 同步财务系统数据
- **入库通知发送**: 发送入库完成通知

**数据处理**:
- **库存增加**: 按采购数量增加库存
- **成本计算**: 计算加权平均成本
- **批次管理**: 生成商品批次信息
- **序列号管理**: 处理商品序列号信息

### 3.5 物流跟踪管理

#### 3.5.1 物流信息查询
**功能描述**: 查询和跟踪采购商品的物流信息

**物流信息**:
- **快递单号**: 显示快递跟踪单号
- **物流公司**: 显示承运的物流公司
- **发货时间**: 供应商发货时间
- **预计到达**: 预计到达时间
- **当前状态**: 当前物流状态
- **运输轨迹**: 详细的运输轨迹信息

#### 3.5.2 物流状态跟踪
**功能描述**: 实时跟踪物流状态和进度

**状态类型**:
- **已发货**: 供应商已发货
- **运输中**: 商品在运输途中
- **到达目的地**: 商品已到达
- **已签收**: 商品已签收确认
- **异常**: 物流异常情况

**跟踪功能**:
- **自动更新**: 定时自动更新物流状态
- **异常提醒**: 物流异常情况提醒
- **签收确认**: 签收状态确认
- **收货依据**: 作为审核入库的依据

### 3.6 数据统计分析

#### 3.6.1 统计数据展示
**功能描述**: 实时展示采购相关的统计数据

**统计指标**:
- **采购金额**: 所有采购单的金额总和
  - 按筛选条件统计
  - 实时数据更新
  - 支持币种转换
- **实付金额**: 实际支付的金额总和
  - 已付款的采购金额
  - 财务确认的金额
  - 支付状态统计
- **未入库金额**: 尚未入库的采购金额
  - 未审核采购单金额
  - 审核中采购单金额
  - 提醒处理的金额

#### 3.6.2 数据分析功能
**功能描述**: 提供采购数据的分析和决策支持

**分析维度**:
- **供应商分析**: 按供应商统计采购数据
- **商品分析**: 按商品类别统计采购情况
- **时间分析**: 按时间维度分析采购趋势
- **成本分析**: 采购成本和价格趋势分析

**决策支持**:
- **采购节奏**: 分析当前采购节奏
- **资金支出**: 监控采购资金支出情况
- **流程效率**: 分析采购流程效率
- **异常识别**: 识别采购流程中的异常情况

## 4. 用户界面设计

### 4.1 页面布局设计
- **顶部导航区**: 面包屑导航和模块切换标签
- **筛选操作区**: 多维度筛选条件和操作按钮
- **数据统计区**: 关键采购指标统计展示
- **数据表格区**: 采购单据数据表格展示
- **行级操作区**: 每条记录的操作按钮

### 4.2 交互设计规范
- **Tab切换**: 清晰的标签页切换设计
- **筛选面板**: 收起/展开的筛选面板
- **状态标识**: 直观的状态颜色和图标
- **操作确认**: 重要操作的确认弹窗

### 4.3 响应式设计
- **PC端**: 完整功能展示，多列表格布局
- **平板端**: 适配中等屏幕，关键信息优先
- **移动端**: 卡片式布局，核心功能保留

## 5. 权限控制

### 5.1 功能权限
- **查看权限**: 采购数据查看权限
- **新增权限**: 采购单新增权限
- **编辑权限**: 采购单编辑权限
- **审核权限**: 采购单审核权限
- **删除权限**: 采购单删除权限

### 5.2 数据权限
- **门店权限**: 只能操作本门店采购数据
- **供应商权限**: 按供应商范围控制访问
- **金额权限**: 大额采购需要特殊权限

## 6. 异常处理

### 6.1 业务异常
- **审核异常**: 审核流程异常的处理
- **入库异常**: 自动入库失败的处理
- **物流异常**: 物流信息异常的处理

### 6.2 系统异常
- **数据异常**: 采购数据异常的检测和修复
- **同步异常**: 数据同步失败的重试机制
- **权限异常**: 权限验证失败的处理

---

**文档版本**: v1.0
**编写日期**: 2025-07-02
**编写人员**: AI系统架构师
**审核状态**: 待审核
