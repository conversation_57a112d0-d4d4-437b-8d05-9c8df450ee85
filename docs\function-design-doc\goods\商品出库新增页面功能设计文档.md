# 商品管理模块 - 商品出库新增页面功能设计文档

## 1. 页面概述

### 1.1 页面目的
商品出库新增页面是智能家装管理平台商品管理系统的核心业务页面，负责创建新的出库单据，支持多种出库类型、往来单位管理、商品选择和出库信息录入等完整的出库单创建流程。该页面通过规范化的出库流程，确保库存数据的准确性和业务操作的可追溯性。

### 1.2 业务价值
- 提供标准化的出库单创建功能，支持销售、调拨、借用等多种业务场景
- 实现出库信息的完整录入，确保出库数据的准确性和可追溯性
- 支持多种商品选择方式，满足不同场景下的出库需求
- 建立出库与库存的实时联动，确保库存数据的准确扣减
- 提供完整的出库流程控制，规范出库业务操作

### 1.3 页面入口
- **主要入口**：商品出库模块 → 点击【新增】按钮
- **快速入口**：库存管理相关页面的快速出库链接
- **业务入口**：销售订单、调拨申请等业务单据的出库操作

### 1.4 功能架构
商品出库新增页面包含四个核心功能区域：
- **基础信息管理**：出库单基础元数据的录入和管理
- **往来单位管理**：客户、供应商等往来单位的选择和信息联动
- **商品选择管理**：商品的选择、展示和出库信息录入
- **操作流程控制**：出库单的保存、提交和流程控制

## 2. 商品出库新增页面操作流程图

```mermaid
flowchart TD
    A[用户访问商品出库新增页面] --> B[权限验证]
    B --> C{权限验证通过?}
    C -->|否| D[显示权限不足提示]
    C -->|是| E[页面初始化]
    
    E --> F[加载基础信息区]
    E --> G[加载商品选择器]
    E --> H[初始化明细表格]
    E --> I[显示操作按钮]
    
    F --> J[自动填充默认信息]
    J --> K[出库单号生成]
    J --> L[当前日期填充]
    J --> M[出库店信息]
    J --> N[经手人信息]
    
    F --> O[往来单位管理]
    O --> P[选择往来单位类型]
    P --> Q[客户类型]
    P --> R[供应商类型]
    P --> S[经销商类型]
    P --> T[工程项目部类型]
    
    Q --> U[限定搜索范围为客户]
    R --> V[限定搜索范围为供应商]
    S --> W[限定搜索范围为经销商]
    T --> X[限定搜索范围为项目部]
    
    U --> Y[点击搜索按钮]
    V --> Y
    W --> Y
    X --> Y
    
    Y --> Z[弹出往来单位选择器]
    Z --> AA[显示过滤后的单位列表]
    AA --> BB[单位信息展示]
    BB --> CC[单位名称]
    BB --> DD[联系人信息]
    BB --> EE[联系电话]
    
    AA --> FF[选择具体往来单位]
    FF --> GG[自动填充联动信息]
    GG --> HH[名称自动填充]
    GG --> II[联系电话自动填充]
    
    F --> JJ[出库类型选择]
    JJ --> KK[销售出库]
    JJ --> LL[调拨出库]
    JJ --> MM[借用出库]
    JJ --> NN[返厂出库]
    
    KK --> OO[关联销售单据]
    LL --> PP[指定入库店]
    MM --> QQ[设置预计归还时间]
    NN --> RR[生成退换货任务]
    
    G --> SS[商品选择功能]
    SS --> TT[商品列表展示]
    TT --> UU[商品缩略图]
    TT --> VV[商品编号品名]
    TT --> WW[计量单位]
    TT --> XX[当前库存]
    
    SS --> YY[商品搜索功能]
    YY --> ZZ[扫码搜索]
    YY --> AAA[手动搜索]
    
    ZZ --> BBB[扫码枪输入]
    BBB --> CCC[商品识别]
    CCC --> DDD{识别成功?}
    DDD -->|否| EEE[显示识别失败提示]
    DDD -->|是| FFF[添加到明细表格]
    
    AAA --> GGG[商品名称编码搜索]
    GGG --> HHH[搜索结果展示]
    HHH --> III[点击选择商品]
    III --> FFF
    
    TT --> JJJ[点击选择商品]
    JJJ --> FFF
    
    FFF --> KKK[明细表格更新]
    KKK --> LLL[商品信息展示]
    LLL --> MMM[商品图片]
    LLL --> NNN[商品编码]
    LLL --> OOO[商品品名]
    LLL --> PPP[型号规格]
    LLL --> QQQ[计量单位]
    
    KKK --> RRR[出库信息录入]
    RRR --> SSS[出库数量输入]
    RRR --> TTT[商品备注录入]
    
    SSS --> UUU{出库数量验证}
    UUU -->|数量≤0| VVV[显示数量错误提示]
    UUU -->|数量>库存| WWW[显示库存不足提示]
    UUU -->|数量合理| XXX[数量验证通过]
    
    I --> YYY[操作按钮功能]
    YYY --> ZZZ[返回操作]
    YYY --> AAAA[取消操作]
    YYY --> BBBB[保存操作]
    
    ZZZ --> CCCC[返回出库单列表]
    
    AAAA --> DDDD{确认取消操作?}
    DDDD -->|否| EEEE[继续编辑]
    DDDD -->|是| FFFF[清空页面数据]
    FFFF --> CCCC
    
    BBBB --> GGGG{数据完整性验证}
    GGGG -->|验证失败| HHHH[显示验证错误提示]
    GGGG -->|验证通过| IIII[保存出库单]
    IIII --> JJJJ[生成出库单号]
    JJJJ --> KKKK[更新出库状态]
    KKKK --> LLLL[库存预扣减]
    LLLL --> MMMM[发送保存通知]
    MMMM --> CCCC
    
    style A fill:#e1f5fe
    style C fill:#fff3e0
    style D fill:#ffebee
    style DDD fill:#fff3e0
    style EEE fill:#ffebee
    style UUU fill:#fff3e0
    style VVV fill:#ffebee
    style WWW fill:#fff8e1
    style DDDD fill:#fff3e0
    style GGGG fill:#fff3e0
    style HHHH fill:#ffebee
    style MMMM fill:#e8f5e8
```

### 流程说明
商品出库新增页面的操作流程主要包含以下几个核心环节：

1. **权限验证与页面初始化**：验证用户出库权限，初始化页面各功能区域
2. **往来单位选择流程**：选择单位类型→限定搜索范围→选择具体单位→信息自动联动
3. **出库类型业务处理**：根据不同出库类型执行相应的业务逻辑
4. **商品选择录入**：支持扫码和手动选择，验证库存可用性
5. **出库信息验证**：验证出库数量、库存充足性等业务规则
6. **出库单保存提交**：完整性验证后保存出库单并执行库存预扣减

## 3. 详细功能设计

### 3.1 基础信息管理功能

#### 3.1.1 出库单基础信息
**功能描述**：管理出库单的基础元数据信息

**基础信息字段**：
- **出库单号**：系统生成唯一出库单编号
  - 自动生成（如：CK...）
  - 唯一性保证
  - 支持自定义编号规则
- **出库日期**：当前出库日期
  - 默认当天日期
  - 支持手动调整
  - 日期格式验证
- **出库店**：当前门店或仓库名称
  - 系统自动带出
  - 基于用户权限确定
  - 影响库存扣减范围
- **经手人**：执行出库动作的用户
  - 默认当前账号
  - 支持下拉选择其他用户
  - 权限控制可选范围

#### 3.1.2 出库类型管理
**功能描述**：管理不同的出库业务类型

**出库类型**：
- **销售出库**：正常销售业务出库
  - 可关联销售单据
  - 需要结算金额
  - 影响销售收入统计
- **调拨出库**：仓库间调拨出库
  - 需指定入库店
  - 不影响总库存
  - 生成调拨记录
- **借用出库**：临时借用出库
  - 需设置预计归还时间
  - 生成借用记录
  - 支持归还跟踪
- **返厂出库**：退换货返厂出库
  - 出库后生成退换货任务
  - 关联售后服务
  - 支持质量追溯

#### 3.1.3 结算信息管理
**功能描述**：管理出库相关的财务结算信息

**结算信息**：
- **结算账户**：关联财务账户
  - 下拉选择账户
  - 如银行对公、现金等
  - 涉及收款时使用
- **备注信息**：自定义出库说明
  - 多行文本输入
  - 可输入发货原因等
  - 支持常用备注模板

### 3.2 往来单位管理功能

#### 3.2.1 往来单位类型选择
**功能描述**：选择往来单位的类型，控制搜索范围

**单位类型**：
- **客户**：销售业务的客户单位
- **供应商**：采购退货等业务的供应商
- **经销商**：渠道业务的经销商
- **工程项目部**：工程项目相关的内部单位

**类型控制**：
- 选择类型后限定搜索范围
- 不同类型显示不同的单位列表
- 类型与出库类型联动验证

#### 3.2.2 往来单位搜索选择
**功能描述**：搜索和选择具体的往来单位

**搜索功能**：
- **弹窗选择器**：点击搜索弹出单位选择器
- **过滤显示**：基于选择的类型过滤单位列表
- **搜索功能**：支持单位名称、编码搜索
- **信息展示**：显示单位名称、联系人、电话等信息

#### 3.2.3 信息自动联动
**功能描述**：选择往来单位后自动填充相关信息

**联动信息**：
- **名称自动填充**：自动填充往来单位名称
- **联系电话自动填充**：自动填充主联系人电话
- **地址信息联动**：自动填充单位地址信息
- **历史记录关联**：显示历史出库记录

### 3.3 商品选择管理功能

#### 3.3.1 商品选择器展示
**功能描述**：右侧商品选择器的展示和交互

**展示内容**：
- **商品缩略图**：快速识别商品视觉形态
- **编号品名**：商品编码 + 中文名称（支持SN提示）
- **计量单位**：如"个"、"台"等计量单位
- **当前库存**：当前库存数量（重要参考）
- **选择操作**：点击商品后加入明细表

#### 3.3.2 商品搜索功能
**功能描述**：多种方式的商品搜索和定位

**搜索方式**：
- **扫码搜索**：支持扫码枪快速定位商品
  - 条码识别
  - 自动匹配商品
  - 快速添加到明细
- **手动搜索**：商品名称、编码搜索
  - 模糊匹配
  - 实时搜索建议
  - 搜索结果高亮

#### 3.3.3 库存可用性检查
**功能描述**：检查商品库存的可用性

**检查内容**：
- **库存数量显示**：实时显示当前库存
- **可用库存计算**：扣除已预占库存
- **库存不足提醒**：库存不足时的提醒
- **库存预警**：接近安全库存的预警

### 3.4 商品明细管理功能

#### 3.4.1 明细信息展示
**功能描述**：展示已选商品的详细信息

**明细字段**：
- **商品图片**：商品图标展示
- **商品编码**：唯一标识码
- **商品品名**：展示品名（含SN提示）
- **型号规格**：商品型号和规格属性
- **计量单位**：计量单位显示
- **当前库存**：库存数量参考

#### 3.4.2 出库信息录入
**功能描述**：录入商品的出库相关信息

**录入信息**：
- **出库数量**：用户需填写的核心字段
  - 默认为0，必须填写
  - 数值验证（非负数）
  - 库存充足性验证
  - 决定库存扣减数量
- **商品备注**：每件商品独立备注
  - 如"展示样品"、"配件缺失"
  - 支持常用备注选择
  - 便于出库后追溯

#### 3.4.3 序列号管理
**功能描述**：管理启用SN控制的商品序列号

**SN管理**：
- **SN标识显示**：商品品名显示SN提示
- **序列号录入**：出库数量与序列号数量匹配
- **序列号选择**：从可用序列号中选择
- **唯一性验证**：确保序列号不重复使用

### 3.5 数据验证功能

#### 3.5.1 基础数据验证
**功能描述**：验证出库单基础信息的完整性

**验证规则**：
- **必填字段验证**：往来单位、出库类型等必填
- **格式验证**：日期格式、数量格式等
- **业务规则验证**：出库类型与往来单位类型匹配
- **权限验证**：操作权限和数据权限验证

#### 3.5.2 库存验证功能
**功能描述**：验证库存的充足性和可用性

**验证内容**：
- **库存充足性**：出库数量不超过可用库存
- **库存状态**：验证商品库存状态正常
- **预占库存**：考虑已预占的库存数量
- **安全库存**：检查是否低于安全库存

### 3.6 操作流程控制

#### 3.6.1 保存功能
**功能描述**：保存出库单并执行相关业务逻辑

**保存流程**：
- **数据完整性验证**：验证所有必填信息
- **业务规则验证**：验证业务逻辑规则
- **出库单生成**：生成正式出库单号
- **库存预扣减**：预扣减库存数量
- **状态更新**：更新出库单状态

#### 3.6.2 取消功能
**功能描述**：取消当前出库单编辑

**取消流程**：
- **确认提示**：确认是否取消当前操作
- **数据清空**：清空页面编辑数据
- **返回列表**：返回商品出库列表页面

#### 3.6.3 返回功能
**功能描述**：返回出库单列表页面

**返回处理**：
- **数据保护**：提醒保存未提交的数据
- **页面跳转**：返回出库单列表页面
- **状态恢复**：恢复列表页面状态

## 4. 用户界面设计

### 4.1 页面布局设计
- **顶部基础信息区**：出库单基础信息和往来单位信息
- **右侧商品选择器**：商品列表展示和选择功能
- **中下明细表格区**：已选商品明细展示和编辑
- **底部操作按钮区**：返回、取消、保存等操作

### 4.2 交互设计规范
- **智能联动**：往来单位选择后信息自动填充
- **实时验证**：出库数量输入时实时验证库存
- **友好提示**：清晰的错误提示和操作指导
- **快捷操作**：支持扫码快速选择商品

### 4.3 响应式设计
- **PC端**：完整功能展示，多区域布局
- **平板端**：适配中等屏幕，关键功能优先
- **移动端**：简化布局，核心功能保留

## 5. 权限控制

### 5.1 功能权限
- **页面访问权限**：商品出库新增页面访问权限
- **商品选择权限**：商品选择和出库权限
- **往来单位权限**：往来单位查看和选择权限
- **出库单保存权限**：出库单保存和提交权限

### 5.2 数据权限
- **门店权限**：只能为有权限的门店创建出库单
- **商品权限**：只能出库有权限的商品类别
- **往来单位权限**：只能选择有业务关系的往来单位
- **库存权限**：只能查看和操作有权限的库存

## 6. 异常处理

### 6.1 业务异常
- **库存不足**：出库数量超过可用库存的处理
- **商品识别失败**：扫码或搜索商品失败的处理
- **数据验证失败**：输入数据不符合业务规则的处理
- **往来单位异常**：往来单位信息异常的处理

### 6.2 系统异常
- **网络异常**：网络连接异常的处理和重试
- **数据保存失败**：数据保存失败的处理和恢复
- **权限异常**：权限验证失败的友好提示
- **系统错误**：系统错误的异常处理和用户提示

---

**文档版本**：v1.0  
**编写日期**：2025-07-02  
**编写人员**：AI系统架构师  
**审核状态**：待审核
