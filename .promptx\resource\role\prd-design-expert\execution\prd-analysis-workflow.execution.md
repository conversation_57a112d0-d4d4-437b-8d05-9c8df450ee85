<execution>
<constraint>
## 基于学习文档的严格约束
- **document-analyzer强制使用**：检测到PRD文档时必须使用document-analyzer分析文档结构
- **优先级识别要求**：必须识别P0/P1/P2优先级标记并据此排序功能点
- **功能点ID格式**：必须使用[项目代号]-REQ-[序号]格式生成唯一ID
- **可追溯性矩阵强制**：必须建立连接需求与测试用例的可追溯性矩阵
- **文档质量检查**：必须检测文档中的不一致性和模糊表述，提出澄清建议
- **术语映射要求**：必须自动映射业务术语到技术概念
</constraint>

<rule>
## PRD文档分析强制规则
- **结构化分析**：使用document-analyzer分析PRD文档结构
- **关键信息提取**：提取功能点、用户故事、业务规则
- **优先级排序**：识别P0/P1/P2标记并排序
- **质量检查**：检测不一致性和模糊表述
- **术语标准化**：建立业务术语到技术概念的映射
- **变更记录**：生成需求变更记录
</rule>

<guideline>
## PRD分析指导原则
- **完整性优先**：确保所有关键信息都被提取
- **准确性保证**：验证提取信息的准确性
- **标准化处理**：使用统一的格式和术语
- **可追溯性建立**：建立需求间的关联关系
- **质量导向**：主动识别和解决质量问题
- **协作支持**：提供清晰的澄清建议
</guideline>

<process>
## PRD文档分析完整流程

### Step 1: PRD文档识别与预处理 (20%)
```mermaid
flowchart TD
    A[接收PRD文档] --> B[文档格式检查]
    B --> C[document-analyzer启动]
    C --> D[文档结构识别]
    D --> E[初步质量评估]
    E --> F[预处理完成]
```

**执行要点**：
- 检查文档格式是否符合标准
- 启动document-analyzer进行结构分析
- 识别文档的基本结构和组织方式
- 评估文档的基本质量和完整性

### Step 2: 关键信息提取 (30%)
```mermaid
graph TD
    A[文档内容分析] --> B[功能点识别]
    A --> C[用户故事提取]
    A --> D[业务规则抽取]
    A --> E[优先级标记识别]
    
    B --> F[功能点分类]
    C --> G[用户故事标准化]
    D --> H[业务规则结构化]
    E --> I[优先级排序]
    
    F --> J[信息整合]
    G --> J
    H --> J
    I --> J
```

**关键信息提取策略**：
- **功能点识别**：
  ```
  识别模式：
  - "系统应该..."
  - "用户可以..."
  - "功能包括..."
  
  提取结果：
  - 功能名称
  - 功能描述
  - 输入输出
  - 约束条件
  ```

- **用户故事提取**：
  ```
  标准格式：
  作为[用户角色]，
  我希望[功能目标]，
  以便[业务价值]。
  
  验收标准：
  - 给定[前提条件]
  - 当[操作行为]
  - 那么[预期结果]
  ```

- **业务规则抽取**：
  ```
  规则类型：
  - 数据验证规则
  - 业务流程规则
  - 权限控制规则
  - 计算逻辑规则
  ```

### Step 3: 优先级分析与排序 (20%)
```mermaid
graph LR
    A[优先级标记识别] --> B[P0关键功能]
    A --> C[P1重要功能]
    A --> D[P2一般功能]
    
    B --> E[功能依赖分析]
    C --> E
    D --> E
    
    E --> F[实现顺序规划]
    F --> G[优先级矩阵生成]
```

**优先级处理规则**：
- **P0（关键功能）**：系统核心功能，必须实现
- **P1（重要功能）**：主要功能，应该实现
- **P2（一般功能）**：次要功能，可以实现

### Step 4: 质量检查与澄清 (20%)
```mermaid
flowchart TD
    A[质量检查启动] --> B[一致性检查]
    A --> C[完整性检查]
    A --> D[模糊性检查]
    
    B --> E[不一致项识别]
    C --> F[缺失项识别]
    D --> G[模糊表述识别]
    
    E --> H[澄清建议生成]
    F --> H
    G --> H
    
    H --> I[质量报告生成]
```

**质量检查要点**：
- **一致性检查**：
  - 术语使用一致性
  - 功能描述一致性
  - 接口定义一致性
  
- **完整性检查**：
  - 用户角色覆盖
  - 功能流程完整
  - 异常处理覆盖
  
- **模糊性检查**：
  - 需求表述模糊
  - 验收标准不明确
  - 依赖关系不清晰

### Step 5: 术语映射与标准化 (10%)
```mermaid
graph TD
    A[业务术语识别] --> B[技术概念映射]
    B --> C[术语表构建]
    C --> D[标准化处理]
    D --> E[映射关系验证]
    E --> F[术语字典生成]
```

**术语映射示例**：
```markdown
| 业务术语 | 技术概念 | 说明 |
|---------|---------|------|
| 用户登录 | Authentication | 身份验证机制 |
| 订单支付 | Payment Processing | 支付处理流程 |
| 库存管理 | Inventory Management | 库存管理系统 |
```

## 输出文档格式标准

### 分析结果输出格式
```markdown
# PRD文档分析报告

## 文档基本信息
- **文档名称**：[PRD文档标题]
- **分析时间**：[YYYY-MM-DD HH:MM:SS]
- **项目代号**：[项目代号]
- **分析工具**：document-analyzer v1.0

## 关键信息提取结果

### 功能点清单
| 功能ID | 功能名称 | 优先级 | 描述 | 依赖 |
|--------|----------|--------|------|------|
| [项目代号]-REQ-001 | 用户登录 | P0 | 用户通过用户名密码登录系统 | 无 |
| [项目代号]-REQ-002 | 订单创建 | P0 | 用户可以创建新订单 | REQ-001 |

### 用户故事
1. **[项目代号]-REQ-001：用户登录**
   - 作为系统用户，我希望能够登录系统，以便访问个人功能
   - 验收标准：
     - 给定用户输入正确的用户名和密码
     - 当用户点击登录按钮
     - 那么系统应该验证用户身份并跳转到主页

### 业务规则
1. **登录验证规则**
   - 用户名长度4-20个字符
   - 密码长度8-32个字符，包含字母和数字
   - 连续3次登录失败将锁定账户15分钟

## 质量检查结果

### 发现问题
1. **模糊表述**：第3.2节中"快速处理"未明确定义时间要求
2. **缺失信息**：未明确说明用户权限等级划分
3. **不一致表述**：第2.1节和第4.3节对"订单状态"的描述不一致

### 澄清建议
1. 建议明确"快速处理"的具体时间要求（如：2秒内响应）
2. 建议补充用户权限等级的详细说明
3. 建议统一"订单状态"的定义和使用

## 术语映射表
| 业务术语 | 技术概念 | 说明 |
|---------|---------|------|
| 用户登录 | Authentication | 身份验证机制 |
| 订单处理 | Order Processing | 订单处理工作流 |
```
</process>

<criteria>
## PRD文档分析质量标准

### 分析完整性标准
- ✅ 使用document-analyzer完成文档结构分析
- ✅ 提取所有关键功能点、用户故事、业务规则
- ✅ 识别并处理所有优先级标记(P0/P1/P2)
- ✅ 建立完整的可追溯性矩阵

### 质量保证标准
- ✅ 检测并标记所有不一致性问题
- ✅ 识别并标记所有模糊表述
- ✅ 提供具体的澄清建议
- ✅ 建立标准化的术语映射

### 输出标准化标准
- ✅ 功能点ID符合[项目代号]-REQ-[序号]格式
- ✅ 用户故事符合标准格式
- ✅ 业务规则结构化表述
- ✅ 分析报告格式规范

### 可操作性标准
- ✅ 分析结果能够直接用于需求分析文档生成
- ✅ 质量问题和澄清建议具体可行
- ✅ 术语映射支持后续技术设计
- ✅ 可追溯性矩阵支持项目管理
</criteria>
</execution>