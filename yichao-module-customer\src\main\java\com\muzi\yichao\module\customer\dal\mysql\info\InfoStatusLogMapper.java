package com.muzi.yichao.module.customer.dal.mysql.info;

import java.util.*;

import com.muzi.yichao.framework.common.pojo.PageResult;
import com.muzi.yichao.framework.common.pojo.PageParam;
import com.muzi.yichao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.muzi.yichao.framework.mybatis.core.mapper.BaseMapperX;
import com.muzi.yichao.module.customer.dal.dataobject.info.InfoStatusLogDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 客户状态变更记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InfoStatusLogMapper extends BaseMapperX<InfoStatusLogDO> {

    default PageResult<InfoStatusLogDO> selectPage(PageParam reqVO, Long customerId) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InfoStatusLogDO>()
            .eq(InfoStatusLogDO::getCustomerId, customerId)
            .orderByDesc(InfoStatusLogDO::getId));
    }

    default int deleteByCustomerId(Long customerId) {
        return delete(InfoStatusLogDO::getCustomerId, customerId);
    }

	default int deleteByCustomerIds(List<Long> customerIds) {
	    return deleteBatch(InfoStatusLogDO::getCustomerId, customerIds);
	}

}
