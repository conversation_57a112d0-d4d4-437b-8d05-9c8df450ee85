# 销售管理模块 - 消费开单页面功能设计文档

## 1. 页面概述

### 1.1 页面目的
消费开单页面是智能家装管理平台销售管理系统的核心业务页面，负责手动开具消费单据，为客户选择商品、记录购买明细、完成结算。该页面通过完整的销售流程管理，实现门店销售、会员消费、积分管理和库存控制的一体化操作。

### 1.2 业务价值
- 提供标准化的门店销售开单功能，规范销售流程和客户服务
- 实现会员卡消费积分系统的完整集成，提升客户粘性和复购率
- 支持多种销售场景，满足现场消费、补录订单、套餐销售等业务需求
- 建立完整的销售记录，确保库存数据准确性和财务核算完整性
- 提供灵活的优惠和结算方式，提升客户购买体验

### 1.3 页面入口
- **主要入口**：销售管理 → 消费开单
- **快速入口**：门店前台销售系统快速开单
- **业务入口**：客户服务、会员管理等业务场景的消费开单

### 1.4 功能架构
消费开单页面包含五个核心功能区域：
- **客户信息管理**：客户身份识别和会员信息展示
- **商品选择管理**：商品搜索、选择和库存展示
- **商品明细管理**：已选商品明细和价格调整
- **订单信息管理**：结算信息和订单细节录入
- **操作流程控制**：提交、挂单、优惠等操作控制

### 1.5 应用场景
- **门店销售前台下单**：门店现场客户购买商品
- **客户现场消费录入**：客户到店消费的实时录入
- **补录线下非系统订单**：补录线下已完成的销售订单
- **关联会员卡消费积分系统**：会员消费积分获取和使用

### 1.6 核心交互逻辑流程
```
客户识别 → 商品选择 → 明细确认 → 优惠设置 → 支付结算（或挂单） → 出库/销售完成
```

## 2. 消费开单页面操作流程图

```mermaid
flowchart TD
    A[用户访问消费开单页面] --> B[权限验证]
    B --> C{权限验证通过?}
    C -->|否| D[显示权限不足提示]
    C -->|是| E[页面初始化]
    
    E --> F[加载客户信息区]
    E --> G[加载商品选择区]
    E --> H[加载商品明细区]
    E --> I[加载订单信息区]
    E --> J[显示操作按钮]
    
    F --> K[客户搜索功能]
    K --> L[输入关键词搜索]
    L --> M[姓名搜索]
    L --> N[手机号搜索]
    L --> O[卡号搜索]
    
    M --> P[客户匹配结果]
    N --> P
    O --> P
    
    P --> Q{找到客户?}
    Q -->|否| R[显示未找到客户提示]
    Q -->|是| S[客户信息自动填充]
    
    S --> T[基本信息填充]
    T --> U[姓名手机微信号]
    T --> V[会员卡号卡种客户类型]
    T --> W[积分余额信息]
    T --> X[旺旺号发票信息]
    T --> Y[上次消费订单数]
    T --> Z[收货地址]
    T --> AA[关联方案]
    
    G --> BB[商品选择功能]
    BB --> CC[商品搜索栏]
    CC --> DD[扫码枪输入]
    CC --> EE[手动关键字搜索]
    
    DD --> FF[商品条码识别]
    FF --> GG{识别成功?}
    GG -->|否| HH[显示识别失败提示]
    GG -->|是| II[添加商品到明细]
    
    EE --> JJ[商品名称编码搜索]
    JJ --> KK[搜索结果展示]
    
    BB --> LL[分类筛选]
    LL --> MM[按商品分类过滤]
    
    BB --> NN[套餐按钮]
    NN --> OO[弹出套餐列表]
    OO --> PP[选择组合商品套餐]
    PP --> QQ[套餐商品批量添加]
    
    BB --> RR[商品展示]
    RR --> SS[商品图片名称]
    RR --> TT[库存价格信息]
    RR --> UU[SN管理标识]
    
    KK --> VV[点击选择商品]
    MM --> VV
    RR --> VV
    
    VV --> WW{库存检查}
    WW -->|库存不足| XX[显示库存不足提示]
    WW -->|库存充足| II
    
    II --> YY[商品明细表格更新]
    YY --> ZZ[商品信息展示]
    ZZ --> AAA[品名单价]
    ZZ --> BBB[折扣数量]
    ZZ --> CCC[合计积分]
    ZZ --> DDD[销售人员]
    
    YY --> EEE[明细编辑功能]
    EEE --> FFF[调整商品数量]
    EEE --> GGG[修改折扣率]
    EEE --> HHH[编辑备注]
    EEE --> III[删除商品]
    
    FFF --> JJJ[数量验证]
    JJJ --> KKK{数量合理?}
    KKK -->|否| LLL[显示数量错误提示]
    KKK -->|是| MMM[更新合计金额]
    
    GGG --> NNN[折扣验证]
    NNN --> OOO{折扣合理?}
    OOO -->|否| PPP[显示折扣错误提示]
    OOO -->|是| MMM
    
    I --> QQQ[订单信息管理]
    QQQ --> RRR[订单号自动生成]
    QQQ --> SSS[订单时间自动填充]
    QQQ --> TTT[平台单号录入]
    QQQ --> UUU[分方案选择]
    QQQ --> VVV[服务费录入]
    QQQ --> WWW[安装费录入]
    
    MMM --> XXX[订单金额自动计算]
    VVV --> XXX
    WWW --> XXX
    XXX --> YYY[实时金额更新]
    
    J --> ZZZ[操作按钮功能]
    ZZZ --> AAAA[导入功能]
    ZZZ --> BBBB[提交结账]
    ZZZ --> CCCC[优惠设置]
    ZZZ --> DDDD[挂单功能]
    ZZZ --> EEEE[取消操作]
    
    AAAA --> FFFF[导入云平台订单]
    FFFF --> GGGG[订单信息回填]
    
    CCCC --> HHHH[优惠操作面板]
    HHHH --> IIII[折扣设置]
    HHHH --> JJJJ[满减优惠]
    HHHH --> KKKK[积分抵扣]
    
    IIII --> LLLL[应用折扣]
    JJJJ --> LLLL
    KKKK --> LLLL
    LLLL --> XXX
    
    DDDD --> MMMM[暂存订单]
    MMMM --> NNNN[保存挂单状态]
    NNNN --> OOOO[挂单管理记录]
    
    BBBB --> PPPP{订单验证}
    PPPP -->|验证失败| QQQQ[显示验证错误提示]
    PPPP -->|验证通过| RRRR[提交销售订单]
    
    RRRR --> SSSS[生成销售记录]
    SSSS --> TTTT[库存自动扣减]
    TTTT --> UUUU[积分自动计算]
    UUUU --> VVVV[会员信息更新]
    VVVV --> WWWW[销售完成通知]
    WWWW --> XXXX[返回销售管理]
    
    EEEE --> YYYY{确认取消操作?}
    YYYY -->|否| ZZZZ[继续编辑]
    YYYY -->|是| AAAAA[清空页面内容]
    AAAAA --> XXXX
    
    style A fill:#e1f5fe
    style C fill:#fff3e0
    style D fill:#ffebee
    style Q fill:#fff3e0
    style R fill:#ffebee
    style GG fill:#fff3e0
    style HH fill:#ffebee
    style WW fill:#fff3e0
    style XX fill:#fff8e1
    style KKK fill:#fff3e0
    style LLL fill:#ffebee
    style OOO fill:#fff3e0
    style PPP fill:#ffebee
    style PPPP fill:#fff3e0
    style QQQQ fill:#ffebee
    style YYYY fill:#fff3e0
    style WWWW fill:#e8f5e8
```

### 流程说明
消费开单页面的操作流程主要包含以下几个核心环节：

1. **权限验证与页面初始化**：验证用户销售权限，初始化页面各功能区域
2. **客户识别与信息填充**：通过多种方式搜索客户，自动填充会员信息
3. **商品选择与库存验证**：多方式商品选择，实时验证库存可用性
4. **商品明细编辑管理**：数量、折扣、备注等明细信息的编辑和验证
5. **订单信息与金额计算**：订单信息录入和金额的实时计算
6. **优惠设置与结算处理**：优惠应用、挂单保存或提交结账

## 3. 详细功能设计

### 3.1 客户信息管理功能

#### 3.1.1 客户搜索识别
**功能描述**：通过多种方式搜索和识别客户身份

**搜索方式**：
- **姓名搜索**：支持客户姓名模糊搜索
- **手机号搜索**：支持手机号精确和模糊搜索
- **卡号搜索**：支持会员卡号精确搜索
- **实时搜索**：输入过程中实时搜索建议

#### 3.1.2 客户信息自动填充
**功能描述**：客户选择后自动填充相关信息

**填充信息**：
- **基本信息**：姓名、手机号、微信号
- **会员信息**：会员卡号、卡种、客户类型
- **财务信息**：积分、余额
- **扩展信息**：旺旺号、发票信息
- **历史信息**：上次消费、订单数
- **地址信息**：收货地址
- **关联方案**：优惠套餐或组合活动方案

#### 3.1.3 会员系统集成
**功能描述**：与会员卡消费积分系统的完整集成

**集成功能**：
- **积分查询**：显示客户当前可用积分
- **余额查询**：显示客户预存余额
- **消费历史**：查询客户消费历史记录
- **会员权益**：显示会员等级和专享权益

### 3.2 商品选择管理功能

#### 3.2.1 商品搜索功能
**功能描述**：多种方式的商品搜索和定位

**搜索方式**：
- **扫码搜索**：支持扫码枪输入商品条码
  - 条码识别
  - 自动匹配商品
  - 快速添加到明细
- **手动搜索**：商品名称、编码关键字搜索
  - 模糊匹配
  - 实时搜索建议
  - 搜索结果高亮

#### 3.2.2 商品分类筛选
**功能描述**：按商品分类快速过滤

**筛选功能**：
- **分类导航**：商品分类树形导航
- **快速筛选**：常用分类快速筛选
- **组合筛选**：分类与搜索的组合筛选

#### 3.2.3 套餐商品管理
**功能描述**：组合商品套餐的选择和管理

**套餐功能**：
- **套餐列表**：弹出所有组合商品销售的套餐列表
- **套餐详情**：显示套餐包含的商品明细
- **批量添加**：套餐商品批量添加到明细
- **套餐优惠**：套餐专享优惠价格

#### 3.2.4 商品信息展示
**功能描述**：商品的详细信息展示

**展示内容**：
- **商品图片**：商品缩略图展示
- **商品名称**：商品名称和规格
- **库存信息**：当前门店可售库存
- **价格信息**：商品售价
- **SN管理标识**：是否启用序列号管理

### 3.3 商品明细管理功能

#### 3.3.1 明细信息展示
**功能描述**：已选商品的明细信息展示

**明细字段**：
- **品名**：商品名称（可编辑备注）
- **单价**：系统默认售价
- **折扣**：可输入折扣率或修改价格
- **数量**：默认1，可手动调整
- **合计**：单价 × 数量 × 折扣后金额
- **积分**：本次购买可获取或使用积分
- **销售人员**：关联负责导购或开单人员
- **操作**：可删除某行商品

#### 3.3.2 明细编辑功能
**功能描述**：商品明细的编辑和调整

**编辑功能**：
- **数量调整**：手动调整商品数量
  - 数量验证（正整数）
  - 库存充足性验证
  - 实时更新合计金额
- **折扣设置**：设置商品折扣
  - 折扣率输入（0-100%）
  - 直接价格修改
  - 折扣权限验证
- **备注编辑**：编辑商品备注信息
- **删除操作**：删除不需要的商品

#### 3.3.3 金额自动计算
**功能描述**：商品明细金额的自动计算

**计算逻辑**：
- **单行合计**：单价 × 数量 × 折扣率
- **总金额**：所有商品合计 + 服务费 + 安装费
- **实时更新**：任何变更都实时更新金额
- **积分计算**：根据消费金额自动计算积分

### 3.4 订单信息管理功能

#### 3.4.1 订单基础信息
**功能描述**：管理订单的基础信息

**基础信息**：
- **订单号**：自动生成订单编号（如：1751469012012716）
- **订单时间**：自动填写当前开单时间，可修改
- **平台单号**：用于绑定外部订单编号（如淘宝、抖音订单号）
- **分方案**：若选择了关联促销方案，则可在此绑定

#### 3.4.2 费用信息管理
**功能描述**：管理订单相关的费用信息

**费用类型**：
- **服务费**：线下装配、配送等服务相关费用
- **安装费**：安装类商品产生的费用字段
- **订单金额**：自动汇总商品合计+附加费用（实时计算）

### 3.5 优惠设置功能

#### 3.5.1 优惠操作面板
**功能描述**：打开折扣/满减/积分抵扣等优惠操作面板

**优惠类型**：
- **折扣设置**：整单折扣或单品折扣
- **满减优惠**：满额减免优惠
- **积分抵扣**：使用客户积分抵扣金额
- **会员优惠**：会员专享优惠

#### 3.5.2 优惠计算逻辑
**功能描述**：优惠的计算和应用逻辑

**计算规则**：
- **优惠叠加**：多种优惠的叠加规则
- **优惠限制**：优惠使用的限制条件
- **最优选择**：自动选择最优优惠方案

### 3.6 操作流程控制

#### 3.6.1 提交结账功能
**功能描述**：提交并完成销售

**结账流程**：
1. **订单验证**：验证订单信息完整性
2. **库存验证**：验证商品库存充足性
3. **销售记录生成**：生成销售记录
4. **库存扣减**：自动扣减商品库存
5. **积分计算**：自动计算和更新客户积分
6. **会员信息更新**：更新会员消费记录

#### 3.6.2 挂单功能
**功能描述**：暂存订单，稍后结账

**挂单功能**：
- **订单暂存**：保存当前订单状态
- **挂单管理**：在"挂单管理"中恢复
- **适用场景**：客户临时不付款或操作中断的场景

#### 3.6.3 导入功能
**功能描述**：导入云平台的订单信息

**导入功能**：
- **云平台对接**：与云平台订单系统对接
- **订单信息回填**：自动回填订单信息
- **数据验证**：验证导入数据的完整性

#### 3.6.4 取消操作功能
**功能描述**：放弃当前订单，清空页面内容

**取消流程**：
- **确认提示**：确认是否取消当前操作
- **数据清空**：清空页面所有编辑内容
- **状态重置**：重置页面到初始状态

## 4. 用户界面设计

### 4.1 页面布局设计
- **客户信息区**：页面左上，客户搜索和信息展示
- **商品选择区**：页面右上，商品搜索和选择
- **商品明细区**：页面中部，已选商品明细表格
- **订单信息区**：页面右下，结算信息和订单细节
- **操作按钮区**：页面底部，各种操作按钮

### 4.2 交互设计规范
- **智能填充**：客户选择后信息自动填充
- **实时计算**：金额和积分的实时计算更新
- **友好提示**：清晰的错误提示和操作指导
- **快捷操作**：支持扫码和快捷键操作

### 4.3 响应式设计
- **PC端**：完整功能展示，多区域布局
- **平板端**：适配中等屏幕，关键功能优先
- **移动端**：简化布局，核心功能保留

## 5. 权限控制

### 5.1 功能权限
- **页面访问权限**：消费开单页面访问权限
- **销售操作权限**：商品销售操作权限
- **折扣设置权限**：商品折扣设置权限
- **挂单管理权限**：挂单操作权限

### 5.2 数据权限
- **客户权限**：只能为有权限的客户开单
- **商品权限**：只能销售有权限的商品类别
- **价格权限**：商品价格查看和修改权限

## 6. 异常处理

### 6.1 业务异常
- **库存不足**：商品库存不足的处理
- **客户异常**：客户信息异常的处理
- **价格异常**：商品价格异常的处理
- **积分异常**：积分计算异常的处理

### 6.2 系统异常
- **网络异常**：网络连接异常的处理和重试
- **数据保存失败**：数据保存失败的处理和恢复
- **权限异常**：权限验证失败的友好提示
- **系统错误**：系统错误的异常处理和用户提示

---

**文档版本**：v1.0  
**编写日期**：2025-07-02  
**编写人员**：AI系统架构师  
**审核状态**：待审核
