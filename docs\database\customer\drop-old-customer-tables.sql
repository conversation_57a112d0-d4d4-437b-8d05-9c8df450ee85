/*
 SaaS智能家装CRM系统 - 客户管理模块旧表删除脚本
 
 用途：删除重命名前的旧表结构
 版本：v1.0
 创建时间：2025-01-19
 数据库：MySQL 8.0+
 
 说明：
 - 本脚本用于删除客户管理模块重命名前的旧表
 - 执行前请确保新表结构已创建并数据已迁移
 - 建议在执行前备份数据库
 - 按照依赖关系顺序删除表（先删除子表，后删除主表）
 
 旧表清单：
 1. customer_customer - 客户主表（旧）
 2. customer_address - 客户收货地址表（旧）
 3. customer_referral - 客户推荐关系表（旧）
 4. customer_status_log - 客户状态变更记录表（旧）
 5. customer_points_record - 客户积分记录表（旧）
 6. customer_account_log - 客户账户操作日志表（旧）
 7. customer_invoice_info - 客户发票信息表（旧）
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ========================================
-- 删除旧表结构
-- ========================================

-- 注意：按照依赖关系顺序删除，先删除子表，最后删除主表

-- ----------------------------
-- 删除客户发票信息表（旧）
-- ----------------------------
DROP TABLE IF EXISTS `customer_invoice_info`;

-- ----------------------------
-- 删除客户账户操作日志表（旧）
-- ----------------------------
DROP TABLE IF EXISTS `customer_account_log`;

-- ----------------------------
-- 删除客户积分记录表（旧）
-- ----------------------------
DROP TABLE IF EXISTS `customer_points_record`;

-- ----------------------------
-- 删除客户状态变更记录表（旧）
-- ----------------------------
DROP TABLE IF EXISTS `customer_status_log`;

-- ----------------------------
-- 删除客户推荐关系表（旧）
-- ----------------------------
DROP TABLE IF EXISTS `customer_referral`;

-- ----------------------------
-- 删除客户收货地址表（旧）
-- ----------------------------
DROP TABLE IF EXISTS `customer_address`;

-- ----------------------------
-- 删除客户主表（旧）
-- ----------------------------
DROP TABLE IF EXISTS `customer_customer`;

SET FOREIGN_KEY_CHECKS = 1;

-- ========================================
-- 执行说明
-- ========================================
/*
执行步骤：
1. 确保新表结构已创建（执行 customer-module.sql）
2. 确保数据已迁移到新表中
3. 备份数据库（重要！）
4. 执行本脚本删除旧表

注意事项：
1. 本脚本会永久删除旧表及其数据
2. 执行前请务必备份数据库
3. 建议在测试环境先验证
4. 确保应用程序已更新为使用新表名
5. 删除顺序：子表 → 主表，避免外键约束错误

验证方法：
-- 检查旧表是否已删除
SHOW TABLES LIKE 'customer_customer';
SHOW TABLES LIKE 'customer_address';
SHOW TABLES LIKE 'customer_referral';
SHOW TABLES LIKE 'customer_status_log';
SHOW TABLES LIKE 'customer_points_record';
SHOW TABLES LIKE 'customer_account_log';
SHOW TABLES LIKE 'customer_invoice_info';

-- 检查新表是否存在
SHOW TABLES LIKE 'customer_info%';
*/
