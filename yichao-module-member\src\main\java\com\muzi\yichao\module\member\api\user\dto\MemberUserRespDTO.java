package com.muzi.yichao.module.member.api.user.dto;

import com.muzi.yichao.framework.common.enums.CommonStatusEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户信息 Response DTO
 *
 * <AUTHOR>
 */
@Data
public class MemberUserRespDTO {

    /**
     * 用户ID
     */
    private Long id;
    /**
     * 用户昵称
     */
    private String nickname;
    /**
     * 帐号状态
     *
     * 枚举 {@link CommonStatusEnum}
     */
    private Integer status;
    /**
     * 用户头像
     */
    private String avatar;
    /**
     * 手机
     */
    private String mobile;
    /**
     * 创建时间（注册时间）
     */
    private LocalDateTime createTime;

    // ========== 其它信息 ==========

    /**
     * 会员级别编号
     */
    private Long levelId;

    /**
     * 积分
     */
    private Integer point;

}
