# SaaS智能家装CRM系统 - 三方工具-投影距离页面功能设计文档

## 1. 页面概述

### 1.1 页面目的
三方工具-投影距离页面是智能家居模块中的专业计算工具，用于计算投影设备的最佳安装距离和位置。该页面为设计师和安装人员提供精确的投影距离计算功能，确保投影效果的最优化。

### 1.2 业务价值
- 提供专业的投影距离计算工具，提升设计和安装的准确性
- 支持多种投影设备型号，建立完整的设备参数库
- 优化投影方案设计，减少安装调试时间和成本
- 提供计算结果的保存和管理功能，便于方案复用

### 1.3 页面位置
- **所属模块**: 智能家居模块 - 三方工具
- **业务流程位置**: 方案设计 → **专业工具计算** → 方案优化
- **关联页面**: 
  - 方案列表页面（工具应用）
  - 新增计算页面（计算记录创建）

## 2. 三方工具-投影距离页面操作流程图

```mermaid
flowchart TD
    A[用户访问投影距离工具] --> B[权限验证]
    B --> C{权限验证通过?}
    C -->|否| D[显示权限不足提示]
    C -->|是| E[加载工具界面]

    E --> F[显示设备选择区]
    E --> G[显示参数输入区]
    E --> H[显示计算结果区]
    E --> I[显示历史记录区]

    F --> J[投影设备品牌选择]
    J --> K[设备型号选择]
    K --> L[加载设备参数]
    L --> M[显示设备规格]

    G --> N[房间尺寸输入]
    G --> O[屏幕参数输入]
    G --> P[安装要求输入]

    N --> Q[房间长度输入]
    N --> R[房间宽度输入]
    N --> S[房间高度输入]

    O --> T[屏幕尺寸输入]
    O --> U[屏幕比例选择]
    O --> V[屏幕类型选择]

    P --> W[安装方式选择]
    P --> X[安装高度输入]
    P --> Y[倾斜角度输入]

    Q --> Z[参数验证]
    R --> Z
    S --> Z
    T --> Z
    U --> Z

    Z --> AA{参数验证通过?}
    AA -->|否| BB[显示错误提示]
    AA -->|是| CC[执行距离计算]

    CC --> DD[计算投影距离]
    DD --> EE[计算最佳位置]
    EE --> FF[计算安装参数]
    FF --> GG[生成计算结果]

    GG --> HH[显示推荐距离]
    GG --> II[显示安装建议]
    GG --> JJ[显示注意事项]

    H --> KK[结果操作]
    KK --> LL[保存计算结果]
    KK --> MM[导出计算报告]
    KK --> NN[新增计算记录]

    I --> OO[显示历史计算]
    OO --> PP[查看历史记录]
    OO --> QQ[重新计算]
    OO --> RR[删除记录]

    LL --> SS[填写保存信息]
    SS --> TT[保存到记录库]

    MM --> UU[选择导出格式]
    UU --> VV[生成报告文件]

    NN --> WW[跳转新增页面]

    style A fill:#e1f5fe
    style C fill:#fff3e0
    style D fill:#ffebee
    style AA fill:#f3e5f5
    style CC fill:#e8f5e8
```

### 流程说明
三方工具-投影距离页面的操作流程包含以下核心环节：

1. **权限验证与界面加载**：用户访问工具时进行权限验证，加载计算界面
2. **设备选择与参数加载**：选择投影设备品牌和型号，自动加载设备参数
3. **环境参数输入**：输入房间尺寸、屏幕参数和安装要求
4. **距离计算与结果展示**：执行计算并展示推荐距离和安装建议
5. **结果保存与管理**：保存计算结果、导出报告或查看历史记录

## 3. 详细功能设计

### 3.1 设备选择功能

#### 3.1.1 设备品牌选择
**功能描述**: 选择投影设备的品牌

**品牌分类**:
- **国际品牌**: 爱普生、索尼、松下、明基、奥图码
- **国内品牌**: 极米、坚果、小米、当贝、峰米
- **商用品牌**: 巴可、科视、NEC、夏普、理光
- **工程品牌**: 专业工程投影设备品牌

#### 3.1.2 设备型号选择
**功能描述**: 选择具体的投影设备型号

**型号信息**:
- **型号名称**: 设备的完整型号名称
- **产品图片**: 设备的产品图片
- **基本规格**: 分辨率、亮度、投射比等基本规格
- **价格信息**: 设备的参考价格
- **上市时间**: 设备的上市时间

#### 3.1.3 设备参数加载
**功能描述**: 自动加载选中设备的技术参数

**技术参数**:
- **投射比例**: 投影距离与画面宽度的比值
- **分辨率**: 设备的原生分辨率
- **亮度**: 设备的光输出亮度（流明）
- **镜头参数**: 镜头焦距、变焦倍数、偏移范围
- **物理尺寸**: 设备的长宽高尺寸和重量

### 3.2 参数输入功能

#### 3.2.1 房间尺寸输入
**功能描述**: 输入房间的基本尺寸参数

**尺寸参数**:
- **房间长度**: 房间的长度，单位米，支持小数
- **房间宽度**: 房间的宽度，单位米，支持小数
- **房间高度**: 房间的高度，单位米，支持小数
- **障碍物**: 房间内障碍物的位置和尺寸
- **门窗位置**: 门窗的位置和尺寸

#### 3.2.2 屏幕参数输入
**功能描述**: 输入投影屏幕的参数

**屏幕参数**:
- **屏幕尺寸**: 屏幕对角线尺寸，单位英寸
- **屏幕比例**: 16:9、4:3、21:9等比例选择
- **屏幕类型**: 白墙、白幕、灰幕、抗光幕等类型
- **屏幕位置**: 屏幕在房间中的安装位置
- **屏幕高度**: 屏幕底边离地面的高度

#### 3.2.3 安装要求输入
**功能描述**: 输入投影设备的安装要求

**安装要求**:
- **安装方式**: 吊装、桌面、壁挂等安装方式
- **安装高度**: 设备的安装高度要求
- **倾斜角度**: 设备的倾斜角度范围
- **维护空间**: 设备维护所需的空间
- **散热要求**: 设备散热的空间要求

### 3.3 距离计算功能

#### 3.3.1 基础距离计算
**功能描述**: 基于投射比例计算基础投影距离

**计算公式**:
- **投影距离** = 屏幕宽度 × 投射比例
- **最小距离** = 屏幕宽度 × 最小投射比例
- **最大距离** = 屏幕宽度 × 最大投射比例
- **屏幕宽度** = 屏幕对角线 × cos(arctan(高宽比))

#### 3.3.2 优化距离计算
**功能描述**: 考虑环境因素的优化距离计算

**优化因素**:
- **房间限制**: 考虑房间长度的限制
- **观看距离**: 考虑最佳观看距离
- **安装空间**: 考虑安装和维护空间
- **散热要求**: 考虑设备散热空间
- **美观要求**: 考虑安装的美观性

#### 3.3.3 安装参数计算
**功能描述**: 计算设备的安装参数

**安装参数**:
- **安装位置**: 设备的最佳安装位置坐标
- **安装高度**: 设备的推荐安装高度
- **倾斜角度**: 设备的推荐倾斜角度
- **镜头偏移**: 镜头偏移的使用建议
- **支架要求**: 安装支架的要求和规格

### 3.4 计算结果展示功能

#### 3.4.1 推荐距离展示
**功能描述**: 展示计算得出的推荐投影距离

**结果展示**:
- **最佳距离**: 推荐的最佳投影距离
- **距离范围**: 可接受的距离范围
- **距离说明**: 距离选择的说明和理由
- **调整建议**: 距离调整的建议
- **影响因素**: 影响距离的主要因素

#### 3.4.2 安装建议展示
**功能描述**: 展示设备安装的建议和要求

**安装建议**:
- **安装位置**: 推荐的安装位置示意图
- **安装方式**: 推荐的安装方式和支架
- **安装注意事项**: 安装过程中的注意事项
- **调试建议**: 安装后的调试建议
- **维护建议**: 日常维护的建议

#### 3.4.3 效果预估展示
**功能描述**: 预估投影效果和画面质量

**效果预估**:
- **画面尺寸**: 实际的画面尺寸
- **画面亮度**: 预估的画面亮度
- **画面清晰度**: 预估的画面清晰度
- **观看体验**: 观看体验的评估
- **改进建议**: 效果改进的建议

### 3.5 结果管理功能

#### 3.5.1 计算结果保存
**功能描述**: 保存计算结果供后续使用

**保存信息**:
- **计算名称**: 为计算结果命名
- **项目关联**: 关联到具体项目或方案
- **客户信息**: 关联的客户信息
- **计算参数**: 完整的计算参数
- **计算结果**: 详细的计算结果

#### 3.5.2 报告导出功能
**功能描述**: 导出计算结果报告

**导出格式**:
- **PDF报告**: 专业的PDF格式报告
- **Word文档**: 可编辑的Word文档
- **Excel表格**: 数据表格格式
- **图片格式**: 结果图片导出

#### 3.5.3 历史记录管理
**功能描述**: 管理历史计算记录

**记录管理**:
- **记录列表**: 显示历史计算记录列表
- **记录搜索**: 搜索特定的计算记录
- **记录查看**: 查看历史计算的详细信息
- **记录复用**: 基于历史记录重新计算
- **记录删除**: 删除不需要的计算记录

### 3.6 新增计算记录功能

#### 3.6.1 记录信息录入
**功能描述**: 录入新计算记录的基本信息

**录入信息**:
- **记录名称**: 计算记录的名称
- **项目名称**: 关联的项目名称
- **客户信息**: 客户的基本信息
- **计算日期**: 计算的日期
- **备注说明**: 计算的备注和说明

#### 3.6.2 计算参数记录
**功能描述**: 记录完整的计算参数

**参数记录**:
- **设备信息**: 使用的投影设备信息
- **房间参数**: 房间的尺寸参数
- **屏幕参数**: 屏幕的参数设置
- **安装要求**: 安装的要求和限制
- **特殊要求**: 其他特殊要求

#### 3.6.3 结果记录保存
**功能描述**: 保存计算结果和建议

**结果保存**:
- **计算结果**: 详细的计算结果数据
- **安装建议**: 安装的建议和要求
- **效果预估**: 效果预估的结果
- **注意事项**: 重要的注意事项
- **后续跟进**: 后续跟进的计划

## 4. 用户界面设计

### 4.1 页面布局设计
- **左侧设备区**: 设备选择和参数显示区域
- **中间输入区**: 参数输入和设置区域
- **右侧结果区**: 计算结果和建议显示区域
- **底部记录区**: 历史记录和操作按钮区域

### 4.2 计算界面设计
- **向导式输入**: 分步骤引导用户输入参数
- **实时计算**: 参数变化时实时更新计算结果
- **可视化展示**: 图形化展示计算结果和建议
- **专业报告**: 专业格式的计算报告

### 4.3 交互设计规范
- **智能提示**: 提供参数输入的智能提示
- **错误提示**: 清晰的错误信息和修正建议
- **快速操作**: 提供常用操作的快速入口
- **数据验证**: 实时验证输入数据的有效性

## 5. 数据流向

### 5.1 数据输入
- **来源**: 用户输入的计算参数 + 设备参数库
- **格式**: 结构化的计算参数数据

### 5.2 数据输出
- **流向**: 方案设计（计算结果应用）
- **存储**: 计算记录数据库

### 5.3 业务关联
- **应用场景**: 方案设计中的投影方案设计
- **关联页面**: 方案列表页面（计算结果应用）
- **数据来源**: 设备参数库、计算公式库

## 6. 权限控制

### 6.1 访问权限
- **设计师**: 可以使用工具进行计算
- **技术人员**: 可以使用工具和管理设备参数
- **销售人员**: 可以查看计算结果

### 6.2 操作权限
- **计算权限**: 工具使用权限控制
- **保存权限**: 计算结果保存权限
- **导出权限**: 报告导出权限控制
- **管理权限**: 历史记录管理权限

## 7. 异常处理

### 7.1 计算异常
- **参数错误**: 输入参数错误的提示和处理
- **计算失败**: 计算过程失败的重试机制
- **结果异常**: 计算结果异常的检测和处理

### 7.2 数据异常
- **设备数据**: 设备参数加载失败的处理
- **保存失败**: 数据保存失败的重试机制
- **网络异常**: 网络连接异常的处理

---

**文档版本**: v1.0
**编写日期**: 2025-07-14
**编写人员**: AI系统架构师
**审核状态**: 待审核
**对应图片**: 三方工具-投影距离页面.png
