### /trade-order/settlement 获得订单结算信息（基于商品）
GET {{appApi}}/trade/order/settlement?type=0&items[0].skuId=1&items[0].count=2&items[1].skuId=2&items[1].count=3&couponId=1
Authorization: Bearer {{appToken}}
tenant-id: {{appTenantId}}

### /trade-order/settlement 获得订单结算信息（基于购物车）
GET {{appApi}}/trade/order/settlement?type=0&items[0].cartId=50&couponId=1
Authorization: Bearer {{appToken}}
tenant-id: {{appTenantId}}

### /trade-order/create 创建订单（基于商品）【快递】
POST {{appApi}}/trade/order/create
Content-Type: application/json
Authorization: Bearer {{appToken}}
tenant-id: {{appTenantId}}

{
  "pointStatus": true,
  "deliveryType": 1,
  "addressId": 21,
  "items": [
    {
      "skuId": 1,
      "count": 2
    }
  ],
  "remark": "我是备注"
}

### /trade-order/create 创建订单（基于商品）【自提】
POST {{appApi}}/trade/order/create
Content-Type: application/json
Authorization: Bearer {{appToken}}
tenant-id: {{appTenantId}}

{
  "pointStatus": true,
  "deliveryType": 2,
  "pickUpStoreId": 1,
  "items": [
    {
      "skuId": 1,
      "count": 2
    }
  ],
  "remark": "我是备注",
  "receiverName": "土豆",
  "receiverMobile": "15601691300"
}

### 获得订单交易的分页
GET {{appApi}}/trade/order/page?pageNo=1&pageSize=10
Authorization: Bearer {{appToken}}
tenant-id: {{appTenantId}}

### 获得订单交易的详细
GET {{appApi}}/trade/order/get-detail?id=21
Authorization: Bearer {{appToken}}
tenant-id: {{appTenantId}}

### 获得交易订单的物流轨迹
GET {{appApi}}/trade/order/get-express-track-list?id=70
Authorization: Bearer {{appToken}}
tenant-id: {{appTenantId}}

### /trade-order/settlement-product 获得商品结算信息
GET {{appApi}}/trade/order/settlement-product?spuIds=633
Authorization: Bearer {{appToken}}
tenant-id: {{appTenantId}}