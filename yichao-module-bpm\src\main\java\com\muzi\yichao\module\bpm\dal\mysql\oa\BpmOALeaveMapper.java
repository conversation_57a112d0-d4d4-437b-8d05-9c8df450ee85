package com.muzi.yichao.module.bpm.dal.mysql.oa;

import com.muzi.yichao.module.bpm.controller.admin.oa.vo.BpmOALeavePageReqVO;
import com.muzi.yichao.module.bpm.dal.dataobject.oa.BpmOALeaveDO;
import com.muzi.yichao.framework.common.pojo.PageResult;
import com.muzi.yichao.framework.mybatis.core.mapper.BaseMapperX;
import com.muzi.yichao.framework.mybatis.core.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;

/**
 * 请假申请 Mapper
 *
 * <AUTHOR>
 * <AUTHOR>
 */
@Mapper
public interface BpmOALeaveMapper extends BaseMapperX<BpmOALeaveDO> {

    default PageResult<BpmOALeaveDO> selectPage(Long userId, BpmOALeavePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<BpmOALeaveDO>()
                .eqIfPresent(BpmOALeaveDO::getUserId, userId)
                .eqIfPresent(BpmOALeaveDO::getStatus, reqVO.getStatus())
                .eqIfPresent(BpmOALeaveDO::getType, reqVO.getType())
                .likeIfPresent(BpmOALeaveDO::getReason, reqVO.getReason())
                .betweenIfPresent(BpmOALeaveDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(BpmOALeaveDO::getId));
    }

}
