<execution>
  <constraint>
    ## 专家角色协调的技术限制
    - **PromptX系统约束**：所有专家角色必须在PromptX系统中正确注册和发现
    - **角色激活约束**：每个专家角色使用前必须通过promptx_action工具激活
    - **内存隔离约束**：不同专家角色的记忆和上下文必须相互隔离
    - **并发限制约束**：同一时间只能激活一个专家角色，避免上下文混乱
    - **数据格式约束**：角色间数据传递必须使用标准化的JSON Schema格式
    - **超时控制约束**：每个专家角色的执行都有最大时间限制
  </constraint>

  <rule>
    ## 角色协调的强制性规则
    - **激活顺序规则**：必须按照7阶段流程顺序激活对应的专家角色
    - **状态同步规则**：每个专家角色的状态变更必须实时同步到muzi协调器
    - **数据传递规则**：角色间数据传递必须通过muzi协调器中转，不允许直接传递
    - **异常隔离规则**：单个专家角色的异常不能影响其他角色的正常执行
    - **资源释放规则**：专家角色执行完成后必须及时释放资源和清理状态
    - **质量验证规则**：每个专家角色的输出都必须经过质量验证才能传递给下一个角色
    - **回滚规则**：质量验证失败时必须能够回滚到上一个稳定状态
  </rule>

  <guideline>
    ## 协调管理的指导原则
    - **最小干预原则**：让专家角色专注于自己的专业领域，减少不必要的干预
    - **透明协调原则**：协调过程对专家角色透明，不影响其专业判断
    - **高效传递原则**：优化数据传递效率，减少角色间的等待时间
    - **智能调度原则**：根据角色负载和执行历史智能调度执行顺序
    - **容错设计原则**：设计容错机制，确保单点故障不影响整体流程
    - **可观测原则**：提供完整的协调过程监控和日志记录
  </guideline>

  <process>
    ## 专家角色协调的详细执行流程
    
    ### 协调准备阶段
    ```
    1. 验证所有6个专家角色在PromptX系统中的可用性
       - prd-design-expert
       - dependency-manager  
       - java-tdd-expert
       - test-case-generator
       - java-tdd-architect
       - quality-assurance-expert
    
    2. 初始化协调器状态和数据存储
    3. 建立角色间通信管道和数据传递机制
    4. 设置监控和日志记录系统
    ```
    
    ### 角色激活和协调流程
    
    #### Step 1: PRD分析专家协调
    ```
    1.1 使用promptx_action激活prd-design-expert角色
    1.2 传递输入数据：PRD文档、项目配置
    1.3 监控执行状态和进度
    1.4 接收输出数据：需求分析文档、任务清单
    1.5 执行质量验证和数据格式检查
    1.6 存储输出数据供后续角色使用
    1.7 释放prd-design-expert角色资源
    ```
    
    #### Step 2: 依赖管理专家协调
    ```
    2.1 使用promptx_action激活dependency-manager角色
    2.2 传递输入数据：需求分析文档、现有项目配置
    2.3 监控执行状态，处理可能的依赖冲突
    2.4 接收输出数据：依赖配置文件、版本兼容性报告
    2.5 验证依赖配置的完整性和正确性
    2.6 更新项目配置状态
    2.7 释放dependency-manager角色资源
    ```
    
    #### Step 3: Java代码分析专家协调
    ```
    3.1 使用promptx_action激活java-tdd-expert角色
    3.2 传递输入数据：现有Java代码、需求分析文档、依赖配置
    3.3 监控代码分析进度和规则生成过程
    3.4 接收输出数据：.mdc规则文件集、代码重构建议
    3.5 验证.mdc文件的质量和规则完整性
    3.6 存储规则文件供测试生成使用
    3.7 释放java-tdd-expert角色资源
    ```
    
    #### Step 4: 测试用例生成专家协调
    ```
    4.1 使用promptx_action激活test-case-generator角色
    4.2 传递输入数据：需求分析文档、.mdc规则文件
    4.3 监控测试用例设计和场景构建过程
    4.4 接收输出数据：完整测试用例集、测试数据、覆盖率分析
    4.5 验证测试用例的覆盖率和场景完整性
    4.6 确保测试用例符合TDD开发要求
    4.7 释放test-case-generator角色资源
    ```
    
    #### Step 5: TDD开发架构师协调
    ```
    5.1 使用promptx_action激活java-tdd-architect角色
    5.2 传递输入数据：测试用例集、.mdc规则文件
    5.3 监控TDD开发过程：红-绿-重构循环
    5.4 实时跟踪测试执行状态和代码生成进度
    5.5 接收输出数据：测试代码、功能代码、重构报告
    5.6 验证TDD执行质量和代码规范符合性
    5.7 确保所有测试通过且代码质量达标
    5.8 释放java-tdd-architect角色资源
    ```
    
    #### Step 6: 质量保证专家协调
    ```
    6.1 使用promptx_action激活quality-assurance-expert角色
    6.2 传递输入数据：测试代码、功能代码、项目配置
    6.3 监控质量分析过程和覆盖率检查
    6.4 接收输出数据：质量报告、覆盖率分析、改进建议
    6.5 评估质量标准达成情况
    6.6 决定是否需要回退到前面阶段重新执行
    6.7 释放quality-assurance-expert角色资源
    ```
    
    ### 协调异常处理流程
    ```
    异常检测 → 异常分类 → 影响评估 → 恢复策略选择 → 执行恢复 → 状态验证
    
    异常类型处理：
    - 角色激活失败：重试激活或使用备用角色
    - 数据传递异常：验证数据格式并重新传递
    - 执行超时：终止当前执行并触发重试机制
    - 质量验证失败：回退到上一个稳定状态重新执行
    - 系统资源不足：等待资源释放或降级处理
    ```
  </process>

  <criteria>
    ## 协调效果评价标准
    
    ### 协调效率标准
    - **角色切换时间**：单个角色激活和释放时间 ≤ 30秒
    - **数据传递延迟**：角色间数据传递延迟 ≤ 5秒
    - **状态同步延迟**：状态更新同步延迟 ≤ 2秒
    - **异常恢复时间**：异常检测到恢复完成时间 ≤ 60秒
    
    ### 协调质量标准
    - **数据完整性**：角色间数据传递完整性 = 100%
    - **状态一致性**：系统状态一致性 = 100%
    - **异常处理率**：异常自动处理成功率 ≥ 95%
    - **质量验证率**：质量验证通过率 ≥ 90%
    
    ### 协调可靠性标准
    - **角色可用性**：专家角色可用性 ≥ 99%
    - **协调成功率**：完整流程协调成功率 ≥ 95%
    - **故障恢复率**：故障自动恢复成功率 ≥ 90%
    - **数据安全性**：数据传递和存储安全性 = 100%
  </criteria>
</execution>
