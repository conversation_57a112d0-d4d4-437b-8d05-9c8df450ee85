package com.muzi.yichao.module.customer.controller.admin.info.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import com.muzi.yichao.framework.excel.core.annotations.DictFormat;
import com.muzi.yichao.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 客户信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InfoRespVO {

    @Schema(description = "客户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15662")
    @ExcelProperty("客户ID")
    private Long id;

    @Schema(description = "客户编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("客户编号")
    private String customerNo;

    @Schema(description = "门店ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "12401")
    @ExcelProperty("门店ID")
    private Long storeId;

    @Schema(description = "客户类别：1-服务商客户，2-品牌家装客户，3-个人客户，4-企业客户", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "客户类别：1-服务商客户，2-品牌家装客户，3-个人客户，4-企业客户", converter = DictConvert.class)
    @DictFormat("customer_type") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer category;

    @Schema(description = "来源渠道：1-设计师推荐，2-大众点评，3-网络推广，4-朋友介绍，5-门店到访，6-其他", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "来源渠道：1-设计师推荐，2-大众点评，3-网络推广，4-朋友介绍，5-门店到访，6-其他", converter = DictConvert.class)
    @DictFormat("customer_source") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer source;

    @Schema(description = "客户姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("客户姓名")
    private String name;

    @Schema(description = "手机号码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("手机号码")
    private String mobile;

    @Schema(description = "微信号")
    @ExcelProperty("微信号")
    private String wechat;

    @Schema(description = "性别：0-未知，1-男，2-女")
    @ExcelProperty(value = "性别：0-未知，1-男，2-女", converter = DictConvert.class)
    @DictFormat("system_user_sex") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer sex;

    @Schema(description = "生日")
    @ExcelProperty("生日")
    private LocalDate birthday;

    @Schema(description = "证件类型：1-身份证，2-护照，3-其他", example = "2")
    @ExcelProperty(value = "证件类型：1-身份证，2-护照，3-其他", converter = DictConvert.class)
    @DictFormat("id_type") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer idType;

    @Schema(description = "证件号码")
    @ExcelProperty("证件号码")
    private String idNumber;

    @Schema(description = "公司名称", example = "芋艿")
    @ExcelProperty("公司名称")
    private String companyName;

    @Schema(description = "跟进人ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "31548")
    @ExcelProperty("跟进人ID")
    private Long followerUserId;

    @Schema(description = "设计师ID", example = "28932")
    @ExcelProperty("设计师ID")
    private Long designerUserId;

    @Schema(description = "登录密码（加密）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("登录密码（加密）")
    private String password;

    @Schema(description = "发卡日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("发卡日期")
    private LocalDate issueDate;

    @Schema(description = "到期日期")
    @ExcelProperty("到期日期")
    private LocalDate expireDate;

    @Schema(description = "收款账户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "30294")
    @ExcelProperty("收款账户ID")
    private Long accountId;

    @Schema(description = "初始存款", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("初始存款")
    private BigDecimal initialDeposit;

    @Schema(description = "红包金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("红包金额")
    private BigDecimal bonusAmount;

    @Schema(description = "初始积分", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("初始积分")
    private Integer initialPoints;

    @Schema(description = "是否安装师傅", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否安装师傅")
    private Boolean isInstaller;

    @Schema(description = "客户类型：1-前装客户，2-后装客户，3-改造客户", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty(value = "客户类型：1-前装客户，2-后装客户，3-改造客户", converter = DictConvert.class)
    @DictFormat("customer_type") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer customerType;

    @Schema(description = "APP账号", example = "23810")
    @ExcelProperty("APP账号")
    private String appAccount;

    @Schema(description = "是否需要发票", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否需要发票")
    private Boolean needInvoice;

    @Schema(description = "推荐人ID", example = "27409")
    @ExcelProperty("推荐人ID")
    private Long referrerId;

    @Schema(description = "备注信息", example = "你猜")
    @ExcelProperty("备注信息")
    private String remark;

    @Schema(description = "邮箱地址")
    @ExcelProperty("邮箱地址")
    private String email;

    @Schema(description = "职位")
    @ExcelProperty("职位")
    private String position;

    @Schema(description = "客户经理ID", example = "20600")
    @ExcelProperty("客户经理ID")
    private Long customerManagerId;

    @Schema(description = "VIP等级：0-普通，1-VIP1，2-VIP2，3-VIP3", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "VIP等级：0-普通，1-VIP1，2-VIP2，3-VIP3", converter = DictConvert.class)
    @DictFormat("vip_level") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer vipLevel;

    @Schema(description = "客户标签（JSON格式）")
    @ExcelProperty("客户标签（JSON格式）")
    private String customerTags;

    @Schema(description = "状态：0-禁用，1-正常，2-冻结，3-黑名单，4-VIP", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty(value = "状态：0-禁用，1-正常，2-冻结，3-黑名单，4-VIP", converter = DictConvert.class)
    @DictFormat("customer_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}