<execution>
  <constraint>
    ## 基础质量检查技术约束
    - **构建集成**：质量检查必须集成到Maven/Gradle构建流程中
    - **性能影响**：质量检查不能显著延长构建时间（增加时间≤30%）
    - **工具兼容性**：必须与现有TDD工具链兼容
    - **报告格式**：生成标准化的质量报告格式
    - **阈值配置**：支持可配置的质量阈值设置
  </constraint>

  <rule>
    ## 基础质量检查强制规则
    - **代码覆盖率门禁**：单元测试覆盖率必须≥90%，集成测试覆盖率≥80%
    - **静态分析强制**：所有代码必须通过SpotBugs基础检查
    - **代码规范强制**：必须符合项目定义的CheckStyle规范
    - **复杂度控制**：方法圈复杂度≤10，类复杂度≤50
    - **重复代码限制**：代码重复率≤5%
    - **测试质量要求**：测试代码也必须符合质量标准
  </rule>

  <guideline>
    ## 基础质量检查指导原则
    - **渐进式改进**：对现有代码采用渐进式质量改进策略
    - **开发者友好**：提供清晰的质量问题描述和修复建议
    - **自动化优先**：优先使用自动化工具进行质量检查
    - **快速反馈**：在开发过程中提供实时质量反馈
    - **标准统一**：在团队内保持一致的质量标准
    - **持续监控**：建立质量趋势监控和改进机制
  </guideline>

  <process>
    ## 基础质量检查工作流程
    
    ### 阶段1: 代码覆盖率检查
    ```mermaid
    flowchart TD
        A[执行测试] --> B[生成覆盖率报告]
        B --> C[JaCoCo分析]
        C --> D{覆盖率达标?}
        D -->|是| E[通过覆盖率检查]
        D -->|否| F[生成覆盖率改进建议]
        F --> G[标记需要补充测试的代码]
    ```
    
    **覆盖率检查配置**：
    ```xml
    <!-- Maven JaCoCo 配置示例 -->
    <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <configuration>
            <rules>
                <rule>
                    <element>BUNDLE</element>
                    <limits>
                        <limit>
                            <counter>LINE</counter>
                            <value>COVEREDRATIO</value>
                            <minimum>0.90</minimum>
                        </limit>
                    </limits>
                </rule>
            </rules>
        </configuration>
    </plugin>
    ```
    
    ### 阶段2: 静态代码分析
    ```mermaid
    flowchart LR
        A[SpotBugs扫描] --> B[PMD检查]
        B --> C[CheckStyle验证]
        C --> D[结果汇总]
        D --> E[生成改进建议]
    ```
    
    **静态分析工具配置**：
    - **SpotBugs**：检测潜在的代码缺陷和安全问题
    - **PMD**：检测代码质量问题和最佳实践违规
    - **CheckStyle**：验证代码格式和命名规范
    
    ### 阶段3: 复杂度分析
    ```mermaid
    flowchart TD
        A[方法复杂度分析] --> B[类复杂度分析]
        B --> C[包复杂度分析]
        C --> D{复杂度超标?}
        D -->|是| E[生成重构建议]
        D -->|否| F[复杂度检查通过]
    ```
    
    **复杂度检查标准**：
    - **圈复杂度**：方法≤10，类≤50
    - **认知复杂度**：方法≤15，类≤100
    - **嵌套深度**：≤4层
    - **参数数量**：方法参数≤5个
    
    ### 阶段4: 重复代码检测
    ```mermaid
    flowchart LR
        A[代码块分析] --> B[相似度计算]
        B --> C[重复度统计]
        C --> D[重构建议生成]
    ```
    
    **重复代码检测配置**：
    - **最小重复块**：≥6行代码或≥50个token
    - **相似度阈值**：≥85%相似度认定为重复
    - **重复率限制**：项目整体重复率≤5%
    
    ### 阶段5: 测试质量评估
    ```mermaid
    flowchart TD
        A[测试代码分析] --> B[测试覆盖质量]
        B --> C[测试用例有效性]
        C --> D[测试维护性评估]
        D --> E[测试质量报告]
    ```
    
    **测试质量检查项**：
    - **测试命名规范**：遵循[被测方法]_[测试条件]_[预期结果]格式
    - **断言有效性**：每个测试至少包含一个有意义的断言
    - **测试独立性**：测试之间不能有依赖关系
    - **边界值测试**：关键方法必须包含边界值和异常场景测试
    
    ### 阶段6: 质量报告生成
    ```mermaid
    flowchart TD
        A[收集质量数据] --> B[计算质量得分]
        B --> C[生成趋势分析]
        C --> D[创建改进建议]
        D --> E[输出质量报告]
    ```
    
    **质量报告内容**：
    - **质量概览**：整体质量得分和等级
    - **覆盖率报告**：详细的测试覆盖率分析
    - **问题清单**：按优先级排序的质量问题
    - **改进建议**：具体的代码改进建议
    - **趋势分析**：质量指标的历史趋势
    
    ### 阶段7: 质量门禁决策
    ```mermaid
    flowchart TD
        A[质量标准检查] --> B{是否达标?}
        B -->|达标| C[质量检查通过]
        B -->|不达标| D[生成阻止报告]
        C --> E[继续构建流程]
        D --> F[提供修复指导]
    ```
    
    **质量门禁标准**：
    - **必须通过项**：覆盖率≥90%，无高危SpotBugs问题
    - **警告项**：复杂度超标，重复代码超标
    - **建议项**：代码规范问题，性能优化建议
  </process>

  <criteria>
    ## 基础质量检查评价标准
    
    ### 检查效率标准
    - ✅ 质量检查完成时间 ≤ 构建时间的30%
    - ✅ 覆盖率分析时间 ≤ 2分钟
    - ✅ 静态分析时间 ≤ 3分钟
    - ✅ 报告生成时间 ≤ 1分钟
    
    ### 检查准确性标准
    - ✅ 覆盖率计算准确率 = 100%
    - ✅ 静态分析误报率 ≤ 15%
    - ✅ 复杂度计算准确率 = 100%
    - ✅ 重复代码检测准确率 ≥ 90%
    
    ### 质量改进效果
    - ✅ 代码质量得分持续提升
    - ✅ 测试覆盖率稳定在90%以上
    - ✅ 代码缺陷率下降趋势
    - ✅ 开发效率保持稳定
    
    ### 开发体验标准
    - ✅ 质量反馈及时性 ≤ 5分钟
    - ✅ 问题描述清晰度 ≥ 90%
    - ✅ 修复建议有效性 ≥ 80%
    - ✅ 工具集成透明度 ≥ 95%
  </criteria>
</execution>
