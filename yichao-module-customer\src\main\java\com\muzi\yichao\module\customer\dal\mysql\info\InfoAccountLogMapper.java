package com.muzi.yichao.module.customer.dal.mysql.info;

import java.util.*;

import com.muzi.yichao.framework.common.pojo.PageResult;
import com.muzi.yichao.framework.common.pojo.PageParam;
import com.muzi.yichao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.muzi.yichao.framework.mybatis.core.mapper.BaseMapperX;
import com.muzi.yichao.module.customer.dal.dataobject.info.InfoAccountLogDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 客户账户操作日志 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InfoAccountLogMapper extends BaseMapperX<InfoAccountLogDO> {

    default PageResult<InfoAccountLogDO> selectPage(PageParam reqVO, Long customerId) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InfoAccountLogDO>()
            .eq(InfoAccountLogDO::getCustomerId, customerId)
            .orderByDesc(InfoAccountLogDO::getId));
    }

    default int deleteByCustomerId(Long customerId) {
        return delete(InfoAccountLogDO::getCustomerId, customerId);
    }

	default int deleteByCustomerIds(List<Long> customerIds) {
	    return deleteBatch(InfoAccountLogDO::getCustomerId, customerIds);
	}

}
