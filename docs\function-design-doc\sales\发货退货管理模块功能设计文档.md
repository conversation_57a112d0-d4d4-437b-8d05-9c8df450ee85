# 销售管理模块 - 发货退货管理页面功能设计文档

## 1. 页面概述

### 1.1 页面目的
发货退货管理模块是智能家装管理平台销售管理系统的出库发货管理核心工具，负责销售订单的发货执行、记录管理、物流追踪及退货处理。该模块通过货单列表、发货详情、发货操作三个核心页面，实现从订单生成到商品交付的完整发货流程管理，确保发货过程的规范化、可追溯性和高效性。

### 1.2 业务价值
- 提供完整的发货流程管理，实现从订单到交付的全程跟踪和控制
- 建立标准化的发货操作流程，提升发货效率和准确性，减少发货错误
- 实现发货批次管理和物流信息记录，为客户提供透明的物流跟踪服务
- 支持退货入库处理，建立完整的逆向物流管理体系
- 提供发货数据统计分析，为库存管理和物流优化提供决策支持
- 集成快递单打印和物流追踪，提升发货操作的自动化水平

### 1.3 页面入口
- **主要入口**：销售管理 → 发货退货管理 → 货单列表
- **快速入口**：销售单管理页面的发货处理链接
- **业务入口**：库存管理、客户服务等业务场景的发货操作

### 1.4 功能架构
发货退货管理模块包含三个核心页面和四个功能区域：

**核心页面**：
- **货单列表页面**：发货订单的总览和管理入口
- **发货详情页面**：单个订单的发货明细和批次管理
- **发货操作页面**：具体的发货执行和物流信息录入

**功能区域**：
- **订单发货管理**：发货订单的筛选、查看和状态管理
- **发货批次控制**：发货批次的创建、管理和追踪
- **物流信息管理**：快递信息录入、打印和追踪
- **退货处理管理**：退货入库和逆向物流处理

### 1.5 使用群体与应用场景
**主要使用角色**：
- **仓库管理员**：货单管理、库存出库、发货批次管理、退货入库处理
- **发货专员**：发货操作执行、快递信息录入、发货确认、物流协调
- **物流专员**：物流信息管理、快递单打印、物流状态跟踪、异常处理
- **客服人员**：发货进度查询、客户咨询响应、发货异常协调处理
- **销售人员**：订单发货状态查看、客户发货通知、发货进度跟踪

**核心应用场景**：
- **订单发货处理**：处理销售订单的商品出库和发货操作
- **发货进度跟踪**：实时跟踪订单发货状态和物流进度
- **批次发货管理**：管理分批发货的订单和发货记录
- **退货入库处理**：处理客户退货的入库和库存更新

## 2. 发货退货管理操作流程图

```mermaid
flowchart TD
    A[销售订单生成] --> B[自动生成货单]
    B --> C[货单列表页面]
    
    C --> D[货单筛选查询]
    C --> E[查看发货详情]
    C --> F[执行发货操作]
    C --> G[订单备注管理]
    
    D --> H[按日期筛选]
    D --> I[按订单号搜索]
    D --> J[按客户名搜索]
    D --> K[按状态筛选]
    
    E --> L[发货详情页面]
    L --> M[商品发货记录]
    L --> N[发货批次明细]
    L --> O[物流追踪信息]
    L --> P[收货信息展示]
    
    M --> Q[查看应发实发数量]
    M --> R[处理退货入库]
    M --> R1[处理退回入库]
    
    N --> S[批次发货记录]
    N --> T[快递信息管理]
    N --> U[发货状态跟踪]
    N --> V1[换货操作]
    N --> W1[打印操作]
    
    F --> V[发货操作页面]
    V --> W[待发货商品确认]
    V --> X[收货信息核对]
    V --> Y[物流信息录入]
    V --> Z[发货执行操作]
    
    W --> AA[确认发货数量]
    W --> BB[选择出库仓库]
    W --> CC[指定库位信息]
    
    X --> DD[收货人信息]
    X --> EE[收货地址确认]
    X --> FF[联系电话核对]
    X --> GG[收货备注录入]
    
    Y --> HH[选择物流方式]
    Y --> II[选择快递公司]
    Y --> JJ[录入快递单号]
    Y --> KK[填写发货备注]
    
    Z --> LL[确认发货]
    Z --> MM[保存物流信息]
    Z --> NN[打印快递单]
    Z --> OO[预览打印模板]
    
    LL --> PP[生成发货批次]
    PP --> QQ[更新库存状态]
    QQ --> RR[发送发货通知]
    RR --> SS[物流状态跟踪]
    
    SS --> TT[运输中]
    SS --> UU[已签收]
    SS --> VV[异常处理]
    
    UU --> WW[发货完成]
    VV --> XX[退货处理]
    XX --> YY[退货入库]
    YY --> ZZ[库存更新]
    
    V1 --> X1[换货申请发起]
    X1 --> Y1[换货原因确认]
    Y1 --> Z1[换货商品选择]
    Z1 --> AA1[新商品确认]
    AA1 --> BB1[生成换货单]
    BB1 --> CC1[关联原订单]

    W1 --> DD1[选择打印类型]
    DD1 --> EE1[发货单打印]
    DD1 --> FF1[快递面单打印]
    EE1 --> GG1[批量打印支持]
    FF1 --> GG1

    R --> AAA[退货原因确认]
    AAA --> BBB[退货商品检验]
    BBB --> CCC[退货入库操作]
    CCC --> DDD[更新订单状态]

    R1 --> EEE[退回原因确认]
    EEE --> FFF[批量处理退回]
    FFF --> GGG[恢复库存状态]
    GGG --> HHH[更新发货状态]
    
    style A fill:#e1f5fe
    style C fill:#f3e5f5
    style L fill:#f3e5f5
    style V fill:#f3e5f5
    style WW fill:#e8f5e8
    style ZZ fill:#e8f5e8
```

### 流程说明
发货退货管理模块的操作流程主要包含以下几个核心环节：

1. **订单货单生成**：销售订单自动生成货单，进入发货管理流程
2. **货单列表管理**：通过货单列表页面进行订单筛选、查看和管理
3. **发货详情查看**：查看单个订单的详细发货信息和批次记录
4. **发货操作执行**：执行具体的发货操作，录入物流信息
5. **批次管理操作**：管理发货批次，支持换货和打印操作
6. **物流跟踪管理**：跟踪发货状态和物流进度，处理异常情况
7. **退货处理流程**：处理客户退货和退回，执行相应的入库操作

**典型操作路径**：
```
货单列表页（图1）→ 点击"详情" → 发货详情页（图2）→ 查看批次记录 →
点击"换货"或"打印" → 执行相应操作

货单列表页（图1）→ 点击"发货" → 发货操作页（图3）→ 填写物流信息 →
确认发货 → 生成发货记录 → 更新订单状态
```

## 3. 详细功能设计

### 3.1 货单列表管理功能

#### 3.1.1 货单列表展示
**功能描述**：展示所有待发货和已发货订单的总览信息

**列表字段**：
- **日期**：货单生成日期，支持按日期排序
- **订单号**：关联的销售订单唯一编号，点击可进入发货详情页
- **订单客户**：客户名称，如"Yeelight品牌运营中心"
- **商品**：订单中的主要商品名称，多商品时显示首个商品为代表
- **传发**：标记商品是否已发货，显示发货数量汇总
- **退货入库**：显示退货流程中的入库处理数量
- **收货信息**：客户的收货地址（部分内容截断保护隐私）
- **操作按钮**：发货、详情、订单备注等操作功能

#### 3.1.2 筛选搜索功能
**功能描述**：提供多维度的货单筛选和搜索功能

**筛选条件**：
- **起止日期**：按货单生成日期范围筛选
- **订单号**：支持精确搜索订单编号
- **快递单号**：按快递单号搜索已发货订单
- **发货状态**：按发货状态筛选（待发货、部分发货、已发货、已签收）
- **客户名称**：支持客户名称模糊搜索
- **商品名称**：按商品名称筛选相关订单

**搜索特性**：
- **组合查询**：支持多条件组合筛选
- **实时搜索**：输入过程中实时搜索建议
- **快速筛选**：常用筛选条件的快速选择
- **搜索历史**：保存常用搜索条件

#### 3.1.3 分页浏览功能
**功能描述**：支持大量货单数据的分页浏览

**分页特性**：
- **默认分页**：每页默认展示15条记录
- **分页大小**：支持调整每页显示数量（10/15/20/50条）
- **快速跳转**：支持页码快速跳转
- **总数统计**：显示符合条件的货单总数

### 3.2 发货详情管理功能

#### 3.2.1 商品发货记录管理
**功能描述**：展示订单中各商品的发货状态和数量统计

**记录字段**：
- **商品编号**：唯一标识，格式如"1-00000xxx"
- **商品名称**：完整商品名称，如"YL-Z1"等产品
- **应发数量**：订单要求发货的数量
- **实发数量**：已成功发出的数量
- **退货数量**：已退回的数量
- **签收数量**：快递签收的数量（客户确认）
- **还欠数量**：客户未收到的数量（应发 - 实发）
- **得分人数**：用于服务评分或提成的人员数量

**操作功能**：
- **退货入库**：处理客户正常退货的入库操作
  - 适用于客户主动发起的退货申请
  - 需要填写退货原因和退货数量
  - 系统自动更新库存和订单状态
- **退回入库**：处理因各种原因退回的商品入库
  - 适用于快递拒收、地址错误等情况
  - 支持批量处理多个商品的退回
  - 自动恢复库存并更新发货状态

#### 3.2.2 发货批次明细管理
**功能描述**：管理订单的多次发货批次记录

**批次信息**：
- **批次发货时间**：每次发货的具体时间
- **发货商品清单**：本次发货的商品明细
- **数量统计**：发货数量、仓库、库位信息
- **快递信息**：快递单号、物流公司、发货人
- **签收状态**：运输中、已签收、取消等状态
- **操作功能**：换货、打印、导出、撤销等操作

**批次操作功能**：
- **换货操作**：点击后可为该批次的商品发起换货流程
  - 系统自动生成新的发货单
  - 关联原订单确保换货操作的可追溯性
  - 支持部分商品换货和全部商品换货
  - 记录换货原因和换货商品明细
- **打印操作**：点击后可打印该批次的发货单或快递面单
  - 支持发货单打印和快递面单打印
  - 支持批量打印多个批次
  - 对接电子面单系统，自动生成标准格式
  - 支持自定义打印模板和格式

**批次卡片示例**：
```
快递单号：AQC3428234X
快递公司：京东物流
仓库：YCZNWL 真爱智慧
发货人：张三
备注：送货上门，司机自提
状态：运输中 / 已签收 / 取消
操作：[换货] [打印] [撤销]
```

#### 3.2.3 物流追踪信息管理
**功能描述**：展示订单的物流跟踪和收货信息

**追踪信息**：
- **收货人信息**：姓名、联系电话、详细地址
- **签收信息**：未签收/已签收时间/签收人
- **物流状态**：实时物流状态更新
- **异常处理**：物流异常情况的处理记录

### 3.3 发货操作控制功能

#### 3.3.1 待发货商品管理
**功能描述**：管理本次发货的商品信息和数量

**商品信息**：
- **商品编号/名称**：商品的基本标识信息
- **发货数量**：本次准备发出的数量，支持修改
- **出库仓库**：系统默认指定或下拉选择
- **库位信息**：子仓库位置信息（可为空）
- **库存验证**：实时验证库存充足性

#### 3.3.2 收货信息管理
**功能描述**：管理和确认客户收货信息

**收货信息**：
- **收货人**：自动读取订单信息，支持修改
- **收货地址**：精确至门牌号，支持复制和修改
- **联系电话**：客户联系电话，支持修改
- **收货备注**：可填写司机/快递特殊要求

#### 3.3.3 物流信息录入
**功能描述**：录入和管理发货的物流信息

**物流信息字段**：
- **物流方式**：普通快递、上门自提、其他方式
- **快递公司**：支持选择常用快递（京东、顺丰、圆通、中通等）
- **快递单号**：人工录入或扫码枪填入，系统可自动识别并校验
- **发货仓库**：默认系统填充，可根据商品库存自动推荐，支持下拉修改
- **发货人**：填写实际操作发货的人员姓名，便于责任追溯
- **发货备注**：填写与本次发货相关的备注信息
  - 特殊包装要求（如"易碎品"、"防潮包装"）
  - 客户指定送货时间（如"工作日送货"、"周末配送"）
  - 配送特殊要求（如"需电话联系"、"送货上门"）
  - 其他注意事项

#### 3.3.4 发货执行操作
**功能描述**：执行最终的发货确认和相关操作

**操作功能**：
- **确认发货**：提交发货信息，执行完整的发货流程
  - 保存发货信息并生成发货记录
  - 自动更新订单状态和库存数量
  - 生成发货批次记录
  - 发送发货通知给客户
- **保存物流信息**：临时存档物流信息，便于后续继续操作
  - 保存当前填写的所有物流信息
  - 支持稍后继续编辑和发货
  - 避免重复录入信息
- **打印快递单**：生成和打印快递面单
  - 对接电子面单系统，自动生成标准格式
  - 支持批量打印多个订单的快递单
  - 支持不同快递公司的面单格式
  - 自动填充收发货信息
- **预览打印模板**：查看快递单打印效果
  - 展示快递面单的预览效果
  - 供用户确认信息准确性
  - 支持打印前的最后检查

### 3.4 换货流程管理功能

#### 3.4.1 换货操作流程
**功能描述**：处理客户商品换货的完整流程

**换货操作步骤**：
1. **换货申请发起**：从发货批次记录中点击"换货"按钮
2. **换货原因确认**：选择换货原因（质量问题、规格不符、客户需求变更等）
3. **换货商品选择**：选择需要换货的商品和数量
4. **新商品确认**：确认客户需要的新商品信息
5. **换货单生成**：系统自动生成新的发货单
6. **原订单关联**：确保换货操作与原订单的可追溯性

**换货类型**：
- **部分商品换货**：仅对订单中的部分商品进行换货
- **全部商品换货**：对整个批次的所有商品进行换货
- **同款换货**：换货相同商品（如质量问题）
- **异款换货**：换货不同商品（如规格变更）

#### 3.4.2 退货处理管理功能

**退货入库操作**：
- **正常退货入库**：客户主动发起的退货申请处理
  - 需要填写退货原因和退货数量
  - 系统自动更新库存和订单状态
  - 生成退货入库记录
- **退回入库操作**：因各种原因退回的商品处理
  - 快递拒收、地址错误等情况的处理
  - 支持批量处理多个商品的退回
  - 自动恢复库存并更新发货状态

**退货记录管理**：
- **退货时间**：客户发起退货的时间
- **退货原因**：客户退货的具体原因分类
- **退货数量**：退回的商品数量
- **处理人员**：负责处理退货的员工
- **处理结果**：退货处理的最终结果
- **备注说明**：退货处理的详细说明

### 3.5 数据统计分析功能

#### 3.5.1 发货数据统计
**功能描述**：发货相关数据的统计和分析

**统计指标**：
- **发货订单总数**：当前筛选条件下的发货订单总量
- **发货商品总数**：已发货商品的总数量
- **发货完成率**：已完成发货的订单比例
- **平均发货时间**：从订单生成到发货完成的平均时间
- **退货率统计**：退货订单占总订单的比例
- **物流公司分布**：各快递公司的使用比例

#### 3.5.2 发货效率分析
**功能描述**：发货效率和质量的分析

**分析维度**：
- **发货时效分析**：按时间维度分析发货效率趋势
- **仓库发货分析**：各仓库的发货量和效率对比
- **人员效率分析**：发货人员的工作量和效率统计
- **异常情况分析**：发货异常的原因分析和改进建议

## 4. 用户界面设计

### 4.1 页面布局设计

#### 4.1.1 货单列表页面布局
- **筛选搜索区**：页面顶部，提供多条件筛选功能
- **货单列表区**：页面主体，展示货单列表和基本信息
- **分页导航区**：页面底部，提供分页浏览功能
- **操作按钮区**：列表右侧，提供发货、详情等操作

#### 4.1.2 发货详情页面布局
- **订单信息区**：页面顶部，展示订单基本信息
- **商品记录区**：页面上半部分，展示商品发货记录
- **批次明细区**：页面中部，展示发货批次明细
- **物流信息区**：页面下部，展示物流追踪信息

#### 4.1.3 发货操作页面布局
- **商品信息区**：页面顶部，展示待发货商品信息
- **收货信息区**：页面左侧，展示和编辑收货信息
- **物流信息区**：页面右侧，录入物流信息
- **操作按钮区**：页面底部，提供发货执行操作

### 4.2 交互设计规范
- **状态标识**：使用颜色和图标清晰标识发货状态
- **操作反馈**：操作后提供明确的成功或失败反馈
- **数据验证**：输入数据的实时验证和错误提示
- **快捷操作**：支持扫码录入和批量操作
- **信息联动**：页面间信息的自动关联和更新

### 4.3 响应式设计
- **PC端**：完整功能展示，多区域布局
- **平板端**：适配中等屏幕，关键功能优先
- **移动端**：简化布局，核心功能保留

## 5. 异常处理

### 5.1 业务异常处理

#### 5.1.1 发货流程异常
**异常类型**：
- **库存不足**：发货时库存数量不足的处理
- **地址异常**：收货地址不准确或无法送达
- **快递异常**：快递丢失、损坏或延误的处理
- **客户拒收**：客户拒绝签收的处理流程

**处理机制**：
- **库存预警**：发货前进行库存充足性验证
- **地址验证**：发货前确认收货地址的准确性
- **物流跟踪**：实时跟踪物流状态，及时发现异常
- **客户沟通**：异常情况及时与客户沟通协调

#### 5.1.2 退货处理异常
**异常类型**：
- **退货商品不符**：退回商品与原订单不符
- **退货时效超期**：超过退货时效期限的处理
- **退货商品损坏**：退回商品存在损坏的处理
- **退货争议**：退货原因存在争议的处理

**处理流程**：
- **商品核验**：严格核验退回商品的一致性
- **时效管理**：建立退货时效管理机制
- **损坏评估**：专业评估退货商品的损坏程度
- **争议调解**：建立退货争议的调解机制

### 5.2 系统异常处理

#### 5.2.1 数据异常
**异常类型**：
- **数据丢失**：发货记录数据丢失的恢复
- **数据不一致**：发货数据与库存数据不一致
- **并发冲突**：多人同时操作同一订单的处理

**处理机制**：
- **数据备份**：定期备份发货相关数据
- **数据同步**：确保各系统间数据的一致性
- **冲突检测**：检测并处理并发操作冲突

#### 5.2.2 系统故障
**异常类型**：
- **网络异常**：网络连接异常的处理
- **打印异常**：快递单打印异常的处理
- **接口异常**：与快递公司接口异常的处理

**处理机制**：
- **自动重试**：网络异常时自动重试机制
- **备用方案**：打印异常时提供备用打印方案
- **降级服务**：接口异常时提供降级服务

---

**文档版本**：v1.0
**编写日期**：2025-07-03
**编写人员**：AI系统架构师
**审核状态**：待审核
