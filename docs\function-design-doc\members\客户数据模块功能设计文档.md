# SaaS智能家装CRM系统 - 客户数据模块功能设计文档

## 1. 模块概述

### 1.1 模块目的
客户数据模块是SaaS智能家装CRM系统的数据展示和分析核心，基于image2.png界面设计，提供客户信息的统一展示、多维度筛选搜索、数据统计分析和批量操作功能。该模块以表格形式展示所有客户信息，支持高效的数据管理和业务决策。

### 1.2 业务价值
- 提供客户数据的统一视图，支持快速查找和定位客户信息
- 建立多维度的数据筛选体系，提升数据查询效率
- 实现关键业务指标的实时统计，支持经营决策分析
- 支持客户数据的批量操作，提高工作效率
- 提供数据导入导出功能，支持数据迁移和备份

### 1.3 功能架构
客户数据模块包含五个核心功能：
- **数据统计概览**: 关键业务指标的统计展示
- **筛选搜索功能**: 多维度的数据筛选和搜索
- **客户数据列表**: 客户信息的表格化展示
- **数据操作功能**: 单行和批量数据操作
- **界面交互设计**: 优化的用户体验设计

## 2. 客户数据模块操作流程图

```mermaid
flowchart TD
    A[用户访问客户数据页面] --> B[权限验证]
    B --> C{权限验证通过?}
    C -->|否| D[显示权限不足提示]
    C -->|是| E[加载页面数据]

    E --> F[显示统计概览]
    E --> G[显示筛选条件]
    E --> H[加载客户数据列表]

    F --> I[展示关键指标卡片]
    I --> J[实时更新统计数据]

    G --> K[基础筛选条件]
    G --> L[高级筛选条件]
    K --> M[姓名/手机号搜索]
    K --> N[状态/门店筛选]
    L --> O[时间范围筛选]
    L --> P[客户类别/经理筛选]

    M --> Q[实时搜索处理]
    N --> Q
    O --> Q
    P --> Q

    Q --> R[防抖处理]
    R --> S[发送筛选请求]
    S --> T[更新数据列表]

    H --> U[表格数据展示]
    U --> V[分页处理]
    U --> W[排序功能]
    U --> X[列自定义]

    T --> U

    U --> Y[单行操作]
    U --> Z[批量操作]

    Y --> AA[查看详情]
    Y --> BB[客户管理]
    AA --> CC[跳转详情页面]
    BB --> DD[跳转管理页面]

    Z --> EE[选择客户记录]
    EE --> FF[批量导出]
    EE --> GG[批量状态修改]
    EE --> HH[批量分配]

    FF --> II[选择导出字段]
    II --> JJ[生成导出文件]

    GG --> KK[选择目标状态]
    KK --> LL[确认批量操作]
    LL --> MM[记录操作日志]

    HH --> NN[选择分配对象]
    NN --> OO[确认分配操作]
    OO --> PP[发送分配通知]

    style A fill:#e1f5fe
    style C fill:#fff3e0
    style D fill:#ffebee
    style Q fill:#f3e5f5
    style U fill:#e8f5e8
```

### 流程说明
客户数据模块的操作流程主要包含以下几个核心环节：

1. **权限验证与页面加载**：用户访问页面时首先进行权限验证，验证通过后加载统计概览、筛选条件和数据列表
2. **数据筛选与搜索**：支持基础筛选（姓名、手机号、状态、门店）和高级筛选（时间范围、客户类别、经理等），采用防抖处理实时更新结果
3. **数据展示与操作**：以表格形式展示客户数据，支持分页、排序、列自定义等功能
4. **单行与批量操作**：提供查看详情、客户管理等单行操作，以及批量导出、状态修改、分配等批量操作
5. **统计数据实时更新**：关键业务指标实时更新，为管理决策提供数据支持

## 3. 详细功能设计

### 2.1 数据统计概览功能

#### 2.1.1 关键指标展示
**功能描述**: 在页面顶部以卡片形式展示核心业务指标

**统计指标**:
- **客户总数**: 系统中所有客户的总数量
  - 实时统计当前系统客户总数
  - 包含所有状态的客户
  - 支持按门店筛选统计
- **总消费额**: 所有客户的累计消费金额
  - 历史累计消费总金额
  - 包含所有消费类型
  - 支持时间范围筛选
- **总剩余金额**: 所有客户账户的剩余金额总和
  - 客户账户余额汇总
  - 实时更新余额变化
  - 支持按门店分组统计
- **活跃客户数**: 近期有消费或活动的客户数量
  - 最近30天有消费记录的客户
  - 最近30天有登录记录的客户
  - 支持活跃度时间范围设置

#### 2.1.2 统计卡片设计
**功能描述**: 统计数据的可视化展示设计

**卡片样式**:
- **渐变色彩**: 采用不同渐变色区分指标类型
  - 客户总数：蓝色渐变
  - 总消费额：绿色渐变
  - 总剩余金额：橙色渐变
  - 活跃客户数：紫色渐变
- **图标标识**: 每个指标配备直观的图标
  - 用户图标、金钱图标、钱包图标、活跃图标
- **数值展示**: 大字号突出显示关键数值
  - 主数值：大字号粗体显示
  - 单位标识：小字号显示
  - 趋势指示：箭头和百分比显示变化
- **描述信息**: 提供指标的详细说明
  - 统计范围说明
  - 更新时间显示
  - 对比数据展示

#### 2.1.3 数据实时更新
**功能描述**: 统计数据的实时更新机制

**更新机制**:
- **自动刷新**: 页面数据定时自动刷新
  - 每5分钟自动更新一次
  - 支持手动刷新按钮
- **增量更新**: 只更新变化的数据
  - 减少服务器负载
  - 提升页面响应速度
- **缓存机制**: 合理使用数据缓存
  - 缓存统计结果
  - 设置合理的缓存过期时间

### 2.2 筛选搜索功能

#### 2.2.1 基础筛选功能
**功能描述**: 提供常用的筛选条件，默认显示在页面上

**基础筛选字段**:
- **客户姓名**: 客户姓名的模糊搜索
  - 支持拼音搜索
  - 支持部分姓名匹配
  - 实时搜索结果更新
- **手机号码**: 手机号的精确和模糊搜索
  - 支持完整手机号搜索
  - 支持手机号后四位搜索
  - 支持手机号段搜索
- **客户状态**: 按客户状态筛选
  - 正常：正常活跃客户
  - 冻结：临时冻结的客户
  - 黑名单：黑名单客户
  - VIP：VIP客户
  - 全部：显示所有状态客户
- **所属门店**: 按门店进行筛选
  - 下拉选择门店
  - 支持多门店选择
  - 权限控制门店范围

#### 2.2.2 高级筛选功能
**功能描述**: 提供更多筛选条件，支持收起/展开切换

**高级筛选字段**:
- **发卡时间段**: 客户注册时间范围筛选
  - 起始日期选择器
  - 结束日期选择器
  - 快捷时间范围选择（今天、本周、本月、本年）
- **客户类别**: 按客户分类筛选
  - 服务商客户
  - 品牌家装客户
  - 个人客户
  - 企业客户
- **客户经理**: 按负责的客户经理筛选
  - 下拉选择客户经理
  - 支持客户经理姓名搜索
  - 显示每个经理的客户数量
- **跟进人**: 按跟进人员筛选
  - 下拉选择跟进人员
  - 支持跟进人姓名搜索
  - 显示每个跟进人的客户数量
- **跟进状态**: 跟进状态筛选
  - 待跟进：新分配未跟进的客户
  - 跟进中：正在跟进的客户
  - 已完成：跟进完成的客户
  - 暂停跟进：暂时停止跟进的客户
- **卡号搜索**: 客户卡号的精确搜索
  - 支持完整卡号搜索
  - 支持卡号部分匹配
- **公司单位**: 公司名称的模糊搜索
  - 支持公司名称关键词搜索
  - 支持公司简称搜索

#### 2.2.3 筛选功能特性
**功能描述**: 筛选功能的交互特性和用户体验

**交互特性**:
- **收起/展开**: 高级筛选区域支持收起展开
  - 默认收起状态
  - 点击"高级筛选"按钮展开
  - 记住用户的展开偏好
- **多条件组合**: 支持多个筛选条件同时使用
  - 条件之间为AND关系
  - 实时显示筛选结果数量
- **实时搜索**: 输入筛选条件后实时更新结果
  - 防抖处理，避免频繁请求
  - 搜索过程中显示加载状态
- **条件保存**: 筛选条件的保存和重置
  - 保存常用筛选条件
  - 一键重置所有筛选条件
  - 显示当前筛选条件数量

### 2.3 客户数据列表展示功能

#### 2.3.1 必须展示字段
**功能描述**: 客户数据表格中必须显示的核心字段

**核心字段**:
- **客户姓名**: 客户的真实姓名
  - 左对齐显示
  - 支持点击查看详情
  - VIP客户显示特殊标识
- **客户卡号**: 系统生成的唯一客户卡号
  - 等宽字体显示
  - 支持复制功能
  - 点击可快速搜索
- **客户类别**: 客户分类标识
  - 标签形式显示
  - 不同类别使用不同颜色
  - 支持点击筛选同类别客户
- **分店**: 客户所属门店信息
  - 显示门店简称
  - 支持点击筛选同门店客户
- **客户经理**: 负责该客户的客户经理
  - 显示经理姓名
  - 支持点击查看经理信息
  - 支持点击筛选同经理客户
- **状态**: 客户当前状态
  - 状态标签显示
  - 正常：绿色标签
  - 冻结：灰色标签
  - 黑名单：红色标签
  - VIP：金色标签

#### 2.3.2 扩展展示字段
**功能描述**: 可选显示的扩展字段信息

**扩展字段**:
- **手机号码**: 客户联系电话
  - 部分隐藏显示（如：138****1234）
  - 有权限用户显示完整号码
  - 支持一键拨号功能
- **注册时间**: 客户注册/发卡时间
  - 格式：YYYY-MM-DD
  - 支持按时间排序
  - 显示注册天数
- **最后消费**: 最近一次消费时间
  - 格式：YYYY-MM-DD
  - 超过30天显示警告色
  - 支持点击查看消费详情
- **账户余额**: 当前账户剩余金额
  - 货币格式显示
  - 余额不足显示警告
  - 支持点击查看余额详情
- **累计消费**: 历史累计消费金额
  - 货币格式显示
  - 支持按金额排序
  - 显示消费等级
- **积分余额**: 当前积分余额
  - 整数显示
  - 支持积分兑换入口
  - 显示积分等级

#### 2.3.3 表格功能特性
**功能描述**: 表格的交互功能和用户体验

**功能特性**:
- **列排序**: 支持按列进行排序
  - 点击列标题进行排序
  - 支持升序/降序切换
  - 显示排序指示器
- **列自定义**: 支持列的显示/隐藏设置
  - 列设置按钮
  - 拖拽调整列顺序
  - 保存用户列设置偏好
- **数据导出**: 支持表格数据导出
  - Excel格式导出
  - CSV格式导出
  - 支持按筛选条件导出
- **分页显示**: 大数据量的分页处理
  - 每页显示数量可设置（10/20/50/100）
  - 页码跳转功能
  - 显示总记录数和当前页信息
- **行选择**: 支持单行和多行选择
  - 复选框选择
  - 全选/反选功能
  - 显示已选择数量

### 2.4 数据操作功能

#### 2.4.1 单行操作功能
**功能描述**: 针对单个客户的操作功能

**操作按钮**:
- **查看详情**: 跳转到客户详细信息页面
  - 新窗口打开详情页
  - 显示客户完整信息
  - 支持快速返回列表
- **管理**: 跳转到客户详情管理页面
  - 进入客户管理界面
  - 支持客户信息编辑
  - 支持各种业务操作

#### 2.4.2 批量操作功能
**功能描述**: 针对多个客户的批量操作

**批量操作**:
- **批量导出**: 导出选中客户的数据
  - 选择导出字段
  - 选择导出格式
  - 生成导出文件
- **批量修改状态**: 批量更改客户状态
  - 选择目标状态
  - 确认批量操作
  - 记录操作日志
- **批量分配**: 批量分配客户经理或跟进人
  - 选择分配对象
  - 确认分配操作
  - 发送分配通知
- **批量发送**: 批量发送通知或消息
  - 选择消息模板
  - 编辑消息内容
  - 发送消息并记录

#### 2.4.3 数据管理功能
**功能描述**: 客户数据的导入导出和管理

**数据管理**:
- **数据导入**: Excel格式的客户数据批量导入
  - 下载导入模板
  - 上传Excel文件
  - 数据验证和预览
  - 批量创建客户
- **数据导出**: 按筛选条件导出客户数据
  - 选择导出字段
  - 选择导出格式
  - 设置导出条件
  - 生成并下载文件
- **数据备份**: 定期数据备份功能
  - 自动备份设置
  - 手动备份操作
  - 备份文件管理
- **数据统计**: 生成各类数据统计报表
  - 客户分布统计
  - 消费统计分析
  - 增长趋势分析
  - 自定义统计报表

### 2.5 界面交互设计功能

#### 2.5.1 响应式设计
**功能描述**: 适配不同设备的响应式布局

**设备适配**:
- **PC端**: 多列表格布局
  - 充分利用屏幕宽度
  - 显示所有核心字段
  - 支持多列排序和筛选
- **平板端**: 自适应布局调整
  - 隐藏部分非核心字段
  - 优化触控操作体验
  - 调整按钮大小和间距
- **移动端**: 卡片式布局
  - 单列卡片显示
  - 显示最核心的客户信息
  - 优化触控操作和滑动体验

#### 2.5.2 交互体验优化
**功能描述**: 提升用户操作体验的交互设计

**体验优化**:
- **筛选预览**: 筛选条件的实时预览
  - 显示筛选结果数量
  - 预览筛选效果
  - 提供筛选建议
- **加载状态**: 数据加载的状态提示
  - 表格加载骨架屏
  - 搜索加载指示器
  - 操作进度提示
- **操作反馈**: 操作确认和结果反馈
  - 操作前确认对话框
  - 操作成功/失败提示
  - 操作结果详细说明
- **快捷键**: 键盘快捷键支持
  - Ctrl+F：快速搜索
  - Ctrl+A：全选
  - Ctrl+E：导出数据
  - ESC：取消当前操作

#### 2.5.3 性能优化
**功能描述**: 大数据量下的性能优化

**优化策略**:
- **分页加载**: 大数据量的分页处理
  - 服务端分页
  - 懒加载机制
  - 预加载下一页数据
- **搜索防抖**: 搜索输入的防抖处理
  - 300ms防抖延迟
  - 避免频繁请求
  - 取消无效请求
- **虚拟滚动**: 表格的虚拟滚动支持
  - 只渲染可见行
  - 减少DOM节点数量
  - 提升滚动性能
- **数据缓存**: 合理的数据缓存机制
  - 缓存搜索结果
  - 缓存用户设置
  - 智能缓存更新

## 3. 用户界面设计

### 3.1 页面布局设计
- **统计概览区**: 顶部关键指标卡片展示
- **筛选搜索区**: 多维度筛选条件面板
- **数据表格区**: 客户信息表格展示
- **操作工具栏**: 批量操作和功能按钮

### 3.2 交互设计规范
- **统计卡片**: 渐变色卡片突出展示
- **筛选面板**: 收起/展开的筛选面板
- **表格交互**: 排序、分页、列自定义
- **快速操作**: 行内操作和批量工具栏

### 3.3 色彩和图标规范
- **主色调**: #1890FF（蓝色）
- **成功色**: #52C41A（绿色）
- **警告色**: #FAAD14（黄色）
- **危险色**: #FF4D4F（红色）
- **中性色**: #8C8C8C（灰色）

## 4. 权限控制

### 4.1 数据权限
- **门店隔离**: 只能查看本门店客户数据
- **跟进人权限**: 销售人员主要查看自己跟进的客户
- **敏感信息**: 财务信息需要特殊权限查看

### 4.2 操作权限
- **查看权限**: 客户数据查看权限
- **导出权限**: 数据导出权限控制
- **批量操作**: 批量操作权限控制
- **管理权限**: 客户管理页面访问权限

## 5. 异常处理

### 5.1 数据异常
- **加载失败**: 数据加载失败的重试机制
- **搜索异常**: 搜索超时或失败的处理
- **导出异常**: 数据导出失败的处理

### 5.2 操作异常
- **权限不足**: 权限不足的友好提示
- **网络异常**: 网络连接异常的处理
- **并发冲突**: 数据并发修改的冲突处理

---

**文档版本**: v1.0
**编写日期**: 2025-07-01
**编写人员**: AI系统架构师
**审核状态**: 待审核
