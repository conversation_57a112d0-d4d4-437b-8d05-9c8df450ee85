package com.muzi.yichao.module.customer.service.info;

import java.util.*;
import jakarta.validation.*;
import com.muzi.yichao.module.customer.controller.admin.info.vo.*;
import com.muzi.yichao.module.customer.dal.dataobject.info.InfoDO;
import com.muzi.yichao.module.customer.dal.dataobject.info.InfoAccountLogDO;
import com.muzi.yichao.module.customer.dal.dataobject.info.InfoAddressDO;
import com.muzi.yichao.module.customer.dal.dataobject.info.InfoInvoiceInfoDO;
import com.muzi.yichao.module.customer.dal.dataobject.info.InfoPointsRecordDO;
import com.muzi.yichao.module.customer.dal.dataobject.info.InfoReferralDO;
import com.muzi.yichao.module.customer.dal.dataobject.info.InfoStatusLogDO;
import com.muzi.yichao.framework.common.pojo.PageResult;
import com.muzi.yichao.framework.common.pojo.PageParam;

/**
 * 客户信息 Service 接口
 *
 * <AUTHOR>
 */
public interface InfoService {

    /**
     * 创建客户信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInfo(@Valid InfoSaveReqVO createReqVO);

    /**
     * 更新客户信息
     *
     * @param updateReqVO 更新信息
     */
    void updateInfo(@Valid InfoSaveReqVO updateReqVO);

    /**
     * 删除客户信息
     *
     * @param id 编号
     */
    void deleteInfo(Long id);

    /**
    * 批量删除客户信息
    *
    * @param ids 编号
    */
    void deleteInfoListByIds(List<Long> ids);

    /**
     * 获得客户信息
     *
     * @param id 编号
     * @return 客户信息
     */
    InfoDO getInfo(Long id);

    /**
     * 获得客户信息分页
     *
     * @param pageReqVO 分页查询
     * @return 客户信息分页
     */
    PageResult<InfoDO> getInfoPage(InfoPageReqVO pageReqVO);

    // ==================== 子表（客户账户操作日志） ====================

    /**
     * 获得客户账户操作日志分页
     *
     * @param pageReqVO 分页查询
     * @param customerId 客户ID
     * @return 客户账户操作日志分页
     */
    PageResult<InfoAccountLogDO> getInfoAccountLogPage(PageParam pageReqVO, Long customerId);

    /**
     * 创建客户账户操作日志
     *
     * @param infoAccountLog 创建信息
     * @return 编号
     */
    Long createInfoAccountLog(@Valid InfoAccountLogDO infoAccountLog);

    /**
     * 更新客户账户操作日志
     *
     * @param infoAccountLog 更新信息
     */
    void updateInfoAccountLog(@Valid InfoAccountLogDO infoAccountLog);

    /**
     * 删除客户账户操作日志
     *
     * @param id 编号
     */
    void deleteInfoAccountLog(Long id);

    /**
    * 批量删除客户账户操作日志
    *
    * @param ids 编号
    */
    void deleteInfoAccountLogListByIds(List<Long> ids);

	/**
	 * 获得客户账户操作日志
	 *
	 * @param id 编号
     * @return 客户账户操作日志
	 */
    InfoAccountLogDO getInfoAccountLog(Long id);

    // ==================== 子表（客户收货地址） ====================

    /**
     * 获得客户收货地址分页
     *
     * @param pageReqVO 分页查询
     * @param customerId 客户ID
     * @return 客户收货地址分页
     */
    PageResult<InfoAddressDO> getInfoAddressPage(PageParam pageReqVO, Long customerId);

    /**
     * 创建客户收货地址
     *
     * @param infoAddress 创建信息
     * @return 编号
     */
    Long createInfoAddress(@Valid InfoAddressDO infoAddress);

    /**
     * 更新客户收货地址
     *
     * @param infoAddress 更新信息
     */
    void updateInfoAddress(@Valid InfoAddressDO infoAddress);

    /**
     * 删除客户收货地址
     *
     * @param id 编号
     */
    void deleteInfoAddress(Long id);

    /**
    * 批量删除客户收货地址
    *
    * @param ids 编号
    */
    void deleteInfoAddressListByIds(List<Long> ids);

	/**
	 * 获得客户收货地址
	 *
	 * @param id 编号
     * @return 客户收货地址
	 */
    InfoAddressDO getInfoAddress(Long id);

    // ==================== 子表（客户发票信息） ====================

    /**
     * 获得客户发票信息分页
     *
     * @param pageReqVO 分页查询
     * @param customerId 客户ID
     * @return 客户发票信息分页
     */
    PageResult<InfoInvoiceInfoDO> getInfoInvoiceInfoPage(PageParam pageReqVO, Long customerId);

    /**
     * 创建客户发票信息
     *
     * @param infoInvoiceInfo 创建信息
     * @return 编号
     */
    Long createInfoInvoiceInfo(@Valid InfoInvoiceInfoDO infoInvoiceInfo);

    /**
     * 更新客户发票信息
     *
     * @param infoInvoiceInfo 更新信息
     */
    void updateInfoInvoiceInfo(@Valid InfoInvoiceInfoDO infoInvoiceInfo);

    /**
     * 删除客户发票信息
     *
     * @param id 编号
     */
    void deleteInfoInvoiceInfo(Long id);

    /**
    * 批量删除客户发票信息
    *
    * @param ids 编号
    */
    void deleteInfoInvoiceInfoListByIds(List<Long> ids);

	/**
	 * 获得客户发票信息
	 *
	 * @param id 编号
     * @return 客户发票信息
	 */
    InfoInvoiceInfoDO getInfoInvoiceInfo(Long id);

    // ==================== 子表（客户积分记录） ====================

    /**
     * 获得客户积分记录分页
     *
     * @param pageReqVO 分页查询
     * @param customerId 客户ID
     * @return 客户积分记录分页
     */
    PageResult<InfoPointsRecordDO> getInfoPointsRecordPage(PageParam pageReqVO, Long customerId);

    /**
     * 创建客户积分记录
     *
     * @param infoPointsRecord 创建信息
     * @return 编号
     */
    Long createInfoPointsRecord(@Valid InfoPointsRecordDO infoPointsRecord);

    /**
     * 更新客户积分记录
     *
     * @param infoPointsRecord 更新信息
     */
    void updateInfoPointsRecord(@Valid InfoPointsRecordDO infoPointsRecord);

    /**
     * 删除客户积分记录
     *
     * @param id 编号
     */
    void deleteInfoPointsRecord(Long id);

    /**
    * 批量删除客户积分记录
    *
    * @param ids 编号
    */
    void deleteInfoPointsRecordListByIds(List<Long> ids);

	/**
	 * 获得客户积分记录
	 *
	 * @param id 编号
     * @return 客户积分记录
	 */
    InfoPointsRecordDO getInfoPointsRecord(Long id);

    // ==================== 子表（客户推荐关系） ====================

    /**
     * 获得客户推荐关系分页
     *
     * @param pageReqVO 分页查询
     * @param referrerId 推荐人ID
     * @return 客户推荐关系分页
     */
    PageResult<InfoReferralDO> getInfoReferralPage(PageParam pageReqVO, Long referrerId);

    /**
     * 创建客户推荐关系
     *
     * @param infoReferral 创建信息
     * @return 编号
     */
    Long createInfoReferral(@Valid InfoReferralDO infoReferral);

    /**
     * 更新客户推荐关系
     *
     * @param infoReferral 更新信息
     */
    void updateInfoReferral(@Valid InfoReferralDO infoReferral);

    /**
     * 删除客户推荐关系
     *
     * @param id 编号
     */
    void deleteInfoReferral(Long id);

    /**
    * 批量删除客户推荐关系
    *
    * @param ids 编号
    */
    void deleteInfoReferralListByIds(List<Long> ids);

	/**
	 * 获得客户推荐关系
	 *
	 * @param id 编号
     * @return 客户推荐关系
	 */
    InfoReferralDO getInfoReferral(Long id);

    // ==================== 子表（客户状态变更记录） ====================

    /**
     * 获得客户状态变更记录分页
     *
     * @param pageReqVO 分页查询
     * @param customerId 客户ID
     * @return 客户状态变更记录分页
     */
    PageResult<InfoStatusLogDO> getInfoStatusLogPage(PageParam pageReqVO, Long customerId);

    /**
     * 创建客户状态变更记录
     *
     * @param infoStatusLog 创建信息
     * @return 编号
     */
    Long createInfoStatusLog(@Valid InfoStatusLogDO infoStatusLog);

    /**
     * 更新客户状态变更记录
     *
     * @param infoStatusLog 更新信息
     */
    void updateInfoStatusLog(@Valid InfoStatusLogDO infoStatusLog);

    /**
     * 删除客户状态变更记录
     *
     * @param id 编号
     */
    void deleteInfoStatusLog(Long id);

    /**
    * 批量删除客户状态变更记录
    *
    * @param ids 编号
    */
    void deleteInfoStatusLogListByIds(List<Long> ids);

	/**
	 * 获得客户状态变更记录
	 *
	 * @param id 编号
     * @return 客户状态变更记录
	 */
    InfoStatusLogDO getInfoStatusLog(Long id);

}