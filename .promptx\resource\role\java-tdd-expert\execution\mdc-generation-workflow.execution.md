<execution>
<constraint>
## 文档生成的客观技术限制
- **8章节结构强制要求**：必须严格按照文档定义的8个章节结构生成
- **Java语言特定约束**：只能分析Java源代码，不支持其他语言
- **静态分析限制**：只能基于源代码进行分析，无法获取运行时行为
- **Markdown格式要求**：输出必须符合Markdown格式规范
- **信息完整性要求**：每个章节都必须有实质性内容，不能为空
</constraint>

<rule>
## .mdc文件生成的强制规则
- **完整性规则**：必须包含所有8个章节，每个章节内容不能为空
- **准确性规则**：所有技术信息必须基于实际代码，不能推测或假设
- **结构化规则**：严格按照文档定义的格式和结构输出
- **可操作性规则**：生成的文档必须能够直接支持TDD流程
- **标准化规则**：同一项目内的所有.mdc文件必须保持格式一致
</rule>

<guideline>
## 文档生成指导原则
- **信息层次清晰**：从宏观到微观，从概述到细节
- **业务优先**：业务规则和逻辑比技术实现更重要
- **测试友好**：所有描述都要考虑如何进行测试
- **可追溯性**：文档内容能够追溯到具体的代码行
- **自然语言表达**：业务规则用自然语言，技术细节用精确术语
</guideline>

<process>
## 标准化.mdc文件生成流程

### Step 1: 代码分析与信息收集
```mermaid
flowchart TD
    A[读取Java源文件] --> B[解析类结构]
    B --> C[识别方法和字段]
    C --> D[分析依赖关系]
    D --> E[提取注释和注解]
    E --> F[识别业务逻辑]
```

**执行要点**：
- 完整读取Java源文件内容
- 解析包声明、import语句、类定义
- 识别所有public方法和重要私有方法
- 分析内部依赖和外部依赖
- 提取JavaDoc注释和关键注解信息

### Step 2: 章节内容生成

#### 章节1-2：基础信息生成
```mermaid
graph LR
    A[文件路径分析] --> B[包结构识别]
    B --> C[架构定位]
    C --> D[职责分析]
    D --> E[目标确定]
```

**生成模板**：
```markdown
**一、文件概览与架构上下文**
* **文件名**: [从文件路径提取]
* **文件路径**: [完整相对路径]
* **所属包/模块**: [包路径和模块名]
* **项目目录规范引用**: [基于包结构推断]
* **文件定位理由**: [分析文件在架构中的位置]
* **简要描述**: [1-2句话概括核心功能]

**二、主要职责与目标**
* [基于类名、方法名、注释分析职责]
* [识别解决的业务问题]
* [明确在系统中的角色定位]
```

#### 章节3：核心规则与逻辑提取
```mermaid
graph TD
    A[方法体分析] --> B[业务规则识别]
    A --> C[数据校验提取]
    A --> D[算法逻辑分析]
    A --> E[状态机识别]
    A --> F[权限规则分析]
    A --> G[配置依赖识别]
```

**提取策略**：
- **业务规则**：从if-else、switch、循环逻辑中提取
- **数据校验**：从参数检查、注解、异常处理中识别
- **算法逻辑**：从计算方法、数据处理流程中抽象
- **状态机**：从枚举、状态转换代码中构建
- **权限规则**：从安全注解、权限检查代码中提取

#### 章节4-5：依赖与接口分析
```mermaid
flowchart LR
    A[Import分析] --> B[内部依赖识别]
    A --> C[外部依赖识别]
    D[方法签名分析] --> E[参数详述]
    D --> F[返回值分析]
    D --> G[异常处理]
```

**分析重点**：
- 区分内部依赖和外部依赖
- 详细描述每个public方法的完整签名
- 分析方法的前置条件和后置条件
- 识别所有可能的异常情况

#### 章节6-7：常量与最佳实践
```mermaid
graph TD
    A[常量字段识别] --> B[枚举类型分析]
    C[代码复杂度分析] --> D[性能瓶颈识别]
    C --> E[最佳实践建议]
    C --> F[注意事项提取]
```

#### 章节8：测试指引生成
```mermaid
flowchart TD
    A[核心规则] --> B[单元测试设计]
    C[接口方法] --> D[集成测试场景]
    E[业务逻辑] --> F[TDD步骤规划]
    G[依赖关系] --> H[Mock策略设计]
```

**测试设计原则**：
- 每个业务规则都要有对应的测试场景
- 每个public方法都要有完整的测试用例
- 异常路径和边界条件必须覆盖
- 提供具体的JUnit/TestNG伪代码示例

### Step 3: 文档质量检查与输出
```mermaid
graph TD
    A[内容完整性检查] --> B[格式规范检查]
    B --> C[技术准确性验证]
    C --> D[可操作性评估]
    D --> E[最终输出]
```

**质量检查清单**：
- [ ] 8个章节结构完整
- [ ] 每个章节内容充实
- [ ] 技术信息准确无误
- [ ] 业务规则清晰无歧义
- [ ] 测试指引具体可执行
- [ ] Markdown格式正确
- [ ] 能够支持TDD流程

## 特殊情况处理

### 接口文件处理
```markdown
- 重点分析接口定义和契约
- 详细描述实现要求
- 提供接口测试策略
```

### 抽象类处理
```markdown
- 分析抽象方法的设计意图
- 描述模板方法模式
- 提供子类实现指导
```

### 工具类处理
```markdown
- 重点分析静态方法
- 描述工具方法的使用场景
- 提供单元测试策略
```

### 配置类处理
```markdown
- 分析配置项的含义和影响
- 描述配置变更的影响范围
- 提供配置测试策略
```
</process>

<criteria>
## 文档质量评价标准

### 完整性标准
- ✅ 包含完整的8个章节结构
- ✅ 每个章节内容充实，没有空章节
- ✅ 所有public方法都有详细描述
- ✅ 核心业务规则完整提取

### 准确性标准
- ✅ 技术信息与代码实际行为一致
- ✅ 业务规则描述准确无歧义
- ✅ 依赖关系分析正确
- ✅ 接口定义完整准确

### 可操作性标准
- ✅ 能够直接支持需求分析
- ✅ 能够指导技术设计方案
- ✅ 能够生成测试用例
- ✅ 能够支持TDD开发流程

### 标准化标准
- ✅ 格式符合Markdown规范
- ✅ 结构符合8章节要求
- ✅ 术语使用一致
- ✅ 项目内文档格式统一

### TDD支持标准
- ✅ 每个业务规则都有对应的测试场景
- ✅ 提供具体的测试用例设计
- ✅ 包含完整的TDD步骤建议
- ✅ 提供Mock策略和测试数据构造方法
</criteria>
</execution>