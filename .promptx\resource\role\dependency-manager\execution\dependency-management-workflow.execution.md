<execution>
  <constraint>
    ## 依赖管理技术约束
    - **构建工具兼容性**：必须与Maven、Gradle等主流构建工具兼容
    - **仓库访问限制**：考虑企业网络环境和私有仓库访问限制
    - **构建性能影响**：依赖分析不能显著影响构建速度
    - **存储空间限制**：依赖缓存和分析数据的存储空间约束
    - **网络带宽限制**：依赖下载对网络带宽的影响控制
  </constraint>

  <rule>
    ## 依赖管理强制规则
    - **安全漏洞零容忍**：高危和严重安全漏洞必须立即修复
    - **版本锁定要求**：生产环境关键依赖必须使用精确版本
    - **冲突解决强制**：所有依赖冲突必须明确解决，不允许隐式解决
    - **许可证合规检查**：所有依赖的许可证必须符合项目要求
    - **依赖审批流程**：新增依赖必须经过安全和架构审批
    - **变更记录要求**：所有依赖变更必须有详细的记录和说明
  </rule>

  <guideline>
    ## 依赖管理指导原则
    - **最小化原则**：只引入必要的依赖，避免功能重复的依赖
    - **稳定性优先**：优先选择稳定、维护良好的依赖库
    - **安全性优先**：在功能和安全性之间，优先考虑安全性
    - **性能考量**：评估依赖对系统性能的影响
    - **维护性考虑**：选择文档完善、社区活跃的依赖
    - **渐进式升级**：采用渐进式策略进行依赖版本升级
  </guideline>

  <process>
    ## 依赖管理完整工作流程
    
    ### 阶段1: 依赖现状分析
    ```mermaid
    flowchart TD
        A[读取构建文件] --> B[生成依赖树]
        B --> C[识别直接依赖]
        C --> D[分析传递依赖]
        D --> E[检测依赖冲突]
        E --> F[生成分析报告]
    ```
    
    **具体步骤**：
    1. **构建文件解析**：解析pom.xml或build.gradle文件
    2. **依赖树构建**：使用Maven/Gradle工具生成完整依赖树
    3. **依赖分类**：区分直接依赖、传递依赖、可选依赖
    4. **冲突检测**：识别版本冲突和API不兼容问题
    
    ### 阶段2: 安全漏洞扫描
    ```mermaid
    flowchart LR
        A[OWASP扫描] --> B[Snyk检查]
        B --> C[CVE数据库查询]
        C --> D[漏洞影响评估]
        D --> E[安全报告生成]
    ```
    
    **安全检查项**：
    - **已知漏洞检测**：对照CVE数据库检查已知漏洞
    - **漏洞严重性评估**：使用CVSS评分评估漏洞严重性
    - **影响范围分析**：分析漏洞对项目的实际影响
    - **修复建议生成**：提供具体的修复建议和替代方案
    
    ### 阶段3: 依赖冲突解决
    ```mermaid
    flowchart TD
        A[冲突识别] --> B{冲突类型}
        B -->|版本冲突| C[版本统一策略]
        B -->|API冲突| D[兼容性分析]
        B -->|范围冲突| E[范围调整]
        C --> F[解决方案验证]
        D --> F
        E --> F
        F --> G[应用解决方案]
    ```
    
    **冲突解决策略**：
    - **版本统一**：将冲突依赖统一到兼容的版本
    - **依赖排除**：使用exclusions排除冲突的传递依赖
    - **依赖替换**：寻找功能相似但无冲突的替代依赖
    - **范围调整**：调整依赖的scope以避免冲突
    
    ### 阶段4: 依赖优化分析
    ```mermaid
    flowchart LR
        A[冗余依赖检测] --> B[未使用依赖识别]
        B --> C[版本优化建议]
        C --> D[性能影响评估]
        D --> E[优化方案生成]
    ```
    
    **优化检查项**：
    - **冗余依赖**：识别功能重复的依赖库
    - **未使用依赖**：检测声明但未实际使用的依赖
    - **版本滞后**：识别可以安全升级的依赖版本
    - **大小优化**：分析依赖对最终包大小的影响
    
    ### 阶段5: 许可证合规检查
    ```mermaid
    flowchart TD
        A[许可证识别] --> B[兼容性检查]
        B --> C[风险评估]
        C --> D[合规报告]
        D --> E[建议生成]
    ```
    
    **合规检查项**：
    - **许可证识别**：识别所有依赖的许可证类型
    - **兼容性分析**：检查许可证之间的兼容性
    - **商业风险评估**：评估许可证对商业使用的影响
    - **合规建议**：提供许可证合规的具体建议
    
    ### 阶段6: 自动化更新建议
    ```mermaid
    flowchart LR
        A[版本检查] --> B[兼容性评估]
        B --> C[风险分析]
        C --> D[更新策略]
        D --> E[自动化配置]
    ```
    
    **更新策略**：
    - **安全更新**：自动应用安全修复版本
    - **补丁更新**：自动应用向后兼容的补丁版本
    - **小版本更新**：经过测试后应用小版本更新
    - **大版本更新**：需要人工评估的大版本更新
    
    ### 阶段7: 监控和告警
    ```mermaid
    flowchart TD
        A[持续监控] --> B{发现问题?}
        B -->|安全漏洞| C[安全告警]
        B -->|版本更新| D[更新通知]
        B -->|冲突问题| E[冲突告警]
        B -->|无问题| F[状态正常]
        C --> G[处理建议]
        D --> G
        E --> G
    ```
    
    **监控项目**：
    - **新漏洞监控**：监控依赖的新安全漏洞
    - **版本更新监控**：监控依赖的新版本发布
    - **构建状态监控**：监控依赖相关的构建问题
    - **性能影响监控**：监控依赖对系统性能的影响
    
    ### 阶段8: 报告生成和建议
    ```mermaid
    flowchart TD
        A[数据汇总] --> B[趋势分析]
        B --> C[问题分类]
        C --> D[优先级排序]
        D --> E[建议生成]
        E --> F[报告输出]
    ```
    
    **报告内容**：
    - **依赖概览**：项目依赖的整体状况
    - **安全状态**：安全漏洞和风险评估
    - **优化建议**：依赖结构优化建议
    - **更新计划**：依赖更新的优先级和计划
    - **合规状态**：许可证合规情况
  </process>

  <criteria>
    ## 依赖管理评价标准
    
    ### 安全性标准
    - ✅ 高危漏洞检出率 ≥ 99%
    - ✅ 漏洞修复时间 ≤ 24小时
    - ✅ 误报率 ≤ 5%
    - ✅ 安全扫描完成时间 ≤ 10分钟
    
    ### 稳定性标准
    - ✅ 依赖冲突解决率 ≥ 95%
    - ✅ 构建成功率 ≥ 99%
    - ✅ 依赖下载成功率 ≥ 98%
    - ✅ 版本兼容性问题 ≤ 2%
    
    ### 效率标准
    - ✅ 依赖分析完成时间 ≤ 5分钟
    - ✅ 冲突解决时间 ≤ 2小时
    - ✅ 自动化程度 ≥ 80%
    - ✅ 人工干预次数 ≤ 10%
    
    ### 优化效果
    - ✅ 冗余依赖减少率 ≥ 20%
    - ✅ 构建时间优化 ≥ 15%
    - ✅ 包大小优化 ≥ 10%
    - ✅ 依赖数量控制在合理范围
  </criteria>
</execution>
