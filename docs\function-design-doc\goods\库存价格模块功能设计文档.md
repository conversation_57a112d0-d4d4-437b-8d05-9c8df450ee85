# 商品管理模块 - 库存价格功能设计文档

## 1. 模块概述

### 1.1 模块目的
库存价格模块是智能家装管理平台商品管理系统的核心模块，负责商品库存信息展示、价格管理、库存状态监控和商品启用状态控制。该模块为企业提供实时的库存数据查询、价格维护和库存预警功能，确保库存管理的准确性和时效性。

### 1.2 业务价值
- 提供实时库存数据查询和展示，支持多维度筛选和快速定位
- 建立完善的价格管理体系，支持批量价格调整和价格策略维护
- 实现库存安全值监控和预警，防范缺货风险和库存异常
- 支持商品启用状态管理，控制商品参与业务流程的范围
- 提供批量操作功能，提高库存和价格管理的工作效率

### 1.3 功能架构
库存价格模块包含五个核心功能：
- **库存信息查询**: 商品库存数据的多维度查询和展示
- **价格管理功能**: 商品销售价格的维护和批量调整
- **库存状态监控**: 挂单量、安全值等库存状态的实时监控
- **商品状态管理**: 商品启用/停用状态的控制和管理
- **批量操作功能**: 批量改价、启用、停用等高效操作

## 2. 库存价格模块操作流程图

```mermaid
flowchart TD
    A[用户访问库存价格页面] --> B[权限验证]
    B --> C{权限验证通过?}
    C -->|否| D[显示权限不足提示]
    C -->|是| E[加载库存价格数据]
    
    E --> F[显示库存统计信息]
    E --> G[显示筛选搜索区域]
    E --> H[加载商品库存列表]
    
    F --> I[库存总量统计]
    F --> J[库存异常提醒]
    I --> K[实时数据更新]
    J --> K
    
    G --> L[筛选条件设置]
    L --> M[商品编号筛选]
    L --> N[商品名称筛选]
    L --> O[分类筛选]
    L --> P[销售价筛选]
    
    M --> Q[组合筛选处理]
    N --> Q
    O --> Q
    P --> Q
    
    Q --> R[模糊匹配搜索]
    R --> S[点击搜索按钮]
    S --> T[发送筛选请求]
    T --> U[更新商品列表]
    
    H --> V[商品数据表格展示]
    V --> W[商品基本信息]
    V --> X[库存状态信息]
    V --> Y[价格信息]
    V --> Z[操作按钮]
    
    W --> AA[商品编号/名称/分类]
    X --> BB[挂单量/安全值显示]
    Y --> CC[销售价显示]
    Z --> DD[启用/停用切换]
    Z --> EE[编辑按钮]
    
    BB --> FF{库存状态检查}
    FF -->|安全值为0| GG[显示库存风险提醒]
    FF -->|挂单量异常| HH[显示挂单异常提醒]
    FF -->|正常| II[正常显示]
    
    DD --> JJ[状态切换操作]
    JJ --> KK{确认状态变更?}
    KK -->|否| LL[取消操作]
    KK -->|是| MM[更新商品状态]
    MM --> NN[同步后端数据]
    NN --> OO[刷新界面状态]
    
    EE --> PP{编辑权限检查}
    PP -->|无权限| QQ[显示权限不足]
    PP -->|有权限| RR[跳转编辑页面]
    
    U --> SS[批量操作功能]
    SS --> TT[选择商品记录]
    TT --> UU[批量改价]
    TT --> VV[批量启用]
    TT --> WW[批量停用]
    TT --> XX[数据导出]
    
    UU --> YY[价格调整界面]
    YY --> ZZ[输入新价格]
    ZZ --> AAA[价格验证]
    AAA --> BBB{价格验证通过?}
    BBB -->|否| CCC[显示价格错误提示]
    BBB -->|是| DDD[批量更新价格]
    
    VV --> EEE[批量启用确认]
    EEE --> FFF[更新商品状态]
    
    WW --> GGG[批量停用确认]
    GGG --> HHH[更新商品状态]
    
    XX --> III[选择导出字段]
    III --> JJJ[生成导出文件]
    
    DDD --> KKK[操作结果反馈]
    FFF --> KKK
    HHH --> KKK
    JJJ --> KKK
    
    KKK --> LLL[刷新页面数据]
    
    style A fill:#e1f5fe
    style C fill:#fff3e0
    style D fill:#ffebee
    style FF fill:#fff3e0
    style GG fill:#fff8e1
    style HH fill:#fff8e1
    style KK fill:#fff3e0
    style PP fill:#fff3e0
    style QQ fill:#ffebee
    style BBB fill:#fff3e0
    style CCC fill:#ffebee
    style KKK fill:#e8f5e8
```

### 流程说明
库存价格模块的操作流程主要包含以下几个核心环节：

1. **权限验证与数据加载**：验证用户访问权限，加载库存统计信息和商品列表数据
2. **多维度筛选查询**：支持商品编号、名称、分类、价格等多条件组合筛选，采用模糊匹配提升查询体验
3. **库存状态监控**：实时监控挂单量、安全值等关键指标，提供库存风险预警和异常提醒
4. **商品状态管理**：支持单个和批量的商品启用/停用操作，控制商品参与业务流程
5. **价格管理功能**：提供批量改价功能，支持价格验证和批量价格调整
6. **数据导出功能**：支持按筛选条件导出库存价格数据，满足数据分析需求

## 3. 详细功能设计

### 3.1 库存信息查询功能

#### 3.1.1 筛选搜索功能
**功能描述**: 提供多维度的商品库存信息筛选和搜索

**筛选条件**:
- **商品编号**: 支持精确匹配和模糊搜索
  - 精确匹配：输入完整商品编号
  - 模糊匹配：支持前缀匹配，如"BXP"匹配所有BXP开头的商品
  - 实时搜索提示
- **商品名称**: 商品名称的模糊搜索
  - 支持关键词搜索
  - 支持拼音搜索
  - 不区分大小写
- **商品分类**: 按商品类别进行筛选
  - 下拉选择商品分类
  - 支持多级分类筛选
  - 显示每个分类的商品数量
- **销售价格**: 按价格范围进行筛选
  - 价格区间选择
  - 支持自定义价格范围
  - 价格为空的商品筛选

**搜索特性**:
- **组合查询**: 支持多个筛选条件同时使用
- **实时搜索**: 输入条件后实时更新搜索建议
- **搜索历史**: 保存常用搜索条件
- **重置功能**: 一键清空所有筛选条件

#### 3.1.2 库存数据展示
**功能描述**: 以表格形式展示商品库存和价格信息

**数据字段**:
- **商品编号**: 系统内唯一标识
  - 支持点击复制
  - 支持快速搜索
- **商品名称**: 商品的描述性名称
  - 支持长名称的省略显示
  - 鼠标悬停显示完整名称
- **所属分类**: 商品类型分类
  - 显示完整分类路径
  - 支持点击筛选同类商品
- **计量单位**: 商品计量单位
  - 标准化单位显示
  - 支持单位转换
- **销售价格**: 商品销售单价
  - 支持多币种显示
  - 价格为空时显示"未设置"
  - 支持价格历史查看
- **挂单量**: 当前被订单锁定的库存量
  - 实时更新挂单状态
  - 挂单异常时高亮显示
- **安全值**: 安全库存阈值
  - 用于库存预警计算
  - 安全值为0时显示风险提示
- **商品型号**: 产品型号信息
  - 支持型号规格查询
- **启用状态**: 商品是否参与业务流程
  - 切换按钮控制
  - 状态变更日志记录

### 3.2 价格管理功能

#### 3.2.1 单个商品价格管理
**功能描述**: 单个商品的价格查看和维护

**价格信息**:
- **当前销售价**: 显示商品当前销售价格
- **价格历史**: 查看价格变更历史记录
- **成本价格**: 商品采购成本价格（需权限）
- **利润率**: 自动计算利润率（需权限）

**价格操作**:
- **价格编辑**: 支持内联编辑价格
- **价格验证**: 价格格式和范围验证
- **价格审批**: 价格变更审批流程（可配置）
- **价格生效**: 价格变更生效时间设置

#### 3.2.2 批量价格管理
**功能描述**: 批量商品的价格调整功能

**批量改价功能**:
- **商品选择**: 支持多选商品进行批量改价
  - 全选/反选功能
  - 按条件批量选择
  - 显示选中商品数量
- **价格调整方式**:
  - 固定价格：设置统一价格
  - 比例调整：按百分比调整价格
  - 固定金额：增加或减少固定金额
  - 成本加成：基于成本价格设置加成比例
- **价格预览**: 调整前后价格对比预览
- **批量确认**: 批量价格变更确认和执行

### 3.3 库存状态监控功能

#### 3.3.1 库存统计概览
**功能描述**: 页面顶部显示关键库存统计信息

**统计指标**:
- **库存总量**: 当前系统总库存数量
  - 实时更新库存数据
  - 支持按分类统计
- **库存异常**: 库存异常情况提醒
  - 负库存提醒（如当前显示-7G）
  - 库存数据异常检测
  - 异常原因分析和处理建议
- **挂单统计**: 当前挂单锁定库存统计
- **预警商品**: 库存低于安全值的商品数量

#### 3.3.2 库存状态监控
**功能描述**: 实时监控商品库存状态和异常情况

**监控指标**:
- **挂单量监控**:
  - 挂单量为0：表明无订单锁定，可随时发货
  - 挂单量异常：挂单量超过可用库存
  - 长期挂单：挂单时间过长的订单提醒
- **安全值监控**:
  - 安全值为0：未设置安全库存的风险提示
  - 库存不足：当前库存低于安全值的预警
  - 安全值建议：基于历史数据的安全值建议
- **库存异常检测**:
  - 负库存检测：库存数量为负数的异常情况
  - 数据不一致：库存数据与实际不符的检测
  - 长期无变动：长期无库存变动的商品提醒

### 3.4 商品状态管理功能

#### 3.4.1 启用状态控制
**功能描述**: 控制商品是否参与业务流程

**状态管理**:
- **启用状态**: 商品正常参与所有业务流程
  - 可用于销售下单
  - 可用于库存调拨
  - 可用于采购入库
- **停用状态**: 商品暂停参与业务流程
  - 不出现在销售下单中
  - 不可进行库存调拨
  - 保留历史数据和库存
- **状态切换**: 支持快速状态切换
  - 切换按钮操作
  - 状态变更确认
  - 状态变更日志记录

#### 3.4.2 批量状态管理
**功能描述**: 批量商品状态的启用和停用

**批量操作**:
- **批量启用**: 批量启用选中的商品
  - 选择要启用的商品
  - 确认批量启用操作
  - 批量状态更新
- **批量停用**: 批量停用选中的商品
  - 选择要停用的商品
  - 停用原因说明
  - 确认批量停用操作
- **状态验证**: 状态变更前的业务规则验证
  - 检查是否有未完成订单
  - 检查是否有库存锁定
  - 提供状态变更建议

### 3.5 数据导出功能

#### 3.5.1 导出功能设计
**功能描述**: 支持库存价格数据的导出

**导出选项**:
- **导出范围**: 
  - 当前筛选结果导出
  - 全部数据导出
  - 选中商品导出
- **导出字段**: 
  - 基础信息：商品编号、名称、分类
  - 库存信息：挂单量、安全值、启用状态
  - 价格信息：销售价、成本价、利润率
  - 自定义字段选择
- **导出格式**: 
  - Excel格式导出
  - CSV格式导出
  - PDF格式导出

## 4. 用户界面设计

### 4.1 页面布局设计
- **顶部统计区**: 库存总量和异常提醒信息
- **筛选搜索区**: 多维度筛选条件和搜索功能
- **数据表格区**: 商品库存价格信息展示
- **批量操作栏**: 批量操作按钮和功能入口

### 4.2 交互设计规范
- **状态切换**: 直观的开关按钮设计
- **批量选择**: 复选框和全选功能
- **数据加载**: 加载状态和进度提示
- **异常提醒**: 醒目的异常状态标识

### 4.3 响应式设计
- **PC端**: 完整功能展示，多列表格布局
- **平板端**: 适配中等屏幕，关键信息优先
- **移动端**: 卡片式布局，核心功能保留

## 5. 权限控制

### 5.1 功能权限
- **查看权限**: 库存价格信息查看权限
- **价格管理**: 价格编辑和批量改价权限
- **状态管理**: 商品启用停用权限
- **导出权限**: 数据导出权限控制

### 5.2 数据权限
- **门店权限**: 只能查看本门店商品库存
- **分类权限**: 按商品分类控制访问范围
- **敏感信息**: 成本价格等敏感信息需特殊权限

## 6. 异常处理

### 6.1 数据异常
- **库存异常**: 负库存等数据异常的检测和处理
- **价格异常**: 价格数据异常的验证和修复
- **同步异常**: 数据同步失败的重试机制

### 6.2 操作异常
- **权限异常**: 权限不足的友好提示
- **网络异常**: 网络连接异常的处理
- **并发异常**: 数据并发修改的冲突处理

---

**文档版本**: v1.0
**编写日期**: 2025-07-02
**编写人员**: AI系统架构师
**审核状态**: 待审核
