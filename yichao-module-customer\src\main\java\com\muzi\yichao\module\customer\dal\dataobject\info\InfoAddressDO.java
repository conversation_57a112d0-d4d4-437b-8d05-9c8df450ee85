package com.muzi.yichao.module.customer.dal.dataobject.info;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.muzi.yichao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 客户收货地址 DO
 *
 * <AUTHOR>
 */
@TableName("customer_info_address")
@KeySequence("customer_info_address_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InfoAddressDO extends BaseDO {

    /**
     * 地址ID
     */
    @TableId
    private Long id;
    /**
     * 客户ID
     */
    private Long customerId;
    /**
     * 收货人姓名
     */
    private String receiverName;
    /**
     * 收货人手机
     */
    private String receiverMobile;
    /**
     * 省份ID
     */
    private Integer provinceId;
    /**
     * 城市ID
     */
    private Integer cityId;
    /**
     * 区县ID
     */
    private Integer districtId;
    /**
     * 详细地址
     */
    private String detailAddress;
    /**
     * 邮政编码
     */
    private String postalCode;
    /**
     * 地址标签：1-家，2-公司，3-其他
     *
     * 枚举 {@link TODO address_label 对应的类}
     */
    private Integer addressLabel;
    /**
     * 是否默认地址
     */
    private Boolean isDefault;

}