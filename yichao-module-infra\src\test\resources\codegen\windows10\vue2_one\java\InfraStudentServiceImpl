package com.muzi.yichao.module.infra.service.demo;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import com.muzi.yichao.module.infra.controller.admin.demo.vo.*;
import com.muzi.yichao.module.infra.dal.dataobject.demo.InfraStudentDO;
import com.muzi.yichao.framework.common.pojo.PageResult;
import com.muzi.yichao.framework.common.pojo.PageParam;
import com.muzi.yichao.framework.common.util.object.BeanUtils;

import com.muzi.yichao.module.infra.dal.mysql.demo.InfraStudentMapper;

import static com.muzi.yichao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.muzi.yichao.module.infra.enums.ErrorCodeConstants.*;

/**
 * 学生 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InfraStudentServiceImpl implements InfraStudentService {

    @Resource
    private InfraStudentMapper studentMapper;

    @Override
    public Long createStudent(InfraStudentSaveReqVO createReqVO) {
        // 插入
        InfraStudentDO student = BeanUtils.toBean(createReqVO, InfraStudentDO.class);
        studentMapper.insert(student);
        // 返回
        return student.getId();
    }

    @Override
    public void updateStudent(InfraStudentSaveReqVO updateReqVO) {
        // 校验存在
        validateStudentExists(updateReqVO.getId());
        // 更新
        InfraStudentDO updateObj = BeanUtils.toBean(updateReqVO, InfraStudentDO.class);
        studentMapper.updateById(updateObj);
    }

    @Override
    public void deleteStudent(Long id) {
        // 校验存在
        validateStudentExists(id);
        // 删除
        studentMapper.deleteById(id);
    }

    private void validateStudentExists(Long id) {
        if (studentMapper.selectById(id) == null) {
            throw exception(STUDENT_NOT_EXISTS);
        }
    }

    @Override
    public InfraStudentDO getStudent(Long id) {
        return studentMapper.selectById(id);
    }

    @Override
    public PageResult<InfraStudentDO> getStudentPage(InfraStudentPageReqVO pageReqVO) {
        return studentMapper.selectPage(pageReqVO);
    }

}