package com.muzi.yichao.module.customer.dal.dataobject.info;

import lombok.*;

import java.time.LocalDate;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.muzi.yichao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 客户积分记录 DO
 *
 * <AUTHOR>
 */
@TableName("customer_info_points_record")
@KeySequence("customer_info_points_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InfoPointsRecordDO extends BaseDO {

    /**
     * 记录ID
     */
    @TableId
    private Long id;
    /**
     * 客户ID
     */
    private Long customerId;
    /**
     * 记录类型：1-获得积分，2-消费积分，3-积分调整，4-积分过期
     *
     * 枚举 {@link TODO points_record_type 对应的类}
     */
    private Integer recordType;
    /**
     * 积分数量
     */
    private Integer points;
    /**
     * 操作前积分余额
     */
    private Integer balanceBefore;
    /**
     * 操作后积分余额
     */
    private Integer balanceAfter;
    /**
     * 来源类型：1-消费获得，2-活动奖励，3-签到获得，4-推荐奖励，5-手动调整
     *
     * 枚举 {@link TODO points_source_type 对应的类}
     */
    private Integer sourceType;
    /**
     * 来源ID（关联消费记录等）
     */
    private Long sourceId;
    /**
     * 兑换类型：1-商品兑换，2-服务兑换，3-现金兑换
     *
     * 枚举 {@link TODO points_exchange_type 对应的类}
     */
    private Integer exchangeType;
    /**
     * 兑换ID
     */
    private Long exchangeId;
    /**
     * 到期日期
     */
    private LocalDate expireDate;
    /**
     * 操作人员ID
     */
    private Long operatorId;
    /**
     * 操作人员姓名
     */
    private String operatorName;
    /**
     * 操作原因
     */
    private String operationReason;

}