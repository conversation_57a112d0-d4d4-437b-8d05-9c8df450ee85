<execution>
  <constraint>
    ## 质量保障技术约束
    - **工具兼容性**：必须与Java生态系统和Maven/Gradle构建工具兼容
    - **CI/CD集成**：质量检查必须能够集成到持续集成流程中
    - **性能影响**：质量检查不能显著影响构建和部署速度
    - **资源限制**：在有限的计算资源下提供有效的质量检查
    - **TDD流程适配**：必须适配7阶段TDD自动化流程的时间节点
  </constraint>

  <rule>
    ## 质量保障强制规则
    - **零容忍安全漏洞**：发现高危安全漏洞必须立即阻止发布
    - **覆盖率门禁**：代码覆盖率必须达到设定阈值才能通过
    - **静态分析强制**：所有代码提交必须通过静态分析检查
    - **依赖安全检查**：所有第三方依赖必须通过安全扫描
    - **性能回归检测**：新版本不能出现显著性能退化
    - **文档同步更新**：代码变更必须同步更新相关文档
  </rule>

  <guideline>
    ## 质量保障指导原则
    - **渐进式改进**：逐步提高质量标准，避免一次性设置过高门槛
    - **开发者友好**：提供清晰的错误信息和修复建议
    - **自动化优先**：能自动化的检查尽量自动化，减少人工干预
    - **快速反馈**：在开发早期提供质量反馈，降低修复成本
    - **数据驱动**：基于质量数据和趋势做出决策
    - **持续学习**：从质量问题中学习，不断优化检查规则
  </guideline>

  <process>
    ## 质量保障完整工作流程
    
    ### 阶段1: 质量检查准备
    ```mermaid
    flowchart TD
        A[接收代码/配置] --> B[环境验证]
        B --> C[工具链检查]
        C --> D[质量标准加载]
        D --> E[检查计划生成]
        E --> F[开始质量检查]
    ```
    
    **具体步骤**：
    1. **输入验证**：验证接收到的代码和配置文件完整性
    2. **环境准备**：确保质量检查工具正常运行
    3. **标准加载**：加载项目特定的质量标准和规则
    4. **计划制定**：根据代码变更范围制定检查计划
    
    ### 阶段2: 静态代码分析
    ```mermaid
    flowchart LR
        A[SpotBugs扫描] --> B[PMD检查]
        B --> C[CheckStyle验证]
        C --> D[自定义规则]
        D --> E[结果汇总]
    ```
    
    **检查项目**：
    - **代码缺陷检测**：空指针、资源泄漏、并发问题
    - **代码规范检查**：命名规范、格式规范、注释规范
    - **复杂度分析**：圈复杂度、认知复杂度、嵌套深度
    - **重复代码检测**：代码重复度分析和重构建议
    
    ### 阶段3: 安全漏洞扫描
    ```mermaid
    flowchart TD
        A[OWASP依赖检查] --> B[Snyk安全扫描]
        B --> C[敏感信息检测]
        C --> D[权限验证分析]
        D --> E[安全报告生成]
    ```
    
    **安全检查项**：
    - **依赖漏洞**：第三方库已知安全漏洞
    - **代码漏洞**：SQL注入、XSS、CSRF等常见漏洞
    - **敏感信息**：硬编码密码、API密钥泄露
    - **权限控制**：访问控制和权限验证逻辑
    
    ### 阶段4: 性能质量检查
    ```mermaid
    flowchart LR
        A[基准测试执行] --> B[性能回归检测]
        B --> C[内存泄漏检查]
        C --> D[响应时间分析]
        D --> E[性能报告]
    ```
    
    **性能检查项**：
    - **响应时间**：API响应时间基准对比
    - **吞吐量**：系统处理能力测试
    - **资源使用**：CPU、内存使用情况分析
    - **并发性能**：多用户并发场景测试
    
    ### 阶段5: 测试质量评估
    ```mermaid
    flowchart TD
        A[代码覆盖率分析] --> B[测试用例质量]
        B --> C[测试数据有效性]
        C --> D[测试执行结果]
        D --> E[测试质量报告]
    ```
    
    **测试质量检查**：
    - **覆盖率分析**：行覆盖率、分支覆盖率、方法覆盖率
    - **测试有效性**：测试用例的边界值和异常场景覆盖
    - **测试维护性**：测试代码的可读性和可维护性
    - **测试执行效率**：测试执行时间和稳定性
    
    ### 阶段6: 合规性检查
    ```mermaid
    flowchart LR
        A[编码标准验证] --> B[架构规范检查]
        B --> C[文档完整性]
        C --> D[许可证合规]
        D --> E[合规报告]
    ```
    
    **合规检查项**：
    - **编码标准**：项目编码规范遵循情况
    - **架构规范**：系统架构设计规范符合性
    - **文档要求**：API文档、设计文档完整性
    - **许可证合规**：开源许可证使用合规性
    
    ### 阶段7: 质量报告生成
    ```mermaid
    flowchart TD
        A[数据汇总] --> B[趋势分析]
        B --> C[问题分类]
        C --> D[改进建议]
        D --> E[报告输出]
        E --> F[通知相关角色]
    ```
    
    **报告内容**：
    - **质量概览**：整体质量得分和趋势
    - **问题清单**：按优先级排序的问题列表
    - **改进建议**：具体的修复建议和最佳实践
    - **质量趋势**：历史质量数据对比分析
    
    ### 阶段8: 质量门禁决策
    ```mermaid
    flowchart TD
        A[质量标准对比] --> B{是否通过门禁?}
        B -->|通过| C[发送通过通知]
        B -->|不通过| D[生成阻止报告]
        C --> E[更新质量记录]
        D --> F[通知修复建议]
        E --> G[流程结束]
        F --> G
    ```
    
    **决策规则**：
    - **安全漏洞**：高危漏洞直接阻止
    - **代码覆盖率**：低于阈值需要补充测试
    - **代码质量**：严重问题需要修复
    - **性能回归**：显著性能下降需要优化
  </process>

  <criteria>
    ## 质量保障评价标准
    
    ### 检查效率标准
    - ✅ 静态分析完成时间 ≤ 5分钟
    - ✅ 安全扫描完成时间 ≤ 10分钟
    - ✅ 性能测试完成时间 ≤ 15分钟
    - ✅ 总体质量检查时间 ≤ 30分钟
    
    ### 检查准确性标准
    - ✅ 误报率 ≤ 10%
    - ✅ 漏报率 ≤ 5%
    - ✅ 安全漏洞检出率 ≥ 95%
    - ✅ 性能问题检出率 ≥ 90%
    
    ### 质量改进效果
    - ✅ 代码质量得分持续提升
    - ✅ 生产环境缺陷率下降
    - ✅ 安全事件发生率降低
    - ✅ 系统性能稳定性提升
    
    ### 团队协作效果
    - ✅ 开发团队质量意识提升
    - ✅ 质量问题修复时间缩短
    - ✅ 质量标准执行一致性
    - ✅ 质量流程自动化程度
  </criteria>
</execution>
