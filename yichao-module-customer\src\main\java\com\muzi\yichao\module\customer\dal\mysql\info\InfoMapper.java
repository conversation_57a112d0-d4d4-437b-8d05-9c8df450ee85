package com.muzi.yichao.module.customer.dal.mysql.info;

import java.util.*;

import com.muzi.yichao.framework.common.pojo.PageResult;
import com.muzi.yichao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.muzi.yichao.framework.mybatis.core.mapper.BaseMapperX;
import com.muzi.yichao.module.customer.dal.dataobject.info.InfoDO;
import org.apache.ibatis.annotations.Mapper;
import com.muzi.yichao.module.customer.controller.admin.info.vo.*;

/**
 * 客户信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InfoMapper extends BaseMapperX<InfoDO> {

    default PageResult<InfoDO> selectPage(InfoPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InfoDO>()
                .eqIfPresent(InfoDO::getCustomerNo, reqVO.getCustomerNo())
                .eqIfPresent(InfoDO::getStoreId, reqVO.getStoreId())
                .eqIfPresent(InfoDO::getCategory, reqVO.getCategory())
                .eqIfPresent(InfoDO::getSource, reqVO.getSource())
                .likeIfPresent(InfoDO::getName, reqVO.getName())
                .eqIfPresent(InfoDO::getMobile, reqVO.getMobile())
                .eqIfPresent(InfoDO::getSex, reqVO.getSex())
                .eqIfPresent(InfoDO::getIdType, reqVO.getIdType())
                .eqIfPresent(InfoDO::getIdNumber, reqVO.getIdNumber())
                .eqIfPresent(InfoDO::getFollowerUserId, reqVO.getFollowerUserId())
                .eqIfPresent(InfoDO::getDesignerUserId, reqVO.getDesignerUserId())
                .betweenIfPresent(InfoDO::getIssueDate, reqVO.getIssueDate())
                .betweenIfPresent(InfoDO::getExpireDate, reqVO.getExpireDate())
                .eqIfPresent(InfoDO::getCustomerType, reqVO.getCustomerType())
                .eqIfPresent(InfoDO::getVipLevel, reqVO.getVipLevel())
                .eqIfPresent(InfoDO::getStatus, reqVO.getStatus())
                .orderByDesc(InfoDO::getId));
    }

}