# SaaS智能家装CRM系统 - 添加图纸页面功能设计文档

## 1. 页面概述

### 1.1 页面目的
添加图纸页面用于向智能家居方案中上传和添加新的设计图纸，提供图纸文件上传、基本信息录入、分类设置和初始配置功能。该页面是图纸管理流程的起点，为方案的图纸库建立基础内容。

### 1.2 业务价值
- 标准化图纸上传流程，确保图纸信息的完整性和规范性
- 建立图纸分类和标签体系，提升图纸管理效率
- 提供图纸预览和验证功能，确保图纸质量
- 支持批量上传功能，提升工作效率

### 1.3 页面位置
- **所属模块**: 智能家居模块
- **业务流程位置**: 方案设计 → 图纸管理 → **添加图纸**
- **关联页面**: 
  - 图纸页面（入口页面）
  - 方案列表页面（方案关联）

## 2. 添加图纸页面操作流程图

```mermaid
flowchart TD
    A[用户点击添加图纸] --> B[跳转添加图纸页面]
    B --> C[权限验证]
    C --> D{权限验证通过?}
    D -->|否| E[显示权限不足提示]
    D -->|是| F[加载页面表单]

    F --> G[显示文件上传区]
    F --> H[显示图纸信息区]
    F --> I[显示分类设置区]
    F --> J[显示预览确认区]

    G --> K[选择上传方式]
    K --> L[单文件上传]
    K --> M[批量上传]
    K --> N[拖拽上传]

    L --> O[文件选择对话框]
    M --> P[多文件选择]
    N --> Q[拖拽区域]

    O --> R[文件格式验证]
    P --> R
    Q --> R

    R --> S{格式验证通过?}
    S -->|否| T[显示格式错误提示]
    S -->|是| U[文件上传处理]

    U --> V[显示上传进度]
    V --> W[上传完成]
    W --> X[生成图纸预览]

    H --> Y[图纸名称输入]
    H --> Z[图纸描述输入]
    H --> AA[设计师选择]
    H --> BB[版本信息设置]

    I --> CC[图纸类型选择]
    I --> DD[标签设置]
    I --> EE[优先级设置]
    I --> FF[状态设置]

    X --> GG[预览图纸内容]
    GG --> HH[确认图纸质量]
    HH --> II{质量检查通过?}
    II -->|否| JJ[重新上传或编辑]
    II -->|是| KK[表单信息验证]

    Y --> KK
    Z --> KK
    AA --> KK
    CC --> KK

    KK --> LL{信息验证通过?}
    LL -->|否| MM[显示错误提示]
    LL -->|是| NN[保存图纸信息]

    NN --> OO[生成图纸编号]
    OO --> PP[关联方案]
    PP --> QQ[更新图纸库]
    QQ --> RR[保存成功提示]
    RR --> SS[跳转图纸页面]

    style A fill:#e1f5fe
    style D fill:#fff3e0
    style E fill:#ffebee
    style S fill:#f3e5f5
    style II fill:#f3e5f5
    style LL fill:#f3e5f5
    style NN fill:#e8f5e8
```

### 流程说明
添加图纸页面的操作流程包含以下核心环节：

1. **页面访问与权限验证**：从图纸页面跳转，进行权限验证
2. **文件上传处理**：支持多种方式上传图纸文件，进行格式验证
3. **图纸信息录入**：录入图纸的基本信息和分类设置
4. **预览质量确认**：预览上传的图纸并确认质量
5. **数据保存与跳转**：验证信息后保存图纸并跳转回图纸页面

## 3. 详细功能设计

### 3.1 文件上传功能

#### 3.1.1 上传方式支持
**功能描述**: 支持多种文件上传方式

**上传方式**:
- **点击上传**: 点击按钮选择文件上传
- **拖拽上传**: 拖拽文件到指定区域上传
- **批量上传**: 一次选择多个文件批量上传
- **文件夹上传**: 支持整个文件夹的上传

#### 3.1.2 文件格式支持
**功能描述**: 支持多种图纸文件格式

**支持格式**:
- **图片格式**: JPG、PNG、GIF、BMP、TIFF
- **CAD格式**: DWG、DXF、DWF
- **PDF格式**: PDF文档
- **矢量格式**: SVG、AI、EPS
- **设计格式**: PSD、CDR、SKP

#### 3.1.3 文件验证功能
**功能描述**: 验证上传文件的有效性

**验证内容**:
- **格式验证**: 检查文件格式是否支持
- **大小验证**: 检查文件大小是否超限
- **完整性验证**: 检查文件是否完整无损
- **安全验证**: 检查文件是否包含恶意代码

### 3.2 图纸信息录入功能

#### 3.2.1 基本信息录入
**功能描述**: 录入图纸的基本信息

**录入字段**:
- **图纸名称**: 图纸的名称，必填字段
- **图纸编号**: 图纸的编号，可自动生成或手动输入
- **图纸描述**: 图纸的详细描述和说明
- **设计师**: 图纸的设计者，可选择或输入
- **创建日期**: 图纸的创建日期，默认当前日期
- **版本号**: 图纸的版本号，默认为1.0

#### 3.2.2 技术信息录入
**功能描述**: 录入图纸的技术相关信息

**技术信息**:
- **图纸比例**: 图纸的绘制比例
- **图纸尺寸**: 图纸的标准尺寸（A0/A1/A2/A3/A4）
- **坐标系统**: 图纸使用的坐标系统
- **单位制**: 图纸使用的单位制（毫米/厘米/米）
- **精度要求**: 图纸的精度要求

### 3.3 分类设置功能

#### 3.3.1 图纸类型设置
**功能描述**: 设置图纸的类型分类

**类型选项**:
- **平面布局图**: 房屋平面布局和设备位置图
- **系统架构图**: 智能家居系统架构图
- **布线图**: 电路和网络布线图
- **安装图**: 设备安装位置和方式图
- **效果图**: 3D效果渲染图
- **施工图**: 详细的施工图纸

#### 3.3.2 标签管理功能
**功能描述**: 为图纸添加标签便于管理

**标签类型**:
- **功能标签**: 照明、安防、影音、环控等功能标签
- **区域标签**: 客厅、卧室、厨房、卫生间等区域标签
- **阶段标签**: 设计、施工、验收等阶段标签
- **自定义标签**: 用户自定义的标签

#### 3.3.3 优先级设置
**功能描述**: 设置图纸的重要程度

**优先级分类**:
- **高优先级**: 核心重要图纸
- **中优先级**: 一般重要图纸
- **低优先级**: 参考性图纸
- **归档**: 历史归档图纸

### 3.4 预览确认功能

#### 3.4.1 图纸预览功能
**功能描述**: 预览上传的图纸内容

**预览功能**:
- **缩略图预览**: 显示图纸的缩略图
- **全尺寸预览**: 查看图纸的全尺寸内容
- **缩放功能**: 图纸的放大和缩小
- **平移功能**: 图纸的拖拽平移
- **旋转功能**: 图纸的旋转调整

#### 3.4.2 质量检查功能
**功能描述**: 检查图纸的质量和规范性

**检查内容**:
- **清晰度检查**: 检查图纸的清晰度
- **完整性检查**: 检查图纸内容的完整性
- **规范性检查**: 检查图纸是否符合规范
- **尺寸检查**: 检查图纸尺寸是否合适

#### 3.4.3 批注功能
**功能描述**: 在预览时添加批注和说明

**批注工具**:
- **文字批注**: 添加文字说明
- **箭头指向**: 添加指向性箭头
- **图形标记**: 添加圆形、矩形等标记
- **颜色标注**: 使用不同颜色进行标注

### 3.5 表单操作功能

#### 3.5.1 数据验证功能
**功能描述**: 验证录入数据的完整性和准确性

**验证规则**:
- **必填字段验证**: 检查必填字段是否完整
- **格式验证**: 验证数据格式的正确性
- **重复验证**: 检查是否存在重复的图纸
- **关联验证**: 验证图纸与方案的关联关系

#### 3.5.2 保存操作功能
**功能描述**: 提供图纸数据的保存操作

**保存选项**:
- **保存草稿**: 保存当前信息为草稿状态
- **保存并继续**: 保存信息并继续添加图纸
- **保存并返回**: 保存信息并返回图纸页面
- **取消操作**: 取消当前操作并返回

## 4. 用户界面设计

### 4.1 页面布局设计
- **顶部导航区**: 页面标题和返回按钮
- **上传区域**: 文件上传的主要区域
- **信息录入区**: 图纸信息和分类设置区域
- **预览区域**: 图纸预览和确认区域
- **底部操作区**: 保存、取消等操作按钮

### 4.2 上传界面设计
- **拖拽区域**: 清晰的拖拽上传区域
- **进度显示**: 文件上传的进度条
- **状态提示**: 上传状态的实时提示
- **错误提示**: 上传失败的错误信息

### 4.3 表单设计规范
- **字段分组**: 相关字段进行分组显示
- **必填标识**: 必填字段使用红色星号标识
- **智能提示**: 提供输入建议和自动完成
- **实时验证**: 输入过程中进行实时验证

## 5. 数据流向

### 5.1 数据输入
- **来源**: 用户上传的图纸文件 + 手动录入的图纸信息
- **格式**: 多种格式的图纸文件和结构化的元数据

### 5.2 数据输出
- **流向**: 图纸页面（新增的图纸记录）
- **存储**: 图纸文件存储 + 图纸信息数据库

### 5.3 业务关联
- **前置页面**: 图纸页面（入口）
- **关联数据**: 方案信息、设计师信息
- **后续应用**: 场景配置、工单管理中的图纸引用

## 6. 权限控制

### 6.1 访问权限
- **设计师**: 可以上传自己负责方案的图纸
- **项目经理**: 可以上传项目相关的图纸
- **管理员**: 可以上传和管理所有图纸

### 6.2 操作权限
- **上传权限**: 图纸文件上传权限
- **编辑权限**: 图纸信息编辑权限
- **分类权限**: 图纸分类设置权限
- **发布权限**: 图纸发布权限

## 7. 异常处理

### 7.1 上传异常
- **网络中断**: 网络中断时的断点续传
- **文件过大**: 文件过大时的分片上传
- **格式错误**: 不支持格式的友好提示
- **存储空间**: 存储空间不足的处理

### 7.2 操作异常
- **权限不足**: 权限不足的友好提示
- **数据丢失**: 意外关闭页面时的数据恢复
- **并发冲突**: 多人同时操作的冲突处理

---

**文档版本**: v1.0
**编写日期**: 2025-07-14
**编写人员**: AI系统架构师
**审核状态**: 待审核
**对应图片**: 方案列表-点击图纸-添加图纸页面.png
