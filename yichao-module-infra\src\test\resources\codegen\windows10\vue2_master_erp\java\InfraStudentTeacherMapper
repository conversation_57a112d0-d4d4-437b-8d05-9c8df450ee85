package com.muzi.yichao.module.infra.dal.mysql.demo;

import java.util.*;

import com.muzi.yichao.framework.common.pojo.PageResult;
import com.muzi.yichao.framework.common.pojo.PageParam;
import com.muzi.yichao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.muzi.yichao.framework.mybatis.core.mapper.BaseMapperX;
import com.muzi.yichao.module.infra.dal.dataobject.demo.InfraStudentTeacherDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 学生班主任 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InfraStudentTeacherMapper extends BaseMapperX<InfraStudentTeacherDO> {

    default PageResult<InfraStudentTeacherDO> selectPage(PageParam reqVO, Long studentId) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InfraStudentTeacherDO>()
            .eq(InfraStudentTeacherDO::getStudentId, studentId)
            .orderByDesc(InfraStudentTeacherDO::getId));
    }

    default int deleteByStudentId(Long studentId) {
        return delete(InfraStudentTeacherDO::getStudentId, studentId);
    }

}