/*
 客户管理模块 - 字典数据重复修复SQL (简化版本)
 
 专门解决SQL安全检查问题：
 - 避免复杂的JOIN DELETE
 - 每个DELETE都有明确的WHERE条件
 - 使用最简单的方式删除重复数据
 
 使用方法：
 1. 如果遇到安全检查问题，先执行：SET SQL_SAFE_UPDATES = 0;
 2. 执行此脚本
 3. 执行完成后：SET SQL_SAFE_UPDATES = 1;
 
 创建时间：2025-07-20
*/

-- 临时关闭安全更新模式（如果需要）
-- SET SQL_SAFE_UPDATES = 0;

-- ========================================
-- 检查重复数据
-- ========================================

-- 查看字典类型重复情况
SELECT 
    'DICT_TYPE_DUPLICATES' as check_type,
    type,
    COUNT(*) as count
FROM system_dict_type 
WHERE type IN (
    'customer_category', 'customer_source', 'customer_type', 'id_type',
    'address_label', 'customer_status', 'vip_level', 'points_record_type',
    'points_source_type', 'points_exchange_type', 'account_operation_type', 'invoice_type'
)
GROUP BY type
HAVING COUNT(*) > 1;

-- 查看字典数据重复情况
SELECT 
    'DICT_DATA_DUPLICATES' as check_type,
    dict_type,
    value,
    COUNT(*) as count
FROM system_dict_data 
WHERE dict_type IN (
    'customer_category', 'customer_source', 'customer_type', 'id_type',
    'address_label', 'customer_status', 'vip_level', 'points_record_type',
    'points_source_type', 'points_exchange_type', 'account_operation_type', 'invoice_type'
)
GROUP BY dict_type, value
HAVING COUNT(*) > 1;

-- ========================================
-- 方法1：使用具体ID删除重复数据
-- ========================================

-- 删除客户类别重复数据（保留ID最小的）
DELETE FROM system_dict_data 
WHERE dict_type = 'customer_category' 
AND id NOT IN (
    SELECT * FROM (
        SELECT MIN(id) 
        FROM system_dict_data 
        WHERE dict_type = 'customer_category'
        GROUP BY dict_type, value
    ) as temp
);

-- 删除客户来源渠道重复数据
DELETE FROM system_dict_data 
WHERE dict_type = 'customer_source' 
AND id NOT IN (
    SELECT * FROM (
        SELECT MIN(id) 
        FROM system_dict_data 
        WHERE dict_type = 'customer_source'
        GROUP BY dict_type, value
    ) as temp
);

-- 删除客户类型重复数据
DELETE FROM system_dict_data 
WHERE dict_type = 'customer_type' 
AND id NOT IN (
    SELECT * FROM (
        SELECT MIN(id) 
        FROM system_dict_data 
        WHERE dict_type = 'customer_type'
        GROUP BY dict_type, value
    ) as temp
);

-- 删除证件类型重复数据
DELETE FROM system_dict_data 
WHERE dict_type = 'id_type' 
AND id NOT IN (
    SELECT * FROM (
        SELECT MIN(id) 
        FROM system_dict_data 
        WHERE dict_type = 'id_type'
        GROUP BY dict_type, value
    ) as temp
);

-- 删除地址标签重复数据
DELETE FROM system_dict_data 
WHERE dict_type = 'address_label' 
AND id NOT IN (
    SELECT * FROM (
        SELECT MIN(id) 
        FROM system_dict_data 
        WHERE dict_type = 'address_label'
        GROUP BY dict_type, value
    ) as temp
);

-- 删除客户状态重复数据
DELETE FROM system_dict_data 
WHERE dict_type = 'customer_status' 
AND id NOT IN (
    SELECT * FROM (
        SELECT MIN(id) 
        FROM system_dict_data 
        WHERE dict_type = 'customer_status'
        GROUP BY dict_type, value
    ) as temp
);

-- 删除VIP等级重复数据
DELETE FROM system_dict_data 
WHERE dict_type = 'vip_level' 
AND id NOT IN (
    SELECT * FROM (
        SELECT MIN(id) 
        FROM system_dict_data 
        WHERE dict_type = 'vip_level'
        GROUP BY dict_type, value
    ) as temp
);

-- 删除积分记录类型重复数据
DELETE FROM system_dict_data 
WHERE dict_type = 'points_record_type' 
AND id NOT IN (
    SELECT * FROM (
        SELECT MIN(id) 
        FROM system_dict_data 
        WHERE dict_type = 'points_record_type'
        GROUP BY dict_type, value
    ) as temp
);

-- 删除积分来源类型重复数据
DELETE FROM system_dict_data 
WHERE dict_type = 'points_source_type' 
AND id NOT IN (
    SELECT * FROM (
        SELECT MIN(id) 
        FROM system_dict_data 
        WHERE dict_type = 'points_source_type'
        GROUP BY dict_type, value
    ) as temp
);

-- 删除积分兑换类型重复数据
DELETE FROM system_dict_data 
WHERE dict_type = 'points_exchange_type' 
AND id NOT IN (
    SELECT * FROM (
        SELECT MIN(id) 
        FROM system_dict_data 
        WHERE dict_type = 'points_exchange_type'
        GROUP BY dict_type, value
    ) as temp
);

-- 删除账户操作类型重复数据
DELETE FROM system_dict_data 
WHERE dict_type = 'account_operation_type' 
AND id NOT IN (
    SELECT * FROM (
        SELECT MIN(id) 
        FROM system_dict_data 
        WHERE dict_type = 'account_operation_type'
        GROUP BY dict_type, value
    ) as temp
);

-- 删除发票类型重复数据
DELETE FROM system_dict_data 
WHERE dict_type = 'invoice_type' 
AND id NOT IN (
    SELECT * FROM (
        SELECT MIN(id) 
        FROM system_dict_data 
        WHERE dict_type = 'invoice_type'
        GROUP BY dict_type, value
    ) as temp
);

-- ========================================
-- 删除重复的字典类型
-- ========================================

-- 删除重复的字典类型记录（保留ID最小的）
DELETE FROM system_dict_type 
WHERE type IN (
    'customer_category', 'customer_source', 'customer_type', 'id_type',
    'address_label', 'customer_status', 'vip_level', 'points_record_type',
    'points_source_type', 'points_exchange_type', 'account_operation_type', 'invoice_type'
)
AND id NOT IN (
    SELECT * FROM (
        SELECT MIN(id) 
        FROM system_dict_type 
        WHERE type IN (
            'customer_category', 'customer_source', 'customer_type', 'id_type',
            'address_label', 'customer_status', 'vip_level', 'points_record_type',
            'points_source_type', 'points_exchange_type', 'account_operation_type', 'invoice_type'
        )
        GROUP BY type
    ) as temp
);

-- ========================================
-- 验证修复结果
-- ========================================

-- 再次检查字典类型重复情况
SELECT 
    'AFTER_FIX_DICT_TYPE' as check_type,
    type,
    COUNT(*) as count,
    CASE WHEN COUNT(*) > 1 THEN 'STILL_DUPLICATE' ELSE 'FIXED' END as status
FROM system_dict_type 
WHERE type IN (
    'customer_category', 'customer_source', 'customer_type', 'id_type',
    'address_label', 'customer_status', 'vip_level', 'points_record_type',
    'points_source_type', 'points_exchange_type', 'account_operation_type', 'invoice_type'
)
GROUP BY type
ORDER BY type;

-- 再次检查字典数据重复情况
SELECT 
    'AFTER_FIX_DICT_DATA' as check_type,
    dict_type,
    value,
    COUNT(*) as count,
    CASE WHEN COUNT(*) > 1 THEN 'STILL_DUPLICATE' ELSE 'FIXED' END as status
FROM system_dict_data 
WHERE dict_type IN (
    'customer_category', 'customer_source', 'customer_type', 'id_type',
    'address_label', 'customer_status', 'vip_level', 'points_record_type',
    'points_source_type', 'points_exchange_type', 'account_operation_type', 'invoice_type'
)
GROUP BY dict_type, value
HAVING COUNT(*) > 1
ORDER BY dict_type, value;

-- 统计最终结果
SELECT 
    'FINAL_STATS' as info,
    'system_dict_type' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN type IN (
        'customer_category', 'customer_source', 'customer_type', 'id_type',
        'address_label', 'customer_status', 'vip_level', 'points_record_type',
        'points_source_type', 'points_exchange_type', 'account_operation_type', 'invoice_type'
    ) THEN 1 END) as customer_related_records
FROM system_dict_type
UNION ALL
SELECT 
    'FINAL_STATS' as info,
    'system_dict_data' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN dict_type IN (
        'customer_category', 'customer_source', 'customer_type', 'id_type',
        'address_label', 'customer_status', 'vip_level', 'points_record_type',
        'points_source_type', 'points_exchange_type', 'account_operation_type', 'invoice_type'
    ) THEN 1 END) as customer_related_records
FROM system_dict_data;

-- 恢复安全更新模式（如果之前关闭了）
-- SET SQL_SAFE_UPDATES = 1;

/*
========================================
使用说明
========================================

如果遇到 "不安全的查询" 错误：

方法1 - 临时关闭安全模式：
1. 执行：SET SQL_SAFE_UPDATES = 0;
2. 运行此脚本
3. 执行：SET SQL_SAFE_UPDATES = 1;

方法2 - 在数据库工具中：
1. 找到安全设置选项
2. 临时关闭 "Safe Updates" 模式
3. 执行脚本
4. 重新开启安全模式

方法3 - 使用命令行：
mysql -u用户名 -p密码 --safe-updates=0 数据库名 < fix-duplicate-simple.sql

特点：
✅ 每个DELETE都有明确的WHERE条件
✅ 使用子查询而非复杂的JOIN
✅ 保留最早创建的记录（ID最小）
✅ 包含完整的验证检查
✅ 简单易懂，容易调试

执行前建议：
1. 备份数据库
2. 在测试环境先验证
3. 检查执行结果
*/
