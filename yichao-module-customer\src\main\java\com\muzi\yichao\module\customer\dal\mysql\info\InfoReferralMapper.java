package com.muzi.yichao.module.customer.dal.mysql.info;

import java.util.*;

import com.muzi.yichao.framework.common.pojo.PageResult;
import com.muzi.yichao.framework.common.pojo.PageParam;
import com.muzi.yichao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.muzi.yichao.framework.mybatis.core.mapper.BaseMapperX;
import com.muzi.yichao.module.customer.dal.dataobject.info.InfoReferralDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 客户推荐关系 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InfoReferralMapper extends BaseMapperX<InfoReferralDO> {

    default PageResult<InfoReferralDO> selectPage(PageParam reqVO, Long referrerId) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InfoReferralDO>()
            .eq(InfoReferralDO::getReferrerId, referrerId)
            .orderByDesc(InfoReferralDO::getId));
    }

    default int deleteByReferrerId(Long referrerId) {
        return delete(InfoReferralDO::getReferrerId, referrerId);
    }

	default int deleteByReferrerIds(List<Long> referrerIds) {
	    return deleteBatch(InfoReferralDO::getReferrerId, referrerIds);
	}

}
