package com.muzi.yichao.module.customer.service.info;

import cn.hutool.core.collection.CollUtil;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import com.muzi.yichao.module.customer.controller.admin.info.vo.*;
import com.muzi.yichao.module.customer.dal.dataobject.info.InfoDO;
import com.muzi.yichao.module.customer.dal.dataobject.info.InfoAccountLogDO;
import com.muzi.yichao.module.customer.dal.dataobject.info.InfoAddressDO;
import com.muzi.yichao.module.customer.dal.dataobject.info.InfoInvoiceInfoDO;
import com.muzi.yichao.module.customer.dal.dataobject.info.InfoPointsRecordDO;
import com.muzi.yichao.module.customer.dal.dataobject.info.InfoReferralDO;
import com.muzi.yichao.module.customer.dal.dataobject.info.InfoStatusLogDO;
import com.muzi.yichao.framework.common.pojo.PageResult;
import com.muzi.yichao.framework.common.pojo.PageParam;
import com.muzi.yichao.framework.common.util.object.BeanUtils;

import com.muzi.yichao.module.customer.dal.mysql.info.InfoMapper;
import com.muzi.yichao.module.customer.dal.mysql.info.InfoAccountLogMapper;
import com.muzi.yichao.module.customer.dal.mysql.info.InfoAddressMapper;
import com.muzi.yichao.module.customer.dal.mysql.info.InfoInvoiceInfoMapper;
import com.muzi.yichao.module.customer.dal.mysql.info.InfoPointsRecordMapper;
import com.muzi.yichao.module.customer.dal.mysql.info.InfoReferralMapper;
import com.muzi.yichao.module.customer.dal.mysql.info.InfoStatusLogMapper;

import static com.muzi.yichao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.muzi.yichao.framework.common.util.collection.CollectionUtils.convertList;
import static com.muzi.yichao.framework.common.util.collection.CollectionUtils.diffList;
import static com.muzi.yichao.module.customer.enums.ErrorCodeConstants.*;

/**
 * 客户信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InfoServiceImpl implements InfoService {

    @Resource
    private InfoMapper infoMapper;
    @Resource
    private InfoAccountLogMapper infoAccountLogMapper;
    @Resource
    private InfoAddressMapper infoAddressMapper;
    @Resource
    private InfoInvoiceInfoMapper infoInvoiceInfoMapper;
    @Resource
    private InfoPointsRecordMapper infoPointsRecordMapper;
    @Resource
    private InfoReferralMapper infoReferralMapper;
    @Resource
    private InfoStatusLogMapper infoStatusLogMapper;

    @Override
    public Long createInfo(InfoSaveReqVO createReqVO) {
        // 插入
        InfoDO info = BeanUtils.toBean(createReqVO, InfoDO.class);
        infoMapper.insert(info);

        // 返回
        return info.getId();
    }

    @Override
    public void updateInfo(InfoSaveReqVO updateReqVO) {
        // 校验存在
        validateInfoExists(updateReqVO.getId());
        // 更新
        InfoDO updateObj = BeanUtils.toBean(updateReqVO, InfoDO.class);
        infoMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteInfo(Long id) {
        // 校验存在
        validateInfoExists(id);
        // 删除
        infoMapper.deleteById(id);

        // 删除子表
        deleteInfoAccountLogByCustomerId(id);
        deleteInfoAddressByCustomerId(id);
        deleteInfoInvoiceInfoByCustomerId(id);
        deleteInfoPointsRecordByCustomerId(id);
        deleteInfoReferralByReferrerId(id);
        deleteInfoStatusLogByCustomerId(id);
    }

    @Override
        @Transactional(rollbackFor = Exception.class)
    public void deleteInfoListByIds(List<Long> ids) {
        // 删除
        infoMapper.deleteByIds(ids);
    
    // 删除子表
            deleteInfoAccountLogByCustomerIds(ids);
            deleteInfoAddressByCustomerIds(ids);
            deleteInfoInvoiceInfoByCustomerIds(ids);
            deleteInfoPointsRecordByCustomerIds(ids);
            deleteInfoReferralByReferrerIds(ids);
            deleteInfoStatusLogByCustomerIds(ids);
    }


    private void validateInfoExists(Long id) {
        if (infoMapper.selectById(id) == null) {
            throw exception(INFO_NOT_EXISTS);
        }
    }

    @Override
    public InfoDO getInfo(Long id) {
        return infoMapper.selectById(id);
    }

    @Override
    public PageResult<InfoDO> getInfoPage(InfoPageReqVO pageReqVO) {
        return infoMapper.selectPage(pageReqVO);
    }

    // ==================== 子表（客户账户操作日志） ====================

    @Override
    public PageResult<InfoAccountLogDO> getInfoAccountLogPage(PageParam pageReqVO, Long customerId) {
        return infoAccountLogMapper.selectPage(pageReqVO, customerId);
    }

    @Override
    public Long createInfoAccountLog(InfoAccountLogDO infoAccountLog) {
        infoAccountLog.clean(); // 清理掉创建、更新时间等相关属性值
        infoAccountLogMapper.insert(infoAccountLog);
        return infoAccountLog.getId();
    }

    @Override
    public void updateInfoAccountLog(InfoAccountLogDO infoAccountLog) {
        // 校验存在
        validateInfoAccountLogExists(infoAccountLog.getId());
        // 更新
        infoAccountLog.clean(); // 解决更新情况下：updateTime 不更新
        infoAccountLogMapper.updateById(infoAccountLog);
    }

    @Override
    public void deleteInfoAccountLog(Long id) {
        // 删除
        infoAccountLogMapper.deleteById(id);
    }

	@Override
	public void deleteInfoAccountLogListByIds(List<Long> ids) {
        // 删除
        infoAccountLogMapper.deleteByIds(ids);
	}

    @Override
    public InfoAccountLogDO getInfoAccountLog(Long id) {
        return infoAccountLogMapper.selectById(id);
    }

    private void validateInfoAccountLogExists(Long id) {
        if (infoAccountLogMapper.selectById(id) == null) {
            throw exception(INFO_ACCOUNT_LOG_NOT_EXISTS);
        }
    }

    private void deleteInfoAccountLogByCustomerId(Long customerId) {
        infoAccountLogMapper.deleteByCustomerId(customerId);
    }

	private void deleteInfoAccountLogByCustomerIds(List<Long> customerIds) {
        infoAccountLogMapper.deleteByCustomerIds(customerIds);
	}

    // ==================== 子表（客户收货地址） ====================

    @Override
    public PageResult<InfoAddressDO> getInfoAddressPage(PageParam pageReqVO, Long customerId) {
        return infoAddressMapper.selectPage(pageReqVO, customerId);
    }

    @Override
    public Long createInfoAddress(InfoAddressDO infoAddress) {
        // 校验是否已经存在
        if (infoAddressMapper.selectByCustomerId(infoAddress.getCustomerId()) != null) {
            throw exception(INFO_ADDRESS_EXISTS);
        }
        // 插入
        infoAddress.clean(); // 清理掉创建、更新时间等相关属性值
        infoAddressMapper.insert(infoAddress);
        return infoAddress.getId();
    }

    @Override
    public void updateInfoAddress(InfoAddressDO infoAddress) {
        // 校验存在
        validateInfoAddressExists(infoAddress.getId());
        // 更新
        infoAddress.clean(); // 解决更新情况下：updateTime 不更新
        infoAddressMapper.updateById(infoAddress);
    }

    @Override
    public void deleteInfoAddress(Long id) {
        // 删除
        infoAddressMapper.deleteById(id);
    }

	@Override
	public void deleteInfoAddressListByIds(List<Long> ids) {
        // 删除
        infoAddressMapper.deleteByIds(ids);
	}

    @Override
    public InfoAddressDO getInfoAddress(Long id) {
        return infoAddressMapper.selectById(id);
    }

    private void validateInfoAddressExists(Long id) {
        if (infoAddressMapper.selectById(id) == null) {
            throw exception(INFO_ADDRESS_NOT_EXISTS);
        }
    }

    private void deleteInfoAddressByCustomerId(Long customerId) {
        infoAddressMapper.deleteByCustomerId(customerId);
    }

	private void deleteInfoAddressByCustomerIds(List<Long> customerIds) {
        infoAddressMapper.deleteByCustomerIds(customerIds);
	}

    // ==================== 子表（客户发票信息） ====================

    @Override
    public PageResult<InfoInvoiceInfoDO> getInfoInvoiceInfoPage(PageParam pageReqVO, Long customerId) {
        return infoInvoiceInfoMapper.selectPage(pageReqVO, customerId);
    }

    @Override
    public Long createInfoInvoiceInfo(InfoInvoiceInfoDO infoInvoiceInfo) {
        infoInvoiceInfo.clean(); // 清理掉创建、更新时间等相关属性值
        infoInvoiceInfoMapper.insert(infoInvoiceInfo);
        return infoInvoiceInfo.getId();
    }

    @Override
    public void updateInfoInvoiceInfo(InfoInvoiceInfoDO infoInvoiceInfo) {
        // 校验存在
        validateInfoInvoiceInfoExists(infoInvoiceInfo.getId());
        // 更新
        infoInvoiceInfo.clean(); // 解决更新情况下：updateTime 不更新
        infoInvoiceInfoMapper.updateById(infoInvoiceInfo);
    }

    @Override
    public void deleteInfoInvoiceInfo(Long id) {
        // 删除
        infoInvoiceInfoMapper.deleteById(id);
    }

	@Override
	public void deleteInfoInvoiceInfoListByIds(List<Long> ids) {
        // 删除
        infoInvoiceInfoMapper.deleteByIds(ids);
	}

    @Override
    public InfoInvoiceInfoDO getInfoInvoiceInfo(Long id) {
        return infoInvoiceInfoMapper.selectById(id);
    }

    private void validateInfoInvoiceInfoExists(Long id) {
        if (infoInvoiceInfoMapper.selectById(id) == null) {
            throw exception(INFO_INVOICE_INFO_NOT_EXISTS);
        }
    }

    private void deleteInfoInvoiceInfoByCustomerId(Long customerId) {
        infoInvoiceInfoMapper.deleteByCustomerId(customerId);
    }

	private void deleteInfoInvoiceInfoByCustomerIds(List<Long> customerIds) {
        infoInvoiceInfoMapper.deleteByCustomerIds(customerIds);
	}

    // ==================== 子表（客户积分记录） ====================

    @Override
    public PageResult<InfoPointsRecordDO> getInfoPointsRecordPage(PageParam pageReqVO, Long customerId) {
        return infoPointsRecordMapper.selectPage(pageReqVO, customerId);
    }

    @Override
    public Long createInfoPointsRecord(InfoPointsRecordDO infoPointsRecord) {
        infoPointsRecord.clean(); // 清理掉创建、更新时间等相关属性值
        infoPointsRecordMapper.insert(infoPointsRecord);
        return infoPointsRecord.getId();
    }

    @Override
    public void updateInfoPointsRecord(InfoPointsRecordDO infoPointsRecord) {
        // 校验存在
        validateInfoPointsRecordExists(infoPointsRecord.getId());
        // 更新
        infoPointsRecord.clean(); // 解决更新情况下：updateTime 不更新
        infoPointsRecordMapper.updateById(infoPointsRecord);
    }

    @Override
    public void deleteInfoPointsRecord(Long id) {
        // 删除
        infoPointsRecordMapper.deleteById(id);
    }

	@Override
	public void deleteInfoPointsRecordListByIds(List<Long> ids) {
        // 删除
        infoPointsRecordMapper.deleteByIds(ids);
	}

    @Override
    public InfoPointsRecordDO getInfoPointsRecord(Long id) {
        return infoPointsRecordMapper.selectById(id);
    }

    private void validateInfoPointsRecordExists(Long id) {
        if (infoPointsRecordMapper.selectById(id) == null) {
            throw exception(INFO_POINTS_RECORD_NOT_EXISTS);
        }
    }

    private void deleteInfoPointsRecordByCustomerId(Long customerId) {
        infoPointsRecordMapper.deleteByCustomerId(customerId);
    }

	private void deleteInfoPointsRecordByCustomerIds(List<Long> customerIds) {
        infoPointsRecordMapper.deleteByCustomerIds(customerIds);
	}

    // ==================== 子表（客户推荐关系） ====================

    @Override
    public PageResult<InfoReferralDO> getInfoReferralPage(PageParam pageReqVO, Long referrerId) {
        return infoReferralMapper.selectPage(pageReqVO, referrerId);
    }

    @Override
    public Long createInfoReferral(InfoReferralDO infoReferral) {
        infoReferral.clean(); // 清理掉创建、更新时间等相关属性值
        infoReferralMapper.insert(infoReferral);
        return infoReferral.getId();
    }

    @Override
    public void updateInfoReferral(InfoReferralDO infoReferral) {
        // 校验存在
        validateInfoReferralExists(infoReferral.getId());
        // 更新
        infoReferral.clean(); // 解决更新情况下：updateTime 不更新
        infoReferralMapper.updateById(infoReferral);
    }

    @Override
    public void deleteInfoReferral(Long id) {
        // 删除
        infoReferralMapper.deleteById(id);
    }

	@Override
	public void deleteInfoReferralListByIds(List<Long> ids) {
        // 删除
        infoReferralMapper.deleteByIds(ids);
	}

    @Override
    public InfoReferralDO getInfoReferral(Long id) {
        return infoReferralMapper.selectById(id);
    }

    private void validateInfoReferralExists(Long id) {
        if (infoReferralMapper.selectById(id) == null) {
            throw exception(INFO_REFERRAL_NOT_EXISTS);
        }
    }

    private void deleteInfoReferralByReferrerId(Long referrerId) {
        infoReferralMapper.deleteByReferrerId(referrerId);
    }

	private void deleteInfoReferralByReferrerIds(List<Long> referrerIds) {
        infoReferralMapper.deleteByReferrerIds(referrerIds);
	}

    // ==================== 子表（客户状态变更记录） ====================

    @Override
    public PageResult<InfoStatusLogDO> getInfoStatusLogPage(PageParam pageReqVO, Long customerId) {
        return infoStatusLogMapper.selectPage(pageReqVO, customerId);
    }

    @Override
    public Long createInfoStatusLog(InfoStatusLogDO infoStatusLog) {
        infoStatusLog.clean(); // 清理掉创建、更新时间等相关属性值
        infoStatusLogMapper.insert(infoStatusLog);
        return infoStatusLog.getId();
    }

    @Override
    public void updateInfoStatusLog(InfoStatusLogDO infoStatusLog) {
        // 校验存在
        validateInfoStatusLogExists(infoStatusLog.getId());
        // 更新
        infoStatusLog.clean(); // 解决更新情况下：updateTime 不更新
        infoStatusLogMapper.updateById(infoStatusLog);
    }

    @Override
    public void deleteInfoStatusLog(Long id) {
        // 删除
        infoStatusLogMapper.deleteById(id);
    }

	@Override
	public void deleteInfoStatusLogListByIds(List<Long> ids) {
        // 删除
        infoStatusLogMapper.deleteByIds(ids);
	}

    @Override
    public InfoStatusLogDO getInfoStatusLog(Long id) {
        return infoStatusLogMapper.selectById(id);
    }

    private void validateInfoStatusLogExists(Long id) {
        if (infoStatusLogMapper.selectById(id) == null) {
            throw exception(INFO_STATUS_LOG_NOT_EXISTS);
        }
    }

    private void deleteInfoStatusLogByCustomerId(Long customerId) {
        infoStatusLogMapper.deleteByCustomerId(customerId);
    }

	private void deleteInfoStatusLogByCustomerIds(List<Long> customerIds) {
        infoStatusLogMapper.deleteByCustomerIds(customerIds);
	}

}