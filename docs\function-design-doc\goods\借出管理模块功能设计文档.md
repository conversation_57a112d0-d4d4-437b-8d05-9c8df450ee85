# 商品管理模块 - 借出管理功能设计文档

## 1. 模块概述

### 1.1 模块目的
借出管理模块是智能家装管理平台商品管理系统的专业业务模块，负责企业内部或外部客户的商品借出与归还流程管理。该模块适用于演示设备借用、工程测试、渠道走访等业务场景，通过规范化的借出流程确保商品资产的有效管理和可追溯性。

### 1.2 业务价值
- 建立完整的商品借出管理体系，规范借出申请、审核、出库、归还全流程
- 支持多种借出场景管理，满足演示、测试、走访等不同业务需求
- 提供借出状态实时跟踪，确保借出商品的安全和及时回收
- 实现借出成本核算和费用管理，支持借出业务的成本控制
- 建立借出数据统计分析，为资产管理和业务决策提供数据支持

### 1.3 功能架构
借出管理模块包含六个核心功能：
- **借出申请管理**: 借出申请的创建、编辑、审核和状态管理
- **借出流程控制**: 从申请到归还的完整流程管理和状态跟踪
- **筛选查询功能**: 多维度借出数据筛选和精确查询
- **归还处理功能**: 借出商品的归还登记和库存回收
- **费用核算管理**: 借出使用费用的计算和管理
- **数据统计分析**: 借出数据的统计分析和报表生成

## 2. 借出管理模块操作流程图

```mermaid
flowchart TD
    A[用户访问借出管理页面] --> B[权限验证]
    B --> C{权限验证通过?}
    C -->|否| D[显示权限不足提示]
    C -->|是| E[加载借出管理数据]
    
    E --> F[显示筛选搜索栏]
    E --> G[显示操作按钮区]
    E --> H[加载借出记录表格]
    E --> I[显示分页导航]
    
    F --> J[筛选条件设置]
    J --> K[申请人筛选]
    J --> L[客户信息筛选]
    J --> M[商品信息筛选]
    J --> N[状态筛选]
    J --> O[到期时间筛选]
    
    K --> P[组合筛选处理]
    L --> P
    M --> P
    N --> P
    O --> P
    
    P --> Q[点击搜索按钮]
    Q --> R[发送筛选请求]
    R --> S[更新借出记录表格]
    
    G --> T[借货申请按钮]
    T --> U[跳转借出申请页面]
    U --> V[填写借出申请信息]
    V --> W[选择申请人]
    V --> X[选择借出客户]
    V --> Y[选择借出商品]
    V --> Z[设置借出数量]
    V --> AA[设置预借日期]
    V --> BB[设置预还日期]
    BB --> CC[提交借出申请]
    CC --> DD{申请信息验证}
    DD -->|验证失败| EE[显示验证错误提示]
    DD -->|验证通过| FF[保存借出申请]
    FF --> GG[返回借出管理列表]
    
    H --> HH[借出记录数据展示]
    HH --> II[记录状态显示]
    HH --> JJ[借出信息展示]
    HH --> KK[时间信息展示]
    HH --> LL[费用信息展示]
    HH --> MM[操作按钮]
    
    II --> NN[借出中状态]
    II --> OO[已归还状态]
    II --> PP[已作废状态]
    
    MM --> QQ[详情查看]
    MM --> RR[归还操作]
    MM --> SS[编辑操作]
    MM --> TT[作废操作]
    
    QQ --> UU[显示借出详情]
    UU --> VV[借出申请信息]
    UU --> WW[商品详细信息]
    UU --> XX[客户联系信息]
    UU --> YY[借出流程记录]
    
    RR --> ZZ{归还权限检查}
    ZZ -->|无权限| AAA[显示权限不足]
    ZZ -->|有权限| BBB[归还确认界面]
    BBB --> CCC[确认归还商品]
    CCC --> DDD[检查商品状态]
    DDD --> EEE[录入归还数量]
    EEE --> FFF[计算使用费用]
    FFF --> GGG{确认归还?}
    GGG -->|否| HHH[取消归还]
    GGG -->|是| III[执行归还操作]
    III --> JJJ[更新借出状态]
    JJJ --> KKK[回收库存数量]
    KKK --> LLL[记录归还时间]
    LLL --> MMM[计算产生费用]
    MMM --> NNN[发送归还通知]
    
    SS --> OOO{编辑权限检查}
    OOO -->|无权限| PPP[显示权限不足]
    OOO -->|状态不允许| QQQ[显示状态限制提示]
    OOO -->|可编辑| RRR[跳转编辑页面]
    RRR --> SSS[修改借出信息]
    SSS --> TTT[保存修改]
    
    TT --> UUU{作废权限检查}
    UUU -->|无权限| VVV[显示权限不足]
    UUU -->|状态不允许| WWW[显示状态限制提示]
    UUU -->|可作废| XXX[作废确认]
    XXX --> YYY{确认作废?}
    YYY -->|否| ZZZ[取消作废]
    YYY -->|是| AAAA[执行作废操作]
    AAAA --> BBBB[更新记录状态]
    BBBB --> CCCC[回收库存数量]
    CCCC --> DDDD[记录作废原因]
    
    I --> EEEE[分页功能]
    EEEE --> FFFF[页码切换]
    EEEE --> GGGG[页面大小设置]
    FFFF --> HHHH[加载对应页面数据]
    GGGG --> HHHH
    
    style A fill:#e1f5fe
    style C fill:#fff3e0
    style D fill:#ffebee
    style DD fill:#fff3e0
    style EE fill:#ffebee
    style ZZ fill:#fff3e0
    style AAA fill:#ffebee
    style GGG fill:#fff3e0
    style OOO fill:#fff3e0
    style PPP fill:#ffebee
    style QQQ fill:#fff8e1
    style UUU fill:#fff3e0
    style VVV fill:#ffebee
    style WWW fill:#fff8e1
    style XXX fill:#fff3e0
    style YYY fill:#fff3e0
    style NNN fill:#e8f5e8
```

### 流程说明
借出管理模块的操作流程主要包含以下几个核心环节：

1. **权限验证与数据加载**：验证用户访问权限，加载借出记录数据和分页信息
2. **多维度筛选查询**：支持申请人、客户信息、商品信息、状态、到期时间等多条件筛选
3. **借出申请流程**：从申请创建到信息验证、保存的完整流程管理
4. **借出状态管理**：借出中、已归还、已作废三种状态的流转和控制
5. **归还处理流程**：归还确认、库存回收、费用计算的完整归还流程
6. **权限控制机制**：根据用户权限和记录状态控制操作可见性和可执行性

## 3. 详细功能设计

### 3.1 筛选查询功能

#### 3.1.1 多维度筛选条件
**功能描述**: 提供全面的借出数据筛选功能

**筛选字段**:
- **申请人筛选**: 按借出申请提交人筛选
  - 下拉选择申请人员
  - 支持申请人姓名搜索
  - 显示每个申请人的借出记录数
- **客户信息筛选**: 按客户姓名或手机号筛选
  - 客户姓名模糊匹配
  - 手机号精确或模糊搜索
  - 支持客户编号搜索
- **商品信息筛选**: 按商品名称或序列号筛选
  - 商品名称模糊匹配
  - 序列号精确搜索
  - 商品型号搜索
- **状态筛选**: 按借出状态筛选
  - 借出中：正在借出使用的记录
  - 已归还：已完成归还的记录
  - 已作废：已作废的借出记录
- **到期时间筛选**: 按借出期限筛选
  - 输入天数范围
  - 筛选指定天数内到期的借出
  - 支持逾期借出筛选

#### 3.1.2 搜索操作功能
**功能描述**: 搜索相关的操作和管理功能

**操作按钮**:
- **搜索按钮**: 执行筛选条件查询
  - 组合所有筛选条件
  - 实时更新借出记录表格
  - 显示筛选结果数量
- **重置功能**: 清除所有筛选条件
  - 恢复默认数据展示
  - 清除缓存的筛选条件
- **高级搜索**: 更多搜索选项
  - 借出金额范围搜索
  - 借出时间范围搜索
  - 自定义字段搜索

### 3.2 借出申请管理

#### 3.2.1 借货申请功能
**功能描述**: 创建新的借出申请，支持非销售出库场景下的物料外借管理

**页面入口**:
- **主要入口**: 商品管理 → 借出管理 → 新增
- **快速入口**: 商品管理相关页面的快速借出链接
- **业务入口**: 客户服务、展会管理等业务场景的借出申请

**功能架构**:
借出申请新增页面包含四个核心功能区域：
- **申请人信息管理**: 借用方身份和借出计划信息
- **商品选择管理**: 借出商品的选择和数量设置
- **备注信息管理**: 借出用途和附加说明信息
- **操作流程控制**: 申请的提交、保存和流程控制

**业务流程**:
借出申请的完整业务流程：
```
借出申请创建 → 审核通过 → 出库（绑定SN） → 借出中 → 用户归还 → 入库登记 → 状态变更为"已归还"
```

**申请人信息管理功能**:
- **客户信息选择**: 选择借出申请的客户或内部借用对象
  - 外部客户：正常的客户借用
    - 客户列表展示
    - 支持客户搜索
    - 客户信息联动
  - 内部借用对象：内部项目部、员工等
    - 项目部列表
    - 员工列表
    - 部门信息联动
  - 搜索功能：
    - 姓名搜索：支持客户姓名模糊搜索
    - 手机号搜索：支持手机号精确和模糊搜索
    - 拼音首字母搜索：支持拼音首字母快速定位
    - 实时搜索：输入过程中实时搜索建议
- **地址信息管理**: 管理客户的地址信息
  - 自动填充：选择客户后自动带出默认地址
  - 多地址支持：若客户有多个地址，提供下拉选择
  - 地址验证：验证地址信息的完整性
  - 新增地址：支持为客户新增地址信息
- **借出时间管理**: 管理借出的时间计划
  - 预借出日期：计划开始借出的日期
    - 日期选择器
    - 不能早于当前日期
    - 支持快速日期选择
  - 预计归还日期：借用物品计划归还时间
    - 日期选择器
    - 不能早于预借出日期
    - 自动计算借出期限
  - 时间验证：
    - 日期合理性：预借出日期不晚于预计归还日期
    - 期限限制：借出期限不超过系统设定的最大期限
    - 冲突检查：检查商品在该时间段是否已被借出
- **费用管理**: 管理借出产生的费用
  - 免费借出：不产生任何费用
  - 按天收费：按借出天数计算费用
  - 固定费用：固定的借出费用
  - 押金管理：收取借出押金

**商品选择管理功能**:
- **商品选择器**: 弹出式商品选择器，支持多种选择方式
  - 分页显示：大量商品的分页展示
  - 分类筛选：按商品分类筛选
  - 搜索功能：商品名称、编码、型号搜索
  - 库存显示：显示当前可借出库存
  - 商品信息展示：
    - 商品缩略图：商品图片展示
    - 商品编码名称：编码和名称信息
    - 型号规格单位：详细规格信息
    - 当前库存：可借出的库存数量
    - SN管理提示：是否需要序列号管理
- **商品明细管理**: 管理已选择的借出商品明细
  - 明细信息：
    - 商品基本信息：图片、编码、名称、型号、规格、单位
    - 出借数量：可手动输入，限制为当前库存上限
    - 库存参考：显示当前可用库存
    - 操作功能：删除商品、修改数量
  - 数量验证：
    - 非负验证：出借数量必须大于0
    - 库存验证：不能超过当前可用库存
    - 合理性验证：数量必须为整数
- **序列号管理**: 管理启用SN控制的商品序列号
  - SN标识：商品明细显示SN管理提示
  - 序列号预分配：为借出商品预分配序列号
  - 序列号验证：确保序列号的唯一性
  - 序列号跟踪：建立序列号与借出记录的关联

**备注信息管理功能**:
- **借出用途管理**: 管理借出的用途分类和说明
  - 用途分类：
    - 展会用途：用于展会展示的借出
    - 客户试用：客户试用体验的借出
    - 内部测试：内部测试使用的借出
    - 其他用途：自定义的其他用途
- **备注说明管理**: 管理借出的详细说明信息
  - 多行文本输入：支持详细的备注说明
  - 常用备注模板：提供常用的备注模板
  - 字符限制：限制备注的字符长度
  - 格式验证：验证备注内容的格式
- **附件上传管理**: 管理借出相关的附件文件
  - 附件类型：
    - 借用协议：上传借用协议文件
    - 现场照片：上传相关的现场照片
    - 其他文件：上传其他相关文件
  - 上传功能：
    - 文件格式限制：限制上传文件的格式
    - 文件大小限制：限制上传文件的大小
    - 文件预览：支持文件的预览功能
    - 文件删除：支持删除已上传的文件

**数据验证功能**:
- **提交前校验**: 提交申请前的完整性校验
  - 校验规则：
    - 客户信息完整：客户信息必须完整填写
    - 商品选择验证：至少选择一种商品
    - 数量验证：所有商品出借数量必须大于0
    - 日期验证：预借出日期不晚于预计归还日期
    - 必填项验证：所有必填项必须填写
- **业务规则验证**: 验证业务规则的合理性
  - 业务验证：
    - 库存充足性：验证库存是否充足
    - 借出权限：验证商品的借出权限
    - 时间冲突：验证时间是否冲突
    - 客户状态：验证客户状态是否正常

**操作流程控制**:
- **草稿保存功能**: 保存借出申请草稿
  - 手动保存：用户主动保存草稿
  - 自动保存：定时自动保存草稿
  - 草稿恢复：支持恢复已保存的草稿
  - 草稿管理：管理多个草稿版本
- **提交申请功能**: 正式提交借出申请
  - 完整性校验：校验申请信息的完整性
  - 业务规则验证：验证业务规则
  - 申请单生成：生成正式的借出申请单
  - 状态更新：更新申请状态
  - 通知发送：发送审核通知
- **取消操作功能**: 取消当前的申请编辑
  - 确认提示：确认是否取消当前操作
  - 数据保护：提醒保存未提交的数据
  - 页面清空：清空页面编辑数据
  - 返回列表：返回借出管理列表页面

#### 3.2.2 申请审核流程
**功能描述**: 借出申请的审核和批准

**审核流程**:
- **审核权限验证**: 检查用户审核权限
- **申请信息审查**: 审查申请信息完整性
- **库存可用性确认**: 确认借出商品库存充足
- **审核决策录入**:
  - 审核意见填写
  - 审核结果选择（通过/拒绝）
  - 审核时间记录
- **审核结果处理**:
  - 通过：更新状态，准备出库
  - 拒绝：返回申请状态，记录拒绝原因

### 3.3 借出流程控制

#### 3.3.1 借出状态管理
**功能描述**: 管理借出记录的状态流转

**状态类型**:
- **借出中状态**:
  - 借出流程正在进行
  - 商品已出库，未归还或部分归还
  - 可进行归还操作
  - 可编辑部分信息
- **已归还状态**:
  - 借出流程已完成
  - 商品已全部归还
  - 所有字段只读
  - 可查看归还时间和费用明细
- **已作废状态**:
  - 借出记录无效
  - 不参与统计或再处理
  - 不允许任何操作
  - 仅用于留档

#### 3.3.2 出库处理功能
**功能描述**: 审核通过后的商品出库处理

**出库流程**:
- **出库准备**: 确认出库商品和数量
- **库存扣减**: 从库存中扣减借出数量
- **出库记录**: 生成借出出库记录
- **状态更新**: 更新借出状态为"借出中"
- **通知发送**: 发送出库完成通知

### 3.4 归还处理功能

#### 3.4.1 归还登记功能
**功能描述**: 处理借出商品的归还

**归还流程**:
- **归还确认**: 确认归还的商品和数量
- **商品检查**: 检查归还商品的状态和完整性
- **数量核对**: 核对归还数量与借出数量
- **状态评估**: 评估商品使用状态和损耗情况
- **归还登记**: 登记实际归还时间和数量
- **库存回收**: 将归还商品重新入库

#### 3.4.2 费用核算功能
**功能描述**: 计算和管理借出使用费用

**费用计算**:
- **使用时间**: 计算实际使用时间
- **费用标准**: 根据商品类型和使用时间计算费用
- **损耗费用**: 计算商品损耗和维修费用
- **逾期费用**: 计算逾期归还的额外费用
- **费用汇总**: 汇总总的使用费用

**费用管理**:
- **费用确认**: 确认计算的费用金额
- **费用收取**: 处理费用收取和支付
- **费用记录**: 记录费用明细和支付状态
- **费用统计**: 统计借出业务的费用收入

### 3.5 数据展示功能

#### 3.5.1 借出记录表格
**功能描述**: 展示借出记录的详细信息

**表格字段**:
- **申请日期**: 借出申请提交时间
- **申请人**: 借出申请人姓名
- **客户信息**: 借出对象姓名和联系方式
- **借出状态**: 当前处理状态（借出中、已归还、已作废）
- **出借商品**: 商品名称、型号等详细信息
- **出借数量**: 实际借出的商品数量
- **出库数量**: 已从仓库出库的数量
- **预借日期**: 计划开始使用日期
- **预还日期**: 预计归还时间
- **归还日期**: 实际归还时间（已归还时显示）
- **产生费用**: 归还后产生的使用费用
- **操作按钮**: 详情查看、归还、编辑、作废等操作

#### 3.5.2 详情查看功能
**功能描述**: 查看借出记录的详细信息

**详情内容**:
- **借出申请信息**: 申请人、申请时间、申请原因
- **商品详细信息**: 商品规格、序列号、价值等
- **客户联系信息**: 客户详细联系方式和地址
- **借出流程记录**: 申请、审核、出库、归还等流程记录
- **费用明细**: 详细的费用计算和支付记录
- **附件文件**: 相关的合同、协议等附件

### 3.6 分页导航功能

#### 3.6.1 分页显示功能
**功能描述**: 支持大量数据的分页显示

**分页特性**:
- **页码导航**: 支持页码跳转和翻页
- **页面大小**: 支持自定义每页显示数量
- **总数统计**: 显示总记录数和总页数
- **快速跳转**: 支持快速跳转到指定页面

#### 3.6.2 数据加载优化
**功能描述**: 优化数据加载性能

**优化策略**:
- **懒加载**: 按需加载数据，提升页面响应速度
- **缓存机制**: 缓存已加载的数据，减少重复请求
- **预加载**: 预加载下一页数据，提升用户体验
- **数据压缩**: 压缩传输数据，减少网络开销

## 4. 用户界面设计

### 4.1 页面布局设计
- **筛选搜索区**: 多维度筛选条件和搜索功能
- **操作按钮区**: 借货申请等主要操作按钮
- **数据表格区**: 借出记录数据表格展示
- **分页导航区**: 分页控制和导航功能

### 4.2 交互设计规范
- **状态标识**: 直观的借出状态颜色和图标
- **操作确认**: 重要操作的确认弹窗
- **数据加载**: 加载状态和进度提示
- **响应反馈**: 操作成功/失败的即时反馈

### 4.3 响应式设计
- **PC端**: 完整功能展示，多列表格布局
- **平板端**: 适配中等屏幕，关键信息优先
- **移动端**: 卡片式布局，核心功能保留

## 5. 权限控制

### 5.1 功能权限
- **查看权限**: 借出数据查看权限
- **申请权限**: 借出申请创建权限
- **审核权限**: 借出申请审核权限
- **归还权限**: 借出商品归还权限
- **作废权限**: 借出记录作废权限

### 5.2 数据权限
- **申请人权限**: 主要查看自己申请的借出记录
- **部门权限**: 按部门范围控制数据访问
- **客户权限**: 按客户范围控制借出范围

## 6. 异常处理

### 6.1 业务异常
- **库存不足**: 借出申请时库存不足的处理
- **逾期归还**: 逾期未归还的提醒和处理
- **商品损坏**: 归还时商品损坏的处理

### 6.2 系统异常
- **数据异常**: 借出数据异常的检测和修复
- **状态异常**: 借出状态异常的处理
- **权限异常**: 权限验证失败的处理

---

**文档版本**: v1.0
**编写日期**: 2025-07-02
**编写人员**: AI系统架构师
**审核状态**: 待审核
