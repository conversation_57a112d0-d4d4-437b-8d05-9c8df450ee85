package com.muzi.yichao.module.customer.controller.admin.info;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.muzi.yichao.framework.common.pojo.PageParam;
import com.muzi.yichao.framework.common.pojo.PageResult;
import com.muzi.yichao.framework.common.pojo.CommonResult;
import com.muzi.yichao.framework.common.util.object.BeanUtils;
import static com.muzi.yichao.framework.common.pojo.CommonResult.success;

import com.muzi.yichao.framework.excel.core.util.ExcelUtils;

import com.muzi.yichao.framework.apilog.core.annotation.ApiAccessLog;
import static com.muzi.yichao.framework.apilog.core.enums.OperateTypeEnum.*;

import com.muzi.yichao.module.customer.controller.admin.info.vo.*;
import com.muzi.yichao.module.customer.dal.dataobject.info.InfoDO;
import com.muzi.yichao.module.customer.dal.dataobject.info.InfoAccountLogDO;
import com.muzi.yichao.module.customer.dal.dataobject.info.InfoAddressDO;
import com.muzi.yichao.module.customer.dal.dataobject.info.InfoInvoiceInfoDO;
import com.muzi.yichao.module.customer.dal.dataobject.info.InfoPointsRecordDO;
import com.muzi.yichao.module.customer.dal.dataobject.info.InfoReferralDO;
import com.muzi.yichao.module.customer.dal.dataobject.info.InfoStatusLogDO;
import com.muzi.yichao.module.customer.service.info.InfoService;

@Tag(name = "管理后台 - 客户数据")
@RestController
@RequestMapping("/customer/info")
@Validated
public class InfoController {

    @Resource
    private InfoService infoService;

    @PostMapping("/create")
    @Operation(summary = "创建客户数据")
    @PreAuthorize("@ss.hasPermission('customer:info:create')")
    public CommonResult<Long> createInfo(@Valid @RequestBody InfoSaveReqVO createReqVO) {
        return success(infoService.createInfo(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新客户数据")
    @PreAuthorize("@ss.hasPermission('customer:info:update')")
    public CommonResult<Boolean> updateInfo(@Valid @RequestBody InfoSaveReqVO updateReqVO) {
        infoService.updateInfo(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除客户数据")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('customer:info:delete')")
    public CommonResult<Boolean> deleteInfo(@RequestParam("id") Long id) {
        infoService.deleteInfo(id);
        return success(true);
    }

    @DeleteMapping("/delete-list")
    @Parameter(name = "ids", description = "编号", required = true)
    @Operation(summary = "批量删除客户数据")
                @PreAuthorize("@ss.hasPermission('customer:info:delete')")
    public CommonResult<Boolean> deleteInfoList(@RequestParam("ids") List<Long> ids) {
        infoService.deleteInfoListByIds(ids);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得客户数据")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('customer:info:query')")
    public CommonResult<InfoRespVO> getInfo(@RequestParam("id") Long id) {
        InfoDO info = infoService.getInfo(id);
        return success(BeanUtils.toBean(info, InfoRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得客户数据分页")
    @PreAuthorize("@ss.hasPermission('customer:info:query')")
    public CommonResult<PageResult<InfoRespVO>> getInfoPage(@Valid InfoPageReqVO pageReqVO) {
        PageResult<InfoDO> pageResult = infoService.getInfoPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InfoRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出客户数据 Excel")
    @PreAuthorize("@ss.hasPermission('customer:info:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportInfoExcel(@Valid InfoPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<InfoDO> list = infoService.getInfoPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "客户数据.xls", "数据", InfoRespVO.class,
                        BeanUtils.toBean(list, InfoRespVO.class));
    }

    // ==================== 子表（客户账户操作日志） ====================

    @GetMapping("/info-account-log/page")
    @Operation(summary = "获得客户账户操作日志分页")
    @Parameter(name = "customerId", description = "客户ID")
    @PreAuthorize("@ss.hasPermission('customer:info:query')")
    public CommonResult<PageResult<InfoAccountLogDO>> getInfoAccountLogPage(PageParam pageReqVO,
                                                                                        @RequestParam("customerId") Long customerId) {
        return success(infoService.getInfoAccountLogPage(pageReqVO, customerId));
    }

    @PostMapping("/info-account-log/create")
    @Operation(summary = "创建客户账户操作日志")
    @PreAuthorize("@ss.hasPermission('customer:info:create')")
    public CommonResult<Long> createInfoAccountLog(@Valid @RequestBody InfoAccountLogDO infoAccountLog) {
        return success(infoService.createInfoAccountLog(infoAccountLog));
    }

    @PutMapping("/info-account-log/update")
    @Operation(summary = "更新客户账户操作日志")
    @PreAuthorize("@ss.hasPermission('customer:info:update')")
    public CommonResult<Boolean> updateInfoAccountLog(@Valid @RequestBody InfoAccountLogDO infoAccountLog) {
        infoService.updateInfoAccountLog(infoAccountLog);
        return success(true);
    }

    @DeleteMapping("/info-account-log/delete")
    @Parameter(name = "id", description = "编号", required = true)
    @Operation(summary = "删除客户账户操作日志")
    @PreAuthorize("@ss.hasPermission('customer:info:delete')")
    public CommonResult<Boolean> deleteInfoAccountLog(@RequestParam("id") Long id) {
        infoService.deleteInfoAccountLog(id);
        return success(true);
    }

    @DeleteMapping("/info-account-log/delete-list")
    @Parameter(name = "ids", description = "编号", required = true)
    @Operation(summary = "批量删除客户账户操作日志")
    @PreAuthorize("@ss.hasPermission('customer:info:delete')")
    public CommonResult<Boolean> deleteInfoAccountLogList(@RequestParam("ids") List<Long> ids) {
        infoService.deleteInfoAccountLogListByIds(ids);
        return success(true);
    }

	@GetMapping("/info-account-log/get")
	@Operation(summary = "获得客户账户操作日志")
	@Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('customer:info:query')")
	public CommonResult<InfoAccountLogDO> getInfoAccountLog(@RequestParam("id") Long id) {
	    return success(infoService.getInfoAccountLog(id));
	}

    // ==================== 子表（客户收货地址） ====================

    @GetMapping("/info-address/page")
    @Operation(summary = "获得客户收货地址分页")
    @Parameter(name = "customerId", description = "客户ID")
    @PreAuthorize("@ss.hasPermission('customer:info:query')")
    public CommonResult<PageResult<InfoAddressDO>> getInfoAddressPage(PageParam pageReqVO,
                                                                                        @RequestParam("customerId") Long customerId) {
        return success(infoService.getInfoAddressPage(pageReqVO, customerId));
    }

    @PostMapping("/info-address/create")
    @Operation(summary = "创建客户收货地址")
    @PreAuthorize("@ss.hasPermission('customer:info:create')")
    public CommonResult<Long> createInfoAddress(@Valid @RequestBody InfoAddressDO infoAddress) {
        return success(infoService.createInfoAddress(infoAddress));
    }

    @PutMapping("/info-address/update")
    @Operation(summary = "更新客户收货地址")
    @PreAuthorize("@ss.hasPermission('customer:info:update')")
    public CommonResult<Boolean> updateInfoAddress(@Valid @RequestBody InfoAddressDO infoAddress) {
        infoService.updateInfoAddress(infoAddress);
        return success(true);
    }

    @DeleteMapping("/info-address/delete")
    @Parameter(name = "id", description = "编号", required = true)
    @Operation(summary = "删除客户收货地址")
    @PreAuthorize("@ss.hasPermission('customer:info:delete')")
    public CommonResult<Boolean> deleteInfoAddress(@RequestParam("id") Long id) {
        infoService.deleteInfoAddress(id);
        return success(true);
    }

    @DeleteMapping("/info-address/delete-list")
    @Parameter(name = "ids", description = "编号", required = true)
    @Operation(summary = "批量删除客户收货地址")
    @PreAuthorize("@ss.hasPermission('customer:info:delete')")
    public CommonResult<Boolean> deleteInfoAddressList(@RequestParam("ids") List<Long> ids) {
        infoService.deleteInfoAddressListByIds(ids);
        return success(true);
    }

	@GetMapping("/info-address/get")
	@Operation(summary = "获得客户收货地址")
	@Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('customer:info:query')")
	public CommonResult<InfoAddressDO> getInfoAddress(@RequestParam("id") Long id) {
	    return success(infoService.getInfoAddress(id));
	}

    // ==================== 子表（客户发票信息） ====================

    @GetMapping("/info-invoice-info/page")
    @Operation(summary = "获得客户发票信息分页")
    @Parameter(name = "customerId", description = "客户ID")
    @PreAuthorize("@ss.hasPermission('customer:info:query')")
    public CommonResult<PageResult<InfoInvoiceInfoDO>> getInfoInvoiceInfoPage(PageParam pageReqVO,
                                                                                        @RequestParam("customerId") Long customerId) {
        return success(infoService.getInfoInvoiceInfoPage(pageReqVO, customerId));
    }

    @PostMapping("/info-invoice-info/create")
    @Operation(summary = "创建客户发票信息")
    @PreAuthorize("@ss.hasPermission('customer:info:create')")
    public CommonResult<Long> createInfoInvoiceInfo(@Valid @RequestBody InfoInvoiceInfoDO infoInvoiceInfo) {
        return success(infoService.createInfoInvoiceInfo(infoInvoiceInfo));
    }

    @PutMapping("/info-invoice-info/update")
    @Operation(summary = "更新客户发票信息")
    @PreAuthorize("@ss.hasPermission('customer:info:update')")
    public CommonResult<Boolean> updateInfoInvoiceInfo(@Valid @RequestBody InfoInvoiceInfoDO infoInvoiceInfo) {
        infoService.updateInfoInvoiceInfo(infoInvoiceInfo);
        return success(true);
    }

    @DeleteMapping("/info-invoice-info/delete")
    @Parameter(name = "id", description = "编号", required = true)
    @Operation(summary = "删除客户发票信息")
    @PreAuthorize("@ss.hasPermission('customer:info:delete')")
    public CommonResult<Boolean> deleteInfoInvoiceInfo(@RequestParam("id") Long id) {
        infoService.deleteInfoInvoiceInfo(id);
        return success(true);
    }

    @DeleteMapping("/info-invoice-info/delete-list")
    @Parameter(name = "ids", description = "编号", required = true)
    @Operation(summary = "批量删除客户发票信息")
    @PreAuthorize("@ss.hasPermission('customer:info:delete')")
    public CommonResult<Boolean> deleteInfoInvoiceInfoList(@RequestParam("ids") List<Long> ids) {
        infoService.deleteInfoInvoiceInfoListByIds(ids);
        return success(true);
    }

	@GetMapping("/info-invoice-info/get")
	@Operation(summary = "获得客户发票信息")
	@Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('customer:info:query')")
	public CommonResult<InfoInvoiceInfoDO> getInfoInvoiceInfo(@RequestParam("id") Long id) {
	    return success(infoService.getInfoInvoiceInfo(id));
	}

    // ==================== 子表（客户积分记录） ====================

    @GetMapping("/info-points-record/page")
    @Operation(summary = "获得客户积分记录分页")
    @Parameter(name = "customerId", description = "客户ID")
    @PreAuthorize("@ss.hasPermission('customer:info:query')")
    public CommonResult<PageResult<InfoPointsRecordDO>> getInfoPointsRecordPage(PageParam pageReqVO,
                                                                                        @RequestParam("customerId") Long customerId) {
        return success(infoService.getInfoPointsRecordPage(pageReqVO, customerId));
    }

    @PostMapping("/info-points-record/create")
    @Operation(summary = "创建客户积分记录")
    @PreAuthorize("@ss.hasPermission('customer:info:create')")
    public CommonResult<Long> createInfoPointsRecord(@Valid @RequestBody InfoPointsRecordDO infoPointsRecord) {
        return success(infoService.createInfoPointsRecord(infoPointsRecord));
    }

    @PutMapping("/info-points-record/update")
    @Operation(summary = "更新客户积分记录")
    @PreAuthorize("@ss.hasPermission('customer:info:update')")
    public CommonResult<Boolean> updateInfoPointsRecord(@Valid @RequestBody InfoPointsRecordDO infoPointsRecord) {
        infoService.updateInfoPointsRecord(infoPointsRecord);
        return success(true);
    }

    @DeleteMapping("/info-points-record/delete")
    @Parameter(name = "id", description = "编号", required = true)
    @Operation(summary = "删除客户积分记录")
    @PreAuthorize("@ss.hasPermission('customer:info:delete')")
    public CommonResult<Boolean> deleteInfoPointsRecord(@RequestParam("id") Long id) {
        infoService.deleteInfoPointsRecord(id);
        return success(true);
    }

    @DeleteMapping("/info-points-record/delete-list")
    @Parameter(name = "ids", description = "编号", required = true)
    @Operation(summary = "批量删除客户积分记录")
    @PreAuthorize("@ss.hasPermission('customer:info:delete')")
    public CommonResult<Boolean> deleteInfoPointsRecordList(@RequestParam("ids") List<Long> ids) {
        infoService.deleteInfoPointsRecordListByIds(ids);
        return success(true);
    }

	@GetMapping("/info-points-record/get")
	@Operation(summary = "获得客户积分记录")
	@Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('customer:info:query')")
	public CommonResult<InfoPointsRecordDO> getInfoPointsRecord(@RequestParam("id") Long id) {
	    return success(infoService.getInfoPointsRecord(id));
	}

    // ==================== 子表（客户推荐关系） ====================

    @GetMapping("/info-referral/page")
    @Operation(summary = "获得客户推荐关系分页")
    @Parameter(name = "referrerId", description = "推荐人ID")
    @PreAuthorize("@ss.hasPermission('customer:info:query')")
    public CommonResult<PageResult<InfoReferralDO>> getInfoReferralPage(PageParam pageReqVO,
                                                                                        @RequestParam("referrerId") Long referrerId) {
        return success(infoService.getInfoReferralPage(pageReqVO, referrerId));
    }

    @PostMapping("/info-referral/create")
    @Operation(summary = "创建客户推荐关系")
    @PreAuthorize("@ss.hasPermission('customer:info:create')")
    public CommonResult<Long> createInfoReferral(@Valid @RequestBody InfoReferralDO infoReferral) {
        return success(infoService.createInfoReferral(infoReferral));
    }

    @PutMapping("/info-referral/update")
    @Operation(summary = "更新客户推荐关系")
    @PreAuthorize("@ss.hasPermission('customer:info:update')")
    public CommonResult<Boolean> updateInfoReferral(@Valid @RequestBody InfoReferralDO infoReferral) {
        infoService.updateInfoReferral(infoReferral);
        return success(true);
    }

    @DeleteMapping("/info-referral/delete")
    @Parameter(name = "id", description = "编号", required = true)
    @Operation(summary = "删除客户推荐关系")
    @PreAuthorize("@ss.hasPermission('customer:info:delete')")
    public CommonResult<Boolean> deleteInfoReferral(@RequestParam("id") Long id) {
        infoService.deleteInfoReferral(id);
        return success(true);
    }

    @DeleteMapping("/info-referral/delete-list")
    @Parameter(name = "ids", description = "编号", required = true)
    @Operation(summary = "批量删除客户推荐关系")
    @PreAuthorize("@ss.hasPermission('customer:info:delete')")
    public CommonResult<Boolean> deleteInfoReferralList(@RequestParam("ids") List<Long> ids) {
        infoService.deleteInfoReferralListByIds(ids);
        return success(true);
    }

	@GetMapping("/info-referral/get")
	@Operation(summary = "获得客户推荐关系")
	@Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('customer:info:query')")
	public CommonResult<InfoReferralDO> getInfoReferral(@RequestParam("id") Long id) {
	    return success(infoService.getInfoReferral(id));
	}

    // ==================== 子表（客户状态变更记录） ====================

    @GetMapping("/info-status-log/page")
    @Operation(summary = "获得客户状态变更记录分页")
    @Parameter(name = "customerId", description = "客户ID")
    @PreAuthorize("@ss.hasPermission('customer:info:query')")
    public CommonResult<PageResult<InfoStatusLogDO>> getInfoStatusLogPage(PageParam pageReqVO,
                                                                                        @RequestParam("customerId") Long customerId) {
        return success(infoService.getInfoStatusLogPage(pageReqVO, customerId));
    }

    @PostMapping("/info-status-log/create")
    @Operation(summary = "创建客户状态变更记录")
    @PreAuthorize("@ss.hasPermission('customer:info:create')")
    public CommonResult<Long> createInfoStatusLog(@Valid @RequestBody InfoStatusLogDO infoStatusLog) {
        return success(infoService.createInfoStatusLog(infoStatusLog));
    }

    @PutMapping("/info-status-log/update")
    @Operation(summary = "更新客户状态变更记录")
    @PreAuthorize("@ss.hasPermission('customer:info:update')")
    public CommonResult<Boolean> updateInfoStatusLog(@Valid @RequestBody InfoStatusLogDO infoStatusLog) {
        infoService.updateInfoStatusLog(infoStatusLog);
        return success(true);
    }

    @DeleteMapping("/info-status-log/delete")
    @Parameter(name = "id", description = "编号", required = true)
    @Operation(summary = "删除客户状态变更记录")
    @PreAuthorize("@ss.hasPermission('customer:info:delete')")
    public CommonResult<Boolean> deleteInfoStatusLog(@RequestParam("id") Long id) {
        infoService.deleteInfoStatusLog(id);
        return success(true);
    }

    @DeleteMapping("/info-status-log/delete-list")
    @Parameter(name = "ids", description = "编号", required = true)
    @Operation(summary = "批量删除客户状态变更记录")
    @PreAuthorize("@ss.hasPermission('customer:info:delete')")
    public CommonResult<Boolean> deleteInfoStatusLogList(@RequestParam("ids") List<Long> ids) {
        infoService.deleteInfoStatusLogListByIds(ids);
        return success(true);
    }

	@GetMapping("/info-status-log/get")
	@Operation(summary = "获得客户状态变更记录")
	@Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('customer:info:query')")
	public CommonResult<InfoStatusLogDO> getInfoStatusLog(@RequestParam("id") Long id) {
	    return success(infoService.getInfoStatusLog(id));
	}

}