<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.muzi.smarthome</groupId>
        <artifactId>yichao</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>yichao-module-erp</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        erp 包下，企业资源管理（Enterprise Resource Planning）。
        例如说：采购、销售、库存、财务、产品等等
    </description>

    <dependencies>
        <dependency>
            <groupId>com.muzi.smarthome</groupId>
            <artifactId>yichao-module-system</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.muzi.smarthome</groupId>
            <artifactId>yichao-spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.muzi.smarthome</groupId>
            <artifactId>yichao-spring-boot-starter-security</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.muzi.smarthome</groupId>
            <artifactId>yichao-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.muzi.smarthome</groupId>
            <artifactId>yichao-spring-boot-starter-redis</artifactId>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>com.muzi.smarthome</groupId>
            <artifactId>yichao-spring-boot-starter-excel</artifactId>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>com.muzi.smarthome</groupId>
            <artifactId>yichao-spring-boot-starter-test</artifactId>
        </dependency>

    </dependencies>

</project>