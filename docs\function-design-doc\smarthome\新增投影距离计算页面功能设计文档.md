# SaaS智能家装CRM系统 - 新增投影距离计算页面功能设计文档

## 1. 页面概述

### 1.1 页面目的
新增投影距离计算页面用于创建新的投影距离计算记录，提供计算参数的详细录入、计算结果的保存和项目关联功能。该页面是投影距离计算工具的扩展，用于建立完整的计算档案和历史记录。

### 1.2 业务价值
- 建立完整的投影计算档案，便于项目管理和历史追溯
- 提供计算记录的标准化管理，提升工作效率
- 支持计算结果的项目关联，确保计算与实际应用的一致性
- 建立计算知识库，为后续类似项目提供参考

### 1.3 页面位置
- **所属模块**: 智能家居模块 - 三方工具
- **业务流程位置**: 专业工具计算 → **新增计算记录** → 记录管理
- **关联页面**: 
  - 三方工具-投影距离页面（入口页面）
  - 方案列表页面（项目关联）

## 2. 新增投影距离计算页面操作流程图

```mermaid
flowchart TD
    A[用户点击新增计算记录] --> B[跳转新增计算页面]
    B --> C[权限验证]
    C --> D{权限验证通过?}
    D -->|否| E[显示权限不足提示]
    D -->|是| F[加载页面表单]

    F --> G[显示基本信息区]
    F --> H[显示计算参数区]
    F --> I[显示项目关联区]
    F --> J[显示备注说明区]

    G --> K[记录名称输入]
    G --> L[计算日期设置]
    G --> M[计算人员选择]
    G --> N[记录类型选择]

    H --> O[设备信息录入]
    H --> P[房间参数录入]
    H --> Q[屏幕参数录入]
    H --> R[安装要求录入]

    O --> S[设备品牌选择]
    O --> T[设备型号选择]
    O --> U[设备参数确认]

    P --> V[房间尺寸输入]
    P --> W[环境条件输入]
    P --> X[限制条件输入]

    Q --> Y[屏幕规格输入]
    Q --> Z[屏幕位置设置]
    Q --> AA[屏幕类型选择]

    R --> BB[安装方式选择]
    R --> CC[安装位置设置]
    R --> DD[特殊要求输入]

    I --> EE[项目选择]
    I --> FF[客户关联]
    I --> GG[方案关联]

    J --> HH[计算目的说明]
    J --> II[特殊要求备注]
    J --> JJ[注意事项记录]

    K --> KK[表单验证]
    S --> KK
    V --> KK
    Y --> KK
    EE --> KK

    KK --> LL{验证通过?}
    LL -->|否| MM[显示错误提示]
    LL -->|是| NN[执行计算]

    NN --> OO[调用计算引擎]
    OO --> PP[生成计算结果]
    PP --> QQ[显示计算预览]

    QQ --> RR[用户确认结果]
    RR --> SS{确认保存?}
    SS -->|否| H
    SS -->|是| TT[保存计算记录]

    TT --> UU[生成记录编号]
    UU --> VV[关联项目信息]
    VV --> WW[保存到数据库]
    WW --> XX[更新计算库]
    XX --> YY[保存成功提示]
    YY --> ZZ[跳转计算工具页面]

    style A fill:#e1f5fe
    style D fill:#fff3e0
    style E fill:#ffebee
    style LL fill:#f3e5f5
    style SS fill:#f3e5f5
    style TT fill:#e8f5e8
```

### 流程说明
新增投影距离计算页面的操作流程包含以下核心环节：

1. **页面访问与权限验证**：从投影距离工具页面跳转，进行权限验证
2. **基本信息录入**：录入计算记录的基本信息和元数据
3. **计算参数设置**：详细设置计算所需的各项参数
4. **项目关联配置**：关联相关的项目、客户和方案
5. **计算执行与保存**：执行计算并保存完整的计算记录

## 3. 详细功能设计

### 3.1 基本信息录入功能

#### 3.1.1 记录基本信息
**功能描述**: 录入计算记录的基本信息

**录入字段**:
- **记录名称**: 计算记录的名称，必填字段
- **记录描述**: 计算记录的详细描述
- **计算日期**: 计算的日期，默认当前日期
- **计算人员**: 执行计算的人员，默认当前用户
- **记录类型**: 计算记录的类型（方案设计/现场勘测/客户咨询/技术验证）
- **优先级**: 记录的重要程度（高/中/低）

#### 3.1.2 计算目的设置
**功能描述**: 设置计算的目的和用途

**目的类型**:
- **方案设计**: 用于智能家居方案的设计
- **现场勘测**: 用于现场勘测和验证
- **客户咨询**: 用于客户咨询和演示
- **技术验证**: 用于技术方案的验证
- **投标支持**: 用于项目投标的技术支持

#### 3.1.3 记录标签管理
**功能描述**: 为计算记录添加标签

**标签类型**:
- **项目标签**: 项目相关的标签
- **客户标签**: 客户相关的标签
- **技术标签**: 技术特征的标签
- **用途标签**: 用途相关的标签
- **自定义标签**: 用户自定义的标签

### 3.2 计算参数录入功能

#### 3.2.1 设备信息录入
**功能描述**: 录入投影设备的详细信息

**设备信息**:
- **设备品牌**: 选择投影设备的品牌
- **设备型号**: 选择具体的设备型号
- **设备参数**: 确认设备的技术参数
- **设备价格**: 设备的参考价格
- **设备来源**: 设备的采购来源和渠道
- **备选设备**: 备选的设备型号和参数

#### 3.2.2 房间参数录入
**功能描述**: 录入房间的详细参数

**房间参数**:
- **房间尺寸**: 长、宽、高的精确尺寸
- **房间形状**: 规则/不规则房间的形状描述
- **环境条件**: 光线、温度、湿度等环境条件
- **装修状态**: 房间的装修状态和材质
- **限制条件**: 梁柱、管道等限制条件
- **使用要求**: 房间的使用要求和限制

#### 3.2.3 屏幕参数录入
**功能描述**: 录入投影屏幕的详细参数

**屏幕参数**:
- **屏幕尺寸**: 屏幕的对角线尺寸
- **屏幕比例**: 屏幕的宽高比例
- **屏幕类型**: 白墙、白幕、灰幕、抗光幕等
- **屏幕位置**: 屏幕在房间中的安装位置
- **屏幕高度**: 屏幕的安装高度
- **观看距离**: 观看者到屏幕的距离

#### 3.2.4 安装要求录入
**功能描述**: 录入设备安装的要求和限制

**安装要求**:
- **安装方式**: 吊装、桌面、壁挂等方式
- **安装位置**: 设备的具体安装位置
- **安装高度**: 设备的安装高度要求
- **倾斜角度**: 设备的倾斜角度范围
- **维护空间**: 设备维护所需的空间
- **美观要求**: 安装的美观性要求

### 3.3 项目关联功能

#### 3.3.1 项目选择关联
**功能描述**: 选择和关联相关的项目

**关联功能**:
- **项目搜索**: 搜索可关联的项目
- **项目选择**: 从项目列表中选择关联项目
- **项目信息**: 显示选中项目的基本信息
- **关联确认**: 确认项目关联关系
- **多项目关联**: 支持关联多个相关项目

#### 3.3.2 客户信息关联
**功能描述**: 关联相关的客户信息

**客户关联**:
- **客户搜索**: 搜索相关的客户
- **客户选择**: 选择关联的客户
- **客户信息**: 显示客户的基本信息
- **联系方式**: 客户的联系方式
- **历史记录**: 客户的历史计算记录

#### 3.3.3 方案信息关联
**功能描述**: 关联相关的智能家居方案

**方案关联**:
- **方案搜索**: 搜索相关的方案
- **方案选择**: 选择关联的方案
- **方案信息**: 显示方案的基本信息
- **方案状态**: 方案的当前状态
- **方案版本**: 方案的版本信息

### 3.4 计算执行功能

#### 3.4.1 参数验证功能
**功能描述**: 验证计算参数的完整性和正确性

**验证内容**:
- **必填字段**: 检查必填字段是否完整
- **数据格式**: 验证数据格式的正确性
- **数值范围**: 验证数值是否在合理范围内
- **逻辑关系**: 验证参数间的逻辑关系
- **兼容性**: 验证设备和环境的兼容性

#### 3.4.2 计算引擎调用
**功能描述**: 调用计算引擎执行距离计算

**计算过程**:
- **参数传递**: 将输入参数传递给计算引擎
- **计算执行**: 执行投影距离计算算法
- **结果生成**: 生成详细的计算结果
- **结果验证**: 验证计算结果的合理性
- **异常处理**: 处理计算过程中的异常

#### 3.4.3 结果预览功能
**功能描述**: 预览计算结果和建议

**预览内容**:
- **计算结果**: 详细的计算结果数据
- **安装建议**: 设备安装的建议
- **效果预估**: 投影效果的预估
- **注意事项**: 重要的注意事项
- **优化建议**: 方案优化的建议

### 3.5 备注说明功能

#### 3.5.1 计算说明录入
**功能描述**: 录入计算的详细说明

**说明内容**:
- **计算背景**: 计算的背景和原因
- **特殊要求**: 客户的特殊要求
- **限制条件**: 现场的限制条件
- **技术难点**: 技术实现的难点
- **解决方案**: 问题的解决方案

#### 3.5.2 注意事项记录
**功能描述**: 记录重要的注意事项

**注意事项**:
- **安装注意**: 安装过程中的注意事项
- **使用注意**: 使用过程中的注意事项
- **维护注意**: 维护保养的注意事项
- **安全注意**: 安全相关的注意事项
- **其他注意**: 其他重要的注意事项

#### 3.5.3 后续跟进计划
**功能描述**: 制定后续的跟进计划

**跟进计划**:
- **跟进时间**: 后续跟进的时间安排
- **跟进内容**: 跟进的具体内容
- **责任人**: 跟进的责任人
- **跟进方式**: 跟进的方式和方法
- **预期结果**: 跟进的预期结果

### 3.6 数据保存功能

#### 3.6.1 记录保存处理
**功能描述**: 保存完整的计算记录

**保存内容**:
- **基本信息**: 记录的基本信息
- **计算参数**: 完整的计算参数
- **计算结果**: 详细的计算结果
- **关联信息**: 项目和客户关联信息
- **备注说明**: 完整的备注和说明

#### 3.6.2 编号生成功能
**功能描述**: 生成唯一的计算记录编号

**编号规则**:
- **前缀标识**: 计算记录的前缀标识
- **日期编码**: 基于日期的编码
- **序号编码**: 当日的序号编码
- **类型编码**: 记录类型的编码
- **校验码**: 编号的校验码

#### 3.6.3 数据同步功能
**功能描述**: 同步数据到相关系统

**同步内容**:
- **项目系统**: 同步到项目管理系统
- **客户系统**: 同步到客户管理系统
- **方案系统**: 同步到方案管理系统
- **知识库**: 同步到计算知识库
- **统计系统**: 同步到统计分析系统

## 4. 用户界面设计

### 4.1 页面布局设计
- **顶部导航区**: 页面标题、步骤指示和返回按钮
- **表单录入区**: 分区域的表单录入界面
- **关联选择区**: 项目和客户关联选择区域
- **预览确认区**: 计算结果预览和确认区域
- **底部操作区**: 保存、取消、计算等操作按钮

### 4.2 表单设计规范
- **分步骤录入**: 按照信息类型分步骤录入
- **字段分组**: 相关字段进行分组显示
- **必填标识**: 必填字段使用红色星号标识
- **智能提示**: 提供输入建议和自动完成

### 4.3 交互设计规范
- **向导式录入**: 分步骤引导用户完成录入
- **实时验证**: 输入过程中进行实时验证
- **智能关联**: 自动关联相关的项目和客户信息
- **保存提醒**: 数据变更时的保存提醒

## 5. 数据流向

### 5.1 数据输入
- **来源**: 用户手动录入 + 关联系统数据
- **格式**: 结构化的计算记录数据

### 5.2 数据输出
- **流向**: 投影距离工具页面（计算记录库）
- **存储**: 计算记录数据库

### 5.3 业务关联
- **前置页面**: 三方工具-投影距离页面（入口）
- **关联数据**: 项目信息、客户信息、方案信息
- **后续应用**: 计算记录的查询和复用

## 6. 权限控制

### 6.1 访问权限
- **设计师**: 可以创建设计相关的计算记录
- **技术人员**: 可以创建技术验证的计算记录
- **销售人员**: 可以创建客户咨询的计算记录

### 6.2 操作权限
- **创建权限**: 计算记录创建权限控制
- **关联权限**: 项目和客户关联权限控制
- **保存权限**: 计算记录保存权限控制
- **查看权限**: 计算记录查看权限控制

## 7. 异常处理

### 7.1 数据异常
- **保存失败**: 数据保存失败的重试机制
- **关联失败**: 项目关联失败的处理
- **计算异常**: 计算执行异常的处理

### 7.2 操作异常
- **权限不足**: 权限不足的友好提示
- **网络异常**: 网络连接异常的处理
- **数据冲突**: 数据冲突的检测和处理

---

**文档版本**: v1.0
**编写日期**: 2025-07-14
**编写人员**: AI系统架构师
**审核状态**: 待审核
**对应图片**: 三方工具-投影距离-点击新增页面.png
