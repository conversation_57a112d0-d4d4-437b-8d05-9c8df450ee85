[{"contentPath": "java/InfraStudentPageReqVO", "filePath": "yichao-module-infra/yichao-module-infra-biz/src/main/java/com.muzi.yichao/module/infra/controller/admin/demo/vo/InfraStudentPageReqVO.java"}, {"contentPath": "java/InfraStudentRespVO", "filePath": "yichao-module-infra/yichao-module-infra-biz/src/main/java/com.muzi.yichao/module/infra/controller/admin/demo/vo/InfraStudentRespVO.java"}, {"contentPath": "java/InfraStudentSaveReqVO", "filePath": "yichao-module-infra/yichao-module-infra-biz/src/main/java/com.muzi.yichao/module/infra/controller/admin/demo/vo/InfraStudentSaveReqVO.java"}, {"contentPath": "java/InfraStudentController", "filePath": "yichao-module-infra/yichao-module-infra-biz/src/main/java/com.muzi.yichao/module/infra/controller/admin/demo/InfraStudentController.java"}, {"contentPath": "java/InfraStudentDO", "filePath": "yichao-module-infra/yichao-module-infra-biz/src/main/java/com.muzi.yichao/module/infra/dal/dataobject/demo/InfraStudentDO.java"}, {"contentPath": "java/InfraStudentContactDO", "filePath": "yichao-module-infra/yichao-module-infra-biz/src/main/java/com.muzi.yichao/module/infra/dal/dataobject/demo/InfraStudentContactDO.java"}, {"contentPath": "java/InfraStudentTeacherDO", "filePath": "yichao-module-infra/yichao-module-infra-biz/src/main/java/com.muzi.yichao/module/infra/dal/dataobject/demo/InfraStudentTeacherDO.java"}, {"contentPath": "java/InfraStudentMapper", "filePath": "yichao-module-infra/yichao-module-infra-biz/src/main/java/com.muzi.yichao/module/infra/dal/mysql/demo/InfraStudentMapper.java"}, {"contentPath": "java/InfraStudentContactMapper", "filePath": "yichao-module-infra/yichao-module-infra-biz/src/main/java/com.muzi.yichao/module/infra/dal/mysql/demo/InfraStudentContactMapper.java"}, {"contentPath": "java/InfraStudentTeacherMapper", "filePath": "yichao-module-infra/yichao-module-infra-biz/src/main/java/com.muzi.yichao/module/infra/dal/mysql/demo/InfraStudentTeacherMapper.java"}, {"contentPath": "xml/InfraStudentMapper", "filePath": "yichao-module-infra/yichao-module-infra-biz/src/main/resources/mapper/demo/InfraStudentMapper.xml"}, {"contentPath": "java/InfraStudentServiceImpl", "filePath": "yichao-module-infra/yichao-module-infra-biz/src/main/java/com.muzi.yichao/module/infra/service/demo/InfraStudentServiceImpl.java"}, {"contentPath": "java/InfraStudentService", "filePath": "yichao-module-infra/yichao-module-infra-biz/src/main/java/com.muzi.yichao/module/infra/service/demo/InfraStudentService.java"}, {"contentPath": "java/InfraStudentServiceImplTest", "filePath": "yichao-module-infra/yichao-module-infra-biz/src/test/java/com.muzi.yichao/module/infra/service/demo/InfraStudentServiceImplTest.java"}, {"contentPath": "java/ErrorCodeConstants_手动操作", "filePath": "yichao-module-infra/yichao-module-infra-api/src/main/java/com.muzi.yichao/module/infra/enums/ErrorCodeConstants_手动操作.java"}, {"contentPath": "sql/sql", "filePath": "sql/sql.sql"}, {"contentPath": "sql/h2", "filePath": "sql/h2.sql"}, {"contentPath": "vue/index", "filePath": "yichao-ui-admin-vue2/src/views/infra/demo/index.vue"}, {"contentPath": "js/index", "filePath": "yichao-ui-admin-vue2/src/api/infra/demo/index.js"}, {"contentPath": "vue/StudentForm", "filePath": "yich<PERSON>-ui-admin-vue2/src/views/infra/demo/StudentForm.vue"}, {"contentPath": "vue/StudentContactForm", "filePath": "yichao-ui-admin-vue2/src/views/infra/demo/components/StudentContactForm.vue"}, {"contentPath": "vue/StudentTeacherForm", "filePath": "yichao-ui-admin-vue2/src/views/infra/demo/components/StudentTeacherForm.vue"}, {"contentPath": "vue/StudentContactList", "filePath": "yichao-ui-admin-vue2/src/views/infra/demo/components/StudentContactList.vue"}, {"contentPath": "vue/StudentTeacherList", "filePath": "yichao-ui-admin-vue2/src/views/infra/demo/components/StudentTeacherList.vue"}]