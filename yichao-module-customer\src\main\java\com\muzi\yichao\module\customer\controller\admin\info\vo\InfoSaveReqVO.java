package com.muzi.yichao.module.customer.controller.admin.info.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import com.muzi.yichao.module.customer.dal.dataobject.infoaccountlog.InfoAccountLogDO;
import com.muzi.yichao.module.customer.dal.dataobject.infoaddress.InfoAddressDO;
import com.muzi.yichao.module.customer.dal.dataobject.infoinvoiceinfo.InfoInvoiceInfoDO;
import com.muzi.yichao.module.customer.dal.dataobject.infopointsrecord.InfoPointsRecordDO;
import com.muzi.yichao.module.customer.dal.dataobject.inforeferral.InfoReferralDO;
import com.muzi.yichao.module.customer.dal.dataobject.infostatuslog.InfoStatusLogDO;

@Schema(description = "管理后台 - 客户信息新增/修改 Request VO")
@Data
public class InfoSaveReqVO {

    @Schema(description = "客户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15662")
    private Long id;

    @Schema(description = "客户编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "客户编号不能为空")
    private String customerNo;

    @Schema(description = "门店ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "12401")
    @NotNull(message = "门店ID不能为空")
    private Long storeId;

    @Schema(description = "客户类别：1-服务商客户，2-品牌家装客户，3-个人客户，4-企业客户", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "客户类别：1-服务商客户，2-品牌家装客户，3-个人客户，4-企业客户不能为空")
    private Integer category;

    @Schema(description = "来源渠道：1-设计师推荐，2-大众点评，3-网络推广，4-朋友介绍，5-门店到访，6-其他", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "来源渠道：1-设计师推荐，2-大众点评，3-网络推广，4-朋友介绍，5-门店到访，6-其他不能为空")
    private Integer source;

    @Schema(description = "客户姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "客户姓名不能为空")
    private String name;

    @Schema(description = "手机号码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "手机号码不能为空")
    private String mobile;

    @Schema(description = "微信号")
    private String wechat;

    @Schema(description = "性别：0-未知，1-男，2-女")
    private Integer sex;

    @Schema(description = "生日")
    private LocalDate birthday;

    @Schema(description = "证件类型：1-身份证，2-护照，3-其他", example = "2")
    private Integer idType;

    @Schema(description = "证件号码")
    private String idNumber;

    @Schema(description = "公司名称", example = "芋艿")
    private String companyName;

    @Schema(description = "跟进人ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "31548")
    @NotNull(message = "跟进人ID不能为空")
    private Long followerUserId;

    @Schema(description = "设计师ID", example = "28932")
    private Long designerUserId;

    @Schema(description = "登录密码（加密）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "登录密码（加密）不能为空")
    private String password;

    @Schema(description = "发卡日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "发卡日期不能为空")
    private LocalDate issueDate;

    @Schema(description = "到期日期")
    private LocalDate expireDate;

    @Schema(description = "收款账户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "30294")
    @NotNull(message = "收款账户ID不能为空")
    private Long accountId;

    @Schema(description = "初始存款", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "初始存款不能为空")
    private BigDecimal initialDeposit;

    @Schema(description = "红包金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "红包金额不能为空")
    private BigDecimal bonusAmount;

    @Schema(description = "初始积分", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "初始积分不能为空")
    private Integer initialPoints;

    @Schema(description = "是否安装师傅", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否安装师傅不能为空")
    private Boolean isInstaller;

    @Schema(description = "客户类型：1-前装客户，2-后装客户，3-改造客户", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "客户类型：1-前装客户，2-后装客户，3-改造客户不能为空")
    private Integer customerType;

    @Schema(description = "APP账号", example = "23810")
    private String appAccount;

    @Schema(description = "是否需要发票", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否需要发票不能为空")
    private Boolean needInvoice;

    @Schema(description = "推荐人ID", example = "27409")
    private Long referrerId;

    @Schema(description = "备注信息", example = "你猜")
    private String remark;

    @Schema(description = "邮箱地址")
    private String email;

    @Schema(description = "职位")
    private String position;

    @Schema(description = "客户经理ID", example = "20600")
    private Long customerManagerId;

    @Schema(description = "VIP等级：0-普通，1-VIP1，2-VIP2，3-VIP3", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "VIP等级：0-普通，1-VIP1，2-VIP2，3-VIP3不能为空")
    private Integer vipLevel;

    @Schema(description = "客户标签（JSON格式）")
    private String customerTags;

    @Schema(description = "状态：0-禁用，1-正常，2-冻结，3-黑名单，4-VIP", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "状态：0-禁用，1-正常，2-冻结，3-黑名单，4-VIP不能为空")
    private Integer status;

}