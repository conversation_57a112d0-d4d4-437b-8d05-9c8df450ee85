package com.muzi.yichao.framework.websocket.core.sender.local;

import com.muzi.yichao.framework.websocket.core.sender.AbstractWebSocketMessageSender;
import com.muzi.yichao.framework.websocket.core.sender.WebSocketMessageSender;
import com.muzi.yichao.framework.websocket.core.session.WebSocketSessionManager;

/**
 * 本地的 {@link WebSocketMessageSender} 实现类
 *
 * 注意：仅仅适合单机场景！！！
 *
 * <AUTHOR>
 */
public class LocalWebSocketMessageSender extends AbstractWebSocketMessageSender {

    public LocalWebSocketMessageSender(WebSocketSessionManager sessionManager) {
        super(sessionManager);
    }

}
