package com.muzi.yichao.module.customer.dal.dataobject.info;

import lombok.*;

import java.time.LocalDate;
import java.util.*;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.muzi.yichao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 客户信息 DO
 *
 * <AUTHOR>
 */
@TableName("customer_info")
@KeySequence("customer_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InfoDO extends BaseDO {

    /**
     * 客户ID
     */
    @TableId
    private Long id;
    /**
     * 客户编号
     */
    private String customerNo;
    /**
     * 门店ID
     */
    private Long storeId;
    /**
     * 客户类别：1-服务商客户，2-品牌家装客户，3-个人客户，4-企业客户
     *
     * 枚举 {@link TODO customer_type 对应的类}
     */
    private Integer category;
    /**
     * 来源渠道：1-设计师推荐，2-大众点评，3-网络推广，4-朋友介绍，5-门店到访，6-其他
     *
     * 枚举 {@link TODO customer_source 对应的类}
     */
    private Integer source;
    /**
     * 客户姓名
     */
    private String name;
    /**
     * 手机号码
     */
    private String mobile;
    /**
     * 微信号
     */
    private String wechat;
    /**
     * 性别：0-未知，1-男，2-女
     *
     * 枚举 {@link TODO system_user_sex 对应的类}
     */
    private Integer sex;
    /**
     * 生日
     */
    private LocalDate birthday;
    /**
     * 证件类型：1-身份证，2-护照，3-其他
     *
     * 枚举 {@link TODO id_type 对应的类}
     */
    private Integer idType;
    /**
     * 证件号码
     */
    private String idNumber;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 跟进人ID
     */
    private Long followerUserId;
    /**
     * 设计师ID
     */
    private Long designerUserId;
    /**
     * 登录密码（加密）
     */
    private String password;
    /**
     * 发卡日期
     */
    private LocalDate issueDate;
    /**
     * 到期日期
     */
    private LocalDate expireDate;
    /**
     * 收款账户ID
     */
    private Long accountId;
    /**
     * 初始存款
     */
    private BigDecimal initialDeposit;
    /**
     * 红包金额
     */
    private BigDecimal bonusAmount;
    /**
     * 初始积分
     */
    private Integer initialPoints;
    /**
     * 是否安装师傅
     */
    private Boolean isInstaller;
    /**
     * 客户类型：1-前装客户，2-后装客户，3-改造客户
     *
     * 枚举 {@link TODO customer_type 对应的类}
     */
    private Integer customerType;
    /**
     * APP账号
     */
    private String appAccount;
    /**
     * 是否需要发票
     */
    private Boolean needInvoice;
    /**
     * 推荐人ID
     */
    private Long referrerId;
    /**
     * 备注信息
     */
    private String remark;
    /**
     * 邮箱地址
     */
    private String email;
    /**
     * 职位
     */
    private String position;
    /**
     * 客户经理ID
     */
    private Long customerManagerId;
    /**
     * VIP等级：0-普通，1-VIP1，2-VIP2，3-VIP3
     *
     * 枚举 {@link TODO vip_level 对应的类}
     */
    private Integer vipLevel;
    /**
     * 客户标签（JSON格式）
     */
    private String customerTags;
    /**
     * 状态：0-禁用，1-正常，2-冻结，3-黑名单，4-VIP
     *
     * 枚举 {@link TODO customer_status 对应的类}
     */
    private Integer status;


}