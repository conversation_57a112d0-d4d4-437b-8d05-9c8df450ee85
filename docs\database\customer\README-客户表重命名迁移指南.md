# 客户管理模块表重命名迁移指南

## 📋 概述

本指南用于将客户管理模块的表结构从旧的命名方式迁移到新的主子表命名规范。

### 表名映射关系

| 旧表名 | 新表名 | 说明 |
|--------|--------|------|
| `customer_customer` | `customer_info` | 客户主表 |
| `customer_address` | `customer_info_address` | 客户收货地址表 |
| `customer_referral` | `customer_info_referral` | 客户推荐关系表 |
| `customer_status_log` | `customer_info_status_log` | 客户状态变更记录表 |
| `customer_points_record` | `customer_info_points_record` | 客户积分记录表 |
| `customer_account_log` | `customer_info_account_log` | 客户账户操作日志表 |
| `customer_invoice_info` | `customer_info_invoice_info` | 客户发票信息表 |

## 🗂️ 文件说明

### 1. `customer-module.sql`
- **用途**: 新的表结构定义
- **内容**: 包含所有新表的创建语句和字典数据
- **执行时机**: 迁移开始前

### 2. `migrate-customer-data.sql`
- **用途**: 数据迁移脚本
- **内容**: 将旧表数据迁移到新表中
- **执行时机**: 新表创建后

### 3. `drop-old-customer-tables.sql`
- **用途**: 删除旧表结构
- **内容**: 删除所有旧表的语句
- **执行时机**: 数据迁移完成并验证后

## 🚀 迁移步骤

### 步骤1: 备份数据库
```bash
# 备份整个数据库
mysqldump -u username -p database_name > backup_$(date +%Y%m%d_%H%M%S).sql

# 或者只备份客户相关表
mysqldump -u username -p database_name \
  customer_customer customer_address customer_referral \
  customer_status_log customer_points_record \
  customer_account_log customer_invoice_info \
  > customer_tables_backup_$(date +%Y%m%d_%H%M%S).sql
```

### 步骤2: 创建新表结构
```sql
-- 执行新表结构创建
source docs/database/customer-module.sql;
```

### 步骤3: 迁移数据
```sql
-- 执行数据迁移
source docs/database/migrate-customer-data.sql;
```

### 步骤4: 验证数据迁移
```sql
-- 执行验证脚本（在 migrate-customer-data.sql 文件末尾）
-- 检查各表数据量是否一致
```

### 步骤5: 更新应用程序
- 更新所有相关的实体类、Mapper、Service等
- 修改表名引用
- 更新SQL语句中的表名

### 步骤6: 测试验证
- 在测试环境验证所有功能正常
- 确认数据完整性
- 验证业务逻辑正确性

### 步骤7: 删除旧表（可选）
```sql
-- 确认一切正常后，删除旧表
source docs/database/drop-old-customer-tables.sql;
```

## ⚠️ 注意事项

### 执行前检查
1. **数据库备份**: 务必完整备份数据库
2. **环境确认**: 先在测试环境执行
3. **权限检查**: 确保有足够的数据库操作权限
4. **空间检查**: 确保有足够的磁盘空间

### 执行中监控
1. **执行时间**: 大表迁移可能需要较长时间
2. **锁表影响**: 迁移过程中可能影响业务
3. **错误处理**: 遇到错误及时停止并分析

### 执行后验证
1. **数据完整性**: 验证所有数据都已正确迁移
2. **关联关系**: 检查外键关系是否正确
3. **业务功能**: 测试所有相关业务功能
4. **性能影响**: 检查查询性能是否正常

## 🔧 故障排除

### 常见问题

#### 1. 外键约束错误
```sql
-- 临时禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;
-- 执行迁移操作
-- 重新启用外键检查
SET FOREIGN_KEY_CHECKS = 1;
```

#### 2. 数据重复错误
- 检查是否已存在新表数据
- 使用 `NOT EXISTS` 条件避免重复插入

#### 3. 权限不足
```sql
-- 检查当前用户权限
SHOW GRANTS FOR CURRENT_USER();
```

#### 4. 磁盘空间不足
```bash
# 检查磁盘空间
df -h
# 清理临时文件或扩展磁盘空间
```

## 📊 验证脚本

### 快速验证
```sql
-- 检查新表是否创建成功
SHOW TABLES LIKE 'customer_info%';

-- 检查数据量对比
SELECT 
    'customer_info' as table_name,
    (SELECT COUNT(*) FROM customer_customer) as old_count,
    (SELECT COUNT(*) FROM customer_info) as new_count;
```

### 详细验证
执行 `migrate-customer-data.sql` 文件末尾的完整验证脚本。

## 📞 支持

如果在迁移过程中遇到问题，请：

1. 检查错误日志
2. 参考本文档的故障排除部分
3. 在测试环境重现问题
4. 联系技术支持团队

---

**重要提醒**: 生产环境执行前，请务必在测试环境完整验证整个迁移流程！
