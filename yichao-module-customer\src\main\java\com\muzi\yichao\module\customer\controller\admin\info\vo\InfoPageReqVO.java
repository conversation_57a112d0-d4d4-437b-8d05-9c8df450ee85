package com.muzi.yichao.module.customer.controller.admin.info.vo;

import lombok.*;

import java.time.LocalDate;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.muzi.yichao.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.muzi.yichao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 客户信息分页 Request VO")
@Data
public class InfoPageReqVO extends PageParam {

    @Schema(description = "客户编号")
    private String customerNo;

    @Schema(description = "门店ID", example = "12401")
    private Long storeId;

    @Schema(description = "客户类别：1-服务商客户，2-品牌家装客户，3-个人客户，4-企业客户")
    private Integer category;

    @Schema(description = "来源渠道：1-设计师推荐，2-大众点评，3-网络推广，4-朋友介绍，5-门店到访，6-其他")
    private Integer source;

    @Schema(description = "客户姓名", example = "芋艿")
    private String name;

    @Schema(description = "手机号码")
    private String mobile;

    @Schema(description = "性别：0-未知，1-男，2-女")
    private Integer sex;

    @Schema(description = "证件类型：1-身份证，2-护照，3-其他", example = "2")
    private Integer idType;

    @Schema(description = "证件号码")
    private String idNumber;

    @Schema(description = "跟进人ID", example = "31548")
    private Long followerUserId;

    @Schema(description = "设计师ID", example = "28932")
    private Long designerUserId;

    @Schema(description = "发卡日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate[] issueDate;

    @Schema(description = "到期日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate[] expireDate;

    @Schema(description = "客户类型：1-前装客户，2-后装客户，3-改造客户", example = "2")
    private Integer customerType;

    @Schema(description = "VIP等级：0-普通，1-VIP1，2-VIP2，3-VIP3")
    private Integer vipLevel;

    @Schema(description = "状态：0-禁用，1-正常，2-冻结，3-黑名单，4-VIP", example = "2")
    private Integer status;

}