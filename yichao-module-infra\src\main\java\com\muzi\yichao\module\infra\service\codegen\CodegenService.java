package com.muzi.yichao.module.infra.service.codegen;

import com.muzi.yichao.framework.common.pojo.PageResult;
import com.muzi.yichao.module.infra.controller.admin.codegen.vo.CodegenCreateListReqVO;
import com.muzi.yichao.module.infra.controller.admin.codegen.vo.CodegenUpdateReqVO;
import com.muzi.yichao.module.infra.controller.admin.codegen.vo.table.CodegenTablePageReqVO;
import com.muzi.yichao.module.infra.controller.admin.codegen.vo.table.DatabaseTableRespVO;
import com.muzi.yichao.module.infra.dal.dataobject.codegen.CodegenColumnDO;
import com.muzi.yichao.module.infra.dal.dataobject.codegen.CodegenTableDO;

import java.util.List;
import java.util.Map;

/**
 * 代码生成 Service 接口
 *
 * <AUTHOR>
 */
public interface CodegenService {

    /**
     * 基于数据库的表结构，创建代码生成器的表定义
     *
     * @param author 作者
     * @param reqVO  表信息
     * @return 创建的表定义的编号数组
     */
    List<Long> createCodegenList(String author, CodegenCreateListReqVO reqVO);

    /**
     * 更新数据库的表和字段定义
     *
     * @param updateReqVO 更新信息
     */
    void updateCodegen(CodegenUpdateReqVO updateReqVO);

    /**
     * 基于数据库的表结构，同步数据库的表和字段定义
     *
     * @param tableId 表编号
     */
    void syncCodegenFromDB(Long tableId);

    /**
     * 删除数据库的表和字段定义
     *
     * @param tableId 数据编号
     */
    void deleteCodegen(Long tableId);

    /**
     * 批量删除数据库的表和字段定义
     *
     * @param tableIds 数据编号列表
     */
    void deleteCodegenList(List<Long> tableIds);

    /**
     * 获得表定义列表
     *
     * @param dataSourceConfigId 数据源配置的编号
     * @return 表定义列表
     */
    List<CodegenTableDO> getCodegenTableList(Long dataSourceConfigId);

    /**
     * 获得表定义分页
     *
     * @param pageReqVO 分页条件
     * @return 表定义分页
     */
    PageResult<CodegenTableDO> getCodegenTablePage(CodegenTablePageReqVO pageReqVO);

    /**
     * 获得表定义
     *
     * @param id 表编号
     * @return 表定义
     */
    CodegenTableDO getCodegenTable(Long id);

    /**
     * 获得指定表的字段定义数组
     *
     * @param tableId 表编号
     * @return 字段定义数组
     */
    List<CodegenColumnDO> getCodegenColumnListByTableId(Long tableId);

    /**
     * 执行指定表的代码生成
     *
     * @param tableId 表编号
     * @return 生成结果。key 为文件路径，value 为对应的代码内容
     */
    Map<String, String> generationCodes(Long tableId);

    /**
     * 获得数据库自带的表定义列表
     *
     * @param dataSourceConfigId 数据源的配置编号
     * @param name               表名称
     * @param comment            表描述
     * @return 表定义列表
     */
    List<DatabaseTableRespVO> getDatabaseTableList(Long dataSourceConfigId, String name, String comment);

}
