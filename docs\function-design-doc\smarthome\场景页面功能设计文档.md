# SaaS智能家装CRM系统 - 场景页面功能设计文档

## 1. 页面概述

### 1.1 页面目的
场景页面用于管理智能家居方案中的所有智能场景配置，提供场景的查看、编辑、测试和部署功能。该页面为设计师和技术人员提供场景管理的集中平台，确保智能场景的正确配置和有效运行。

### 1.2 业务价值
- 建立方案场景的统一管理平台，提升场景配置效率
- 提供场景的可视化配置界面，降低配置复杂度
- 支持场景的测试和调试功能，确保场景运行效果
- 建立场景模板库，支持场景的快速复制和应用

### 1.3 页面位置
- **所属模块**: 智能家居模块
- **业务流程位置**: 方案设计 → **场景配置** → 方案实施
- **关联页面**: 
  - 方案列表页面（入口页面）
  - 新增场景页面（场景创建）
  - 图纸页面（场景与图纸关联）

## 2. 场景页面操作流程图

```mermaid
flowchart TD
    A[用户点击场景管理] --> B[跳转场景页面]
    B --> C[权限验证]
    C --> D{权限验证通过?}
    D -->|否| E[显示权限不足提示]
    D -->|是| F[加载场景列表]

    F --> G[显示场景列表区]
    F --> H[显示筛选条件区]
    F --> I[显示操作工具栏]

    G --> J[场景卡片展示]
    G --> K[场景基本信息]
    G --> L[场景状态标识]
    G --> M[操作按钮组]

    H --> N[场景类型筛选]
    H --> O[状态筛选]
    H --> P[房间筛选]
    H --> Q[设备筛选]

    I --> R[新增场景]
    I --> S[批量操作]
    I --> T[场景模板]

    M --> U[编辑场景]
    M --> V[测试场景]
    M --> W[复制场景]
    M --> X[删除场景]
    M --> Y[启用/停用]

    R --> Z[跳转新增场景页面]
    U --> AA[进入场景编辑模式]
    V --> BB[执行场景测试]
    W --> CC[复制场景配置]

    AA --> DD[编辑触发条件]
    AA --> EE[编辑执行动作]
    AA --> FF[设置场景参数]

    BB --> GG[模拟场景执行]
    GG --> HH[显示测试结果]
    HH --> II[记录测试日志]

    CC --> JJ[生成场景副本]
    JJ --> KK[修改场景信息]
    KK --> LL[保存新场景]

    N --> MM[实时筛选更新]
    O --> MM
    P --> MM
    Q --> MM

    MM --> NN[更新场景列表]
    NN --> G

    style A fill:#e1f5fe
    style D fill:#fff3e0
    style E fill:#ffebee
    style MM fill:#f3e5f5
    style NN fill:#e8f5e8
```

### 流程说明
场景页面的操作流程包含以下核心环节：

1. **页面访问与权限验证**：从方案列表页面跳转，进行权限验证
2. **场景列表展示**：展示方案相关的所有智能场景，支持筛选和搜索
3. **场景操作管理**：提供场景的编辑、测试、复制、删除等操作
4. **场景配置编辑**：编辑场景的触发条件和执行动作
5. **场景测试验证**：测试场景的执行效果和运行状态

## 3. 详细功能设计

### 3.1 场景列表展示功能

#### 3.1.1 场景卡片展示
**功能描述**: 以卡片形式展示场景的核心信息

**卡片信息**:
- **场景名称**: 场景的名称和简要描述
- **场景类型**: 生活场景/安全场景/娱乐场景/节能场景
- **触发方式**: 时间触发/设备触发/手动触发/组合触发
- **包含设备**: 场景涉及的设备数量
- **场景状态**: 启用/停用/测试/故障
- **创建时间**: 场景创建的时间
- **最后执行**: 最后一次执行的时间
- **执行次数**: 场景的执行统计

#### 3.1.2 场景分类管理
**功能描述**: 按照场景类型进行分类管理

**场景分类**:
- **生活场景**: 起床、离家、回家、睡眠等日常生活场景
- **安全场景**: 布防、撤防、紧急、外出等安全相关场景
- **娱乐场景**: 观影、音乐、游戏、聚会等娱乐场景
- **节能场景**: 节能模式、度假模式、深夜模式等节能场景
- **自定义场景**: 用户个性化定制的场景

#### 3.1.3 状态标识系统
**功能描述**: 清晰的场景状态标识

**状态类型**:
- **启用**: 绿色标签，场景正常运行
- **停用**: 灰色标签，场景暂停运行
- **测试**: 蓝色标签，场景测试状态
- **故障**: 红色标签，场景运行异常
- **编辑**: 橙色标签，场景编辑状态

### 3.2 场景操作功能

#### 3.2.1 场景编辑功能
**功能描述**: 编辑场景的配置和参数

**编辑内容**:
- **基本信息**: 场景名称、描述、类型等基本信息
- **触发条件**: 场景触发的条件和规则
- **执行动作**: 场景执行时的具体动作
- **设备配置**: 参与场景的设备和参数设置
- **时间设置**: 场景的时间计划和延迟设置

#### 3.2.2 场景测试功能
**功能描述**: 测试场景的执行效果

**测试功能**:
- **模拟执行**: 模拟场景的完整执行过程
- **分步测试**: 分步骤测试场景的各个动作
- **设备检查**: 检查参与设备的状态和连接
- **结果验证**: 验证场景执行的结果和效果
- **日志记录**: 记录测试过程和结果

#### 3.2.3 场景复制功能
**功能描述**: 复制现有场景创建新场景

**复制操作**:
- **完整复制**: 复制场景的所有配置信息
- **选择复制**: 选择性复制部分配置
- **修改调整**: 复制后修改场景信息
- **保存新场景**: 保存为新的场景配置

### 3.3 场景配置功能

#### 3.3.1 触发条件配置
**功能描述**: 配置场景的触发条件

**触发类型**:
- **时间触发**: 定时触发、周期触发、日出日落触发
- **设备触发**: 开关状态、传感器数据、设备故障触发
- **环境触发**: 温度、湿度、光照、空气质量触发
- **人员触发**: 人体感应、门窗状态、位置信息触发
- **组合触发**: 多种条件的逻辑组合触发

#### 3.3.2 执行动作配置
**功能描述**: 配置场景执行的具体动作

**动作类型**:
- **设备控制**: 开关控制、调节控制、模式切换
- **场景联动**: 启动其他场景、停止场景、延迟执行
- **通知提醒**: 手机推送、语音播报、屏幕显示
- **数据记录**: 记录执行日志、统计数据、状态变化

#### 3.3.3 逻辑关系配置
**功能描述**: 配置复杂的逻辑关系

**逻辑类型**:
- **AND逻辑**: 所有条件都满足时触发
- **OR逻辑**: 任一条件满足时触发
- **NOT逻辑**: 条件不满足时触发
- **时间窗口**: 在特定时间窗口内的条件判断
- **优先级**: 多场景冲突时的优先级处理

### 3.4 筛选搜索功能

#### 3.4.1 基础筛选条件
**功能描述**: 提供常用的筛选条件

**筛选字段**:
- **场景类型**: 按场景类型筛选
- **场景状态**: 按场景状态筛选
- **房间位置**: 按房间位置筛选
- **设备类型**: 按设备类型筛选
- **创建时间**: 按创建时间范围筛选

#### 3.4.2 搜索功能
**功能描述**: 支持关键词搜索

**搜索范围**:
- **场景名称**: 场景名称的模糊搜索
- **场景描述**: 场景描述的关键词搜索
- **设备名称**: 包含设备的名称搜索
- **标签搜索**: 场景标签的搜索

### 3.5 场景模板功能

#### 3.5.1 模板管理功能
**功能描述**: 管理场景模板库

**模板功能**:
- **模板分类**: 按场景类型分类管理模板
- **模板预览**: 预览模板的配置和效果
- **模板应用**: 应用模板创建新场景
- **模板编辑**: 编辑和更新模板内容

#### 3.5.2 模板创建功能
**功能描述**: 将现有场景保存为模板

**创建流程**:
- **场景选择**: 选择要保存为模板的场景
- **模板信息**: 设置模板的名称和描述
- **参数抽象**: 将具体参数抽象为可配置参数
- **保存模板**: 保存到模板库供后续使用

## 4. 用户界面设计

### 4.1 页面布局设计
- **顶部工具栏**: 新增场景、批量操作、模板管理等功能按钮
- **筛选条件区**: 场景类型、状态、房间等筛选条件
- **场景列表区**: 主要的场景卡片展示区域
- **详情面板区**: 右侧场景详情和配置面板

### 4.2 卡片设计规范
- **场景图标**: 清晰的场景类型图标
- **信息层次**: 合理的信息层次和布局
- **状态标识**: 清晰的状态标签和指示器
- **操作按钮**: 统一的操作按钮样式

### 4.3 配置界面设计
- **可视化配置**: 拖拽式的场景配置界面
- **逻辑流程图**: 场景逻辑的流程图展示
- **参数面板**: 设备参数的配置面板
- **预览功能**: 场景配置的实时预览

## 5. 数据流向

### 5.1 数据输入
- **来源**: 新增场景页面创建的场景配置
- **格式**: 结构化的场景配置数据

### 5.2 数据输出
- **流向**: 智能家居控制系统（场景执行）
- **应用**: 方案实施和客户使用

### 5.3 业务关联
- **前置页面**: 方案列表页面（入口）
- **关联页面**: 新增场景页面（场景创建）
- **应用系统**: 智能家居控制系统（场景运行）

## 6. 权限控制

### 6.1 数据权限
- **设计师权限**: 只能管理自己负责方案的场景
- **项目权限**: 项目成员可以查看项目相关场景
- **客户权限**: 客户可以查看和使用已部署的场景

### 6.2 操作权限
- **查看权限**: 场景信息查看权限
- **编辑权限**: 场景配置编辑权限
- **测试权限**: 场景测试权限
- **部署权限**: 场景部署权限

## 7. 异常处理

### 7.1 配置异常
- **设备离线**: 设备离线时的场景处理
- **配置错误**: 场景配置错误的检测和提示
- **逻辑冲突**: 场景逻辑冲突的检测和处理

### 7.2 执行异常
- **执行失败**: 场景执行失败的重试机制
- **超时处理**: 场景执行超时的处理
- **异常恢复**: 场景异常后的恢复机制

---

**文档版本**: v1.0
**编写日期**: 2025-07-14
**编写人员**: AI系统架构师
**审核状态**: 待审核
**对应图片**: 方案列表-点击场景页面.png
