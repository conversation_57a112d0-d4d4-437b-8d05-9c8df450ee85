# 商品管理模块 - 库存调拨功能设计文档

## 1. 模块概述

### 1.1 模块目的
库存调拨模块是智能家装管理平台商品管理系统的核心业务模块，负责管理商品从一个仓库（出库店）向另一个仓库（入库店）的内部转移流程。该模块通过规范化的调拨流程，确保库存在不同仓库间的合理分配和准确记录。

### 1.2 业务价值
- 建立完整的库存调拨管理体系，规范仓库间商品转移流程
- 支持多种调拨场景，满足仓库调配、项目发货、销售补货等业务需求
- 提供调拨状态实时跟踪，确保调拨商品的安全和及时到达
- 实现调拨数据统计分析，优化库存分布和调拨效率
- 建立调拨流程的完整记录，支持库存审计和问题追溯

### 1.3 功能架构
库存调拨模块包含六个核心功能：
- **调拨单管理**: 调拨单的新增、编辑、审核和状态管理
- **调拨流程控制**: 从出库到入库的完整流程管理和状态跟踪
- **筛选查询功能**: 多维度调拨数据筛选和精确查询
- **入库确认功能**: 目标仓库的商品接收确认和入库处理
- **状态监控管理**: 调拨状态监控和超期预警
- **数据统计分析**: 调拨数据的统计分析和报表导出

## 2. 库存调拨模块操作流程图

```mermaid
flowchart TD
    A[用户访问库存调拨页面] --> B[权限验证]
    B --> C{权限验证通过?}
    C -->|否| D[显示权限不足提示]
    C -->|是| E[加载调拨数据]
    
    E --> F[显示顶部筛选栏]
    E --> G[显示操作按钮区]
    E --> H[加载调拨单表格]
    
    F --> I[筛选条件设置]
    I --> J[经手人筛选]
    I --> K[状态筛选]
    I --> L[调拨日期筛选]
    I --> M[调拨单号查询]
    I --> N[商品信息筛选]
    I --> O[出库店/入库店筛选]
    
    J --> P[组合筛选处理]
    K --> P
    L --> P
    M --> P
    N --> P
    O --> P
    
    P --> Q[点击搜索按钮]
    Q --> R[发送筛选请求]
    R --> S[更新调拨单表格]
    
    G --> T[操作按钮功能]
    T --> U[搜索功能]
    T --> V[导出功能]
    T --> W[新增调拨]
    
    U --> Q
    
    V --> X[选择导出格式]
    X --> Y[生成Excel文件]
    
    W --> Z[跳转新增调拨页面]
    Z --> AA[填写调拨信息]
    AA --> BB[选择出库店]
    AA --> CC[选择入库店]
    AA --> DD[选择调拨商品]
    AA --> EE[设置调拨数量]
    AA --> FF[填写备注信息]
    AA --> GG[选择经手人]
    GG --> HH[提交调拨单]
    HH --> II{调拨信息验证}
    II -->|验证失败| JJ[显示验证错误提示]
    II -->|验证通过| KK[保存调拨单]
    KK --> LL[执行出库操作]
    LL --> MM[更新出库店库存]
    MM --> NN[设置状态为未入库]
    NN --> OO[返回调拨列表]
    
    H --> PP[调拨单数据展示]
    PP --> QQ[调拨基本信息]
    PP --> RR[状态颜色标识]
    PP --> SS[行级操作按钮]
    
    RR --> TT[已完成-绿色]
    RR --> UU[已作废-灰色]
    RR --> VV[未入库-红色]
    
    SS --> WW[打印操作]
    SS --> XX[详情查看]
    SS --> YY[入库操作]
    SS --> ZZ[作废操作]
    
    WW --> AAA[生成打印文件]
    
    XX --> BBB[显示调拨详情]
    BBB --> CCC[调拨单基本信息]
    BBB --> DDD[商品详细信息]
    BBB --> EEE[出入库店信息]
    BBB --> FFF[调拨流程记录]
    
    YY --> GGG{入库权限检查}
    GGG -->|无权限| HHH[显示权限不足]
    GGG -->|状态不允许| III[显示状态限制提示]
    GGG -->|可入库| JJJ[入库确认界面]
    JJJ --> KKK[确认接收商品]
    KKK --> LLL[检查商品状态]
    LLL --> MMM[录入实际入库数量]
    MMM --> NNN{确认入库?}
    NNN -->|否| OOO[取消入库]
    NNN -->|是| PPP[执行入库操作]
    PPP --> QQQ[更新入库店库存]
    QQQ --> RRR[更新调拨状态为已完成]
    RRR --> SSS[记录入库时间]
    SSS --> TTT[生成入库记录]
    TTT --> UUU[发送入库通知]
    
    ZZ --> VVV{作废权限检查}
    VVV -->|无权限| WWW[显示权限不足]
    VVV -->|状态不允许| XXX[显示状态限制提示]
    VVV -->|可作废| YYY[作废确认]
    YYY --> ZZZ{确认作废?}
    ZZZ -->|否| AAAA[取消作废]
    ZZZ -->|是| BBBB[执行作废操作]
    BBBB --> CCCC[回滚出库库存]
    CCCC --> DDDD[更新状态为已作废]
    DDDD --> EEEE[记录作废原因]
    EEEE --> FFFF[发送作废通知]
    
    style A fill:#e1f5fe
    style C fill:#fff3e0
    style D fill:#ffebee
    style II fill:#fff3e0
    style JJ fill:#ffebee
    style GGG fill:#fff3e0
    style HHH fill:#ffebee
    style III fill:#fff8e1
    style NNN fill:#fff3e0
    style VVV fill:#fff3e0
    style WWW fill:#ffebee
    style XXX fill:#fff8e1
    style YYY fill:#fff3e0
    style ZZZ fill:#fff3e0
    style UUU fill:#e8f5e8
    style FFFF fill:#e8f5e8
```

### 流程说明
库存调拨模块的操作流程主要包含以下几个核心环节：

1. **权限验证与数据加载**：验证用户访问权限，加载调拨单数据和筛选条件
2. **多维度筛选查询**：支持经手人、状态、日期、单号、商品、出入库店等多条件筛选
3. **调拨单创建流程**：从新增申请到出库操作的完整调拨单创建流程
4. **调拨状态管理**：未入库、已完成、已作废三种状态的流转和控制
5. **入库确认流程**：目标仓库确认接收、库存更新、状态变更的完整入库流程
6. **作废处理机制**：调拨单作废、库存回滚、状态更新的异常处理流程

## 3. 详细功能设计

### 3.1 筛选查询功能

#### 3.1.1 多维度筛选条件
**功能描述**: 提供全面的调拨数据筛选功能

**筛选字段**:
- **经手人筛选**: 按操作调拨流程的人员筛选
  - 下拉选择经手人员
  - 支持经手人姓名搜索
  - 显示每个经手人的调拨记录数
- **状态筛选**: 按调拨状态筛选
  - 未入库：已出库但尚未入库的调拨
  - 已完成：调拨流程完成的记录
  - 已作废：已作废的调拨记录
- **调拨日期筛选**: 按调拨时间范围筛选
  - 起始日期选择器
  - 结束日期选择器
  - 快速日期选择（今天、本周、本月）
- **调拨单号查询**: 调拨单号的精确查询
  - 支持完整单号搜索
  - 支持单号模糊匹配
  - 支持批量单号查询
- **商品信息筛选**: 按商品名称或型号筛选
  - 商品名称模糊匹配
  - 商品型号搜索
  - 商品规格搜索
- **出库店/入库店筛选**: 按仓库进行筛选
  - 出库店下拉选择
  - 入库店下拉选择
  - 支持多仓库选择
  - 权限控制仓库范围

#### 3.1.2 搜索操作功能
**功能描述**: 搜索相关的操作和管理功能

**操作按钮**:
- **搜索按钮**: 执行筛选条件查询
  - 组合所有筛选条件
  - 实时更新调拨单表格
  - 显示筛选结果数量
- **重置功能**: 清除所有筛选条件
  - 恢复默认数据展示
  - 清除缓存的筛选条件
- **保存筛选**: 保存常用筛选条件
  - 自定义筛选方案
  - 快速应用保存的筛选

### 3.2 调拨单管理功能

#### 3.2.1 新增调拨功能
**功能描述**: 创建新的库存调拨单，支持在两个仓库或门店之间发起内部商品调拨

**页面入口**:
- **主要入口**: 商品管理 → 库存调拨 → 新增
- **快速入口**: 库存管理相关页面的快速调拨链接
- **业务入口**: 库存预警、门店补货等业务场景的调拨申请

**功能架构**:
库存调拨新增页面包含四个核心功能区域：
- **基础信息管理**: 调出调入仓库配置和调拨单元数据
- **商品选择管理**: 可调拨商品的选择和库存展示
- **调拨明细管理**: 调拨商品明细和数量管理
- **操作流程控制**: 调拨单的保存、提交和流程控制

**典型应用场景**:
- **门店库存紧张**: 从总部仓调货补充门店库存
- **热销商品补仓**: 热销商品的快速补货调拨
- **仓库平衡库存**: 减少积压，优化库存分布
- **季节性调拨**: 根据季节变化调整库存分布

**业务流程**:
库存调拨的完整业务流程：
```
创建调拨单 → 保存 → 调拨出库（锁定出库库存） → 调入仓库确认 → 入库 → 调拨完成
```

**基础信息管理功能**:
- **调拨仓库配置**: 配置调拨的出库和入库仓库信息
  - 调出门店：发货方组织
    - 显示字段，默认当前操作组织
    - 不可修改，基于用户权限确定
    - 影响调出仓库的可选范围
  - 调出仓库：选择出库仓位
    - 下拉选择，必填字段
    - 基于调出门店筛选可选仓库
    - 影响商品库存的显示范围
  - 调入门店：接收方门店或组织
    - 下拉选择，必填字段
    - 限制选项为非当前门店
    - 防止同门店内部调拨
  - 调入仓库：目标仓位
    - 下拉选择，必填字段
    - 基于调入门店筛选可选仓库
    - 与调出仓库不能相同
- **调拨单基础信息**: 管理调拨单的基础元数据
  - 调拨单号：自动生成唯一编号
    - 显示字段，系统自动生成（如DB...）
    - 唯一性保证
    - 支持自定义编号规则
  - 经手人：当前操作人
    - 下拉选择或系统识别
    - 默认当前登录账号
    - 支持修改为其他有权限用户
  - 调拨日期：调拨申请日期
    - 默认当前日期
    - 支持手动调整
  - 备注信息：自定义说明
    - 文本输入，非必填
    - 如"门店季节性补货"
    - 支持常用备注模板
- **联动规则验证**: 验证调拨配置的合理性
  - 仓库冲突检查：调入仓与调出仓不能一致
  - 门店权限验证：调入门店必须为非当前门店
  - 库存可用性：调出仓库必须有可用库存
  - 权限范围验证：用户对调入调出仓库的操作权限

**商品选择管理功能**:
- **商品选择器展示**: 右侧商品选择器的展示和交互
  - 商品缩略图：商品图片展示
  - 编码名称：商品编码和名称信息
  - SN管理提示：是否启用序列号管理的标识
  - 当前库存：在选定调出仓的可用库存
  - 选择操作：点击商品添加到明细表格
- **商品搜索功能**: 多种方式的商品搜索和定位
  - 扫码搜索：支持扫码枪快速定位商品
    - 条码识别
    - 自动匹配商品
    - 快速添加到明细
  - 手动搜索：商品名称、编码搜索
    - 模糊匹配
    - 实时搜索建议
    - 搜索结果高亮
- **库存可用性检查**: 检查商品在调出仓库的可用性
  - 库存数量显示：实时显示调出仓库存
  - 可用库存计算：扣除已预占库存
  - 零库存处理：库存为0时不允许添加或高亮提示
  - 库存预警：接近安全库存的预警

**调拨明细管理功能**:
- **明细信息展示**: 展示已选商品的调拨明细信息
  - 商品编码：系统内唯一标识
  - 商品名称：展示名称
  - 计量单位：个、台、套等计量单位
  - 当前库存：来自调出仓的实时可用库存
  - 调拨数量：用户输入调拨数量（不可超过库存）
  - 序列号（SN）：若启用序列号管理，需绑定具体SN列表
  - 备注：针对商品级别的备注说明
  - 操作：删除该商品记录
- **调拨数量管理**: 管理商品的调拨数量
  - 数量输入验证：
    - 非负数验证
    - 不能超过当前库存
    - 必须为整数
  - 库存实时检查：
    - 实时显示可用库存
    - 数量超限时即时提示
    - 库存变化时自动更新
  - 批量数量设置：
    - 支持批量设置调拨数量
    - 按比例分配调拨数量
    - 快速清零或最大值设置
- **序列号管理**: 管理启用SN控制的商品序列号
  - SN标识显示：商品名称显示SN管理提示
  - 序列号选择：从调出仓可用序列号中选择
  - 数量匹配验证：序列号数量与调拨数量匹配
  - 序列号绑定：建立序列号与调拨记录的关联
  - 序列号跟踪：记录序列号的调拨流转轨迹

**数据验证功能**:
- **基础信息验证**: 验证调拨单基础信息的完整性
  - 必填字段验证：调出调入仓库、经手人等必填
  - 仓库冲突验证：调出调入仓库不能相同
  - 门店权限验证：调入门店权限和范围验证
  - 日期合理性验证：调拨日期的合理性检查
- **明细数据验证**: 验证调拨明细的准确性
  - 商品存在性：验证商品在调出仓库存在
  - 数量合理性：验证调拨数量不超过可用库存
  - 序列号匹配：SN商品的序列号数量匹配验证
  - 明细完整性：至少包含一个调拨商品
- **业务规则验证**: 验证调拨业务规则的合理性
  - 库存充足性：确保调出仓库存充足
  - 调拨权限：验证商品的调拨权限
  - 仓库状态：验证调出调入仓库状态正常
  - 商品状态：验证商品状态允许调拨

**操作流程控制**:
- **保存草稿功能**: 保存调拨单草稿
  - 基础信息验证：验证基础信息完整性
  - 草稿状态保存：保存为草稿状态
  - 二次编辑支持：支持对草稿的二次编辑
  - 自动保存：定时自动保存草稿
- **调拨出库功能**: 提交调拨单并执行出库
  - 完整性验证：验证调拨信息完整性
  - 业务规则检查：检查所有业务规则
  - 库存锁定：锁定调出仓库存
  - 状态更新：更新调拨单状态
  - 通知发送：发送调拨通知给调入方
- **返回操作功能**: 返回调拨列表页面
  - 数据保护：提醒保存未提交的数据
  - 页面跳转：返回库存调拨列表页面
  - 状态恢复：恢复列表页面状态

#### 3.2.2 调拨审核流程
**功能描述**: 调拨单的审核和出库处理

**审核流程**:
- **调拨信息审查**: 审查调拨信息完整性和合理性
- **库存可用性确认**: 确认出库店库存充足
- **仓库权限验证**: 验证出入库店的操作权限
- **出库操作执行**:
  - 扣减出库店库存
  - 生成出库记录
  - 更新调拨状态为"未入库"
  - 发送出库通知

### 3.3 调拨流程控制

#### 3.3.1 调拨状态管理
**功能描述**: 管理调拨单的状态流转

**状态类型**:
- **未入库状态（红色标识）**:
  - 出库已执行，目标仓库尚未确认收货
  - 可执行入库操作
  - 可执行作废操作
  - 需要入库提醒或超期预警
- **已完成状态（绿色标识）**:
  - 调拨流程闭环，库存调整已完成
  - 只能查看详情和打印
  - 不可修改或作废
- **已作废状态（灰色标识）**:
  - 调拨流程中止，不再影响库存数据
  - 库存已回滚
  - 仅用于记录保存

#### 3.3.2 超期预警功能
**功能描述**: 监控未入库调拨的超期情况

**预警机制**:
- **时间阈值设置**: 设置调拨超期时间阈值
- **自动预警**: 超期调拨自动标识和提醒
- **预警通知**: 发送超期预警通知给相关人员
- **处理建议**: 提供超期调拨的处理建议

### 3.4 入库确认功能

#### 3.4.1 入库操作功能
**功能描述**: 目标仓库确认接收调拨商品

**入库流程**:
- **入库权限验证**: 检查用户入库操作权限
- **商品接收确认**: 确认接收的商品和数量
- **商品状态检查**: 检查商品的完整性和状态
- **数量核对**: 核对实际入库数量与调拨数量
- **入库登记**: 登记实际入库时间和数量
- **库存更新**: 增加入库店的库存数量

#### 3.4.2 入库异常处理
**功能描述**: 处理入库过程中的异常情况

**异常类型**:
- **数量不符**: 实际入库数量与调拨数量不符
- **商品损坏**: 调拨商品在运输过程中损坏
- **商品丢失**: 调拨商品在运输过程中丢失
- **质量问题**: 调拨商品存在质量问题

**处理机制**:
- **差异记录**: 记录数量差异和原因
- **损坏处理**: 处理损坏商品的报损和赔偿
- **丢失处理**: 处理丢失商品的责任认定
- **质量处理**: 处理质量问题商品的退换

### 3.5 数据展示功能

#### 3.5.1 调拨单表格展示
**功能描述**: 展示调拨单的详细信息

**表格字段**:
- **调拨日期**: 调拨单创建时间
- **调拨单号**: 系统生成的唯一编号
- **出库店**: 发出商品的仓库名称
- **入库店**: 接收商品的仓库名称
- **调拨商品**: 商品名称、型号、规格、单位
- **调拨数量**: 调拨的商品数量
- **备注信息**: 用户输入的说明内容
- **经手人**: 执行调拨的人员
- **调拨状态**: 当前状态（颜色标识）
- **操作按钮**: 可执行的操作功能

#### 3.5.2 详情查看功能
**功能描述**: 查看调拨单的详细信息

**详情内容**:
- **调拨单基本信息**: 单号、日期、经手人、状态
- **商品详细信息**: 商品规格、数量、价值等
- **出入库店信息**: 仓库详细信息和联系方式
- **调拨流程记录**: 创建、出库、入库等流程记录
- **异常处理记录**: 异常情况和处理结果记录

### 3.6 数据导出功能

#### 3.6.1 导出功能设计
**功能描述**: 支持调拨数据的导出

**导出选项**:
- **导出格式**: Excel格式导出
- **导出范围**: 
  - 当前筛选结果导出
  - 全部数据导出
  - 选中记录导出
- **导出字段**: 
  - 基础信息：日期、单号、出入库店
  - 商品信息：商品名称、数量、规格
  - 状态信息：调拨状态、经手人
  - 自定义字段选择

#### 3.6.2 打印功能设计
**功能描述**: 支持调拨单的打印

**打印功能**:
- **调拨单打印**: 标准调拨单格式打印
- **批量打印**: 支持批量调拨单打印
- **自定义打印**: 自定义打印格式和内容
- **打印预览**: 打印前预览功能

## 4. 用户界面设计

### 4.1 页面布局设计
- **顶部筛选区**: 多维度筛选条件和快速搜索
- **操作按钮区**: 搜索、导出、新增调拨等功能
- **数据表格区**: 调拨单数据表格展示
- **行级操作区**: 每条记录的操作按钮

### 4.2 交互设计规范
- **状态标识**: 直观的调拨状态颜色标识
- **操作确认**: 重要操作的确认弹窗
- **数据加载**: 加载状态和进度提示
- **响应反馈**: 操作成功/失败的即时反馈

### 4.3 响应式设计
- **PC端**: 完整功能展示，多列表格布局
- **平板端**: 适配中等屏幕，关键信息优先
- **移动端**: 卡片式布局，核心功能保留

## 5. 权限控制

### 5.1 功能权限
- **查看权限**: 调拨数据查看权限
- **新增权限**: 调拨单新增权限
- **入库权限**: 调拨商品入库权限
- **作废权限**: 调拨单作废权限
- **导出权限**: 数据导出权限

### 5.2 数据权限
- **仓库权限**: 按仓库范围控制调拨操作
- **门店权限**: 只能操作本门店相关调拨
- **经手人权限**: 主要操作自己经手的调拨

## 6. 异常处理

### 6.1 业务异常
- **库存不足**: 调拨时出库店库存不足的处理
- **入库异常**: 入库过程中的异常处理
- **超期预警**: 未入库调拨超期的处理

### 6.2 系统异常
- **数据异常**: 调拨数据异常的检测和修复
- **状态异常**: 调拨状态异常的处理
- **权限异常**: 权限验证失败的处理

---

**文档版本**: v1.0
**编写日期**: 2025-07-02
**编写人员**: AI系统架构师
**审核状态**: 待审核
