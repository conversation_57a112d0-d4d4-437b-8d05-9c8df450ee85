<execution>
<constraint>
## 基于学习文档的设计生成约束
- **requirement-extractor强制使用**：必须使用requirement-extractor生成需求分析文档
- **design-generator强制使用**：必须使用design-generator基于需求分析生成技术设计文档
- **文档路径严格遵循**：需求分析文档路径/docs/requirements/，技术设计文档路径/docs/design/
- **命名规则严格执行**：[项目代号]-requirement-analysis-[YYYYMMDD].md和[项目代号]-technical-design-[YYYYMMDD].md
- **章节结构强制要求**：需求分析8章节，技术设计10章节+Java TDD测试设计
- **Mermaid图表强制包含**：技术设计文档必须包含系统架构图、类图、流程图、时序图
- **技术债务追踪强制**：必须自动生成技术债务追踪文档
</constraint>

<rule>
## 需求分析文档生成强制规则
- **使用requirement-extractor**：基于PRD分析结果生成需求分析文档
- **8章节结构完整**：项目概述、用户角色、功能描述、用户故事、业务规则、接口规格、非功能性需求、术语表
- **功能点ID一致性**：使用[项目代号]-REQ-[序号]格式
- **可追溯性矩阵**：连接需求与测试用例
- **变更记录机制**：生成需求变更记录

## 技术设计文档生成强制规则
- **使用design-generator**：基于需求分析生成技术设计文档
- **10章节+测试设计**：系统架构概述、设计原则、系统架构图、类图、流程图、时序图、数据模型、API设计、安全设计、扩展性考量、Java TDD测试设计
- **Mermaid图表集成**：所有架构图、类图、流程图、时序图使用Mermaid格式
- **设计决策记录**：为每个设计决策添加理由说明
- **技术债务追踪**：自动生成技术债务追踪文档
</rule>

<guideline>
## 文档生成指导原则
- **需求驱动设计**：技术设计严格基于需求分析
- **标准化优先**：使用统一的模板和格式
- **可视化表达**：复杂设计用图表表达
- **可追溯性保证**：建立完整的追溯链路
- **质量导向**：确保文档质量和可操作性
- **TDD集成**：在设计阶段融入测试考虑
</guideline>

<process>
## 需求分析文档生成流程

### Step 1: 需求分析文档框架生成 (20%)
```mermaid
flowchart TD
    A[PRD分析结果] --> B[requirement-extractor启动]
    B --> C[项目信息提取]
    C --> D[文档框架生成]
    D --> E[章节结构创建]
    E --> F[基础模板应用]
```

**需求分析文档模板**：
```markdown
# [项目名称] 需求分析文档

**文档信息**
- 项目代号：[项目代号]
- 文档版本：v1.0
- 创建日期：[YYYY-MM-DD]
- 最后更新：[YYYY-MM-DD]
- 文档状态：草案/评审中/已确认

## 1. 项目概述
### 1.1 项目背景
### 1.2 项目目标
### 1.3 项目范围
### 1.4 项目约束

## 2. 用户角色
### 2.1 主要用户角色
### 2.2 次要用户角色
### 2.3 用户角色权限矩阵

## 3. 功能描述
### 3.1 核心功能
### 3.2 辅助功能
### 3.3 功能优先级

## 4. 用户故事
### 4.1 P0用户故事
### 4.2 P1用户故事
### 4.3 P2用户故事

## 5. 业务规则
### 5.1 数据验证规则
### 5.2 业务流程规则
### 5.3 权限控制规则

## 6. 接口规格
### 6.1 用户接口
### 6.2 系统接口
### 6.3 数据接口

## 7. 非功能性需求
### 7.1 性能需求
### 7.2 安全需求
### 7.3 可用性需求
### 7.4 可扩展性需求

## 8. 术语表
### 8.1 业务术语
### 8.2 技术术语
### 8.3 缩写说明

## 附录
### A. 可追溯性矩阵
### B. 需求变更记录
```

### Step 2: 需求内容填充 (40%)
```mermaid
graph TD
    A[PRD分析数据] --> B[功能点整理]
    A --> C[用户故事组织]
    A --> D[业务规则分类]
    A --> E[接口规格定义]
    
    B --> F[功能描述章节]
    C --> G[用户故事章节]
    D --> H[业务规则章节]
    E --> I[接口规格章节]
    
    F --> J[需求文档生成]
    G --> J
    H --> J
    I --> J
```

**关键内容填充示例**：
```markdown
## 3. 功能描述

### 3.1 核心功能

#### 3.1.1 用户登录功能
- **功能ID**：[项目代号]-REQ-001
- **功能名称**：用户登录
- **功能描述**：用户通过用户名和密码登录系统
- **优先级**：P0
- **输入**：用户名、密码
- **输出**：登录成功页面/错误提示
- **前置条件**：用户已注册
- **后置条件**：用户状态为已登录

## 4. 用户故事

### 4.1 P0用户故事

#### 4.1.1 用户登录故事
- **故事ID**：[项目代号]-REQ-001
- **用户故事**：作为系统用户，我希望能够登录系统，以便访问个人功能
- **验收标准**：
  - 给定用户输入正确的用户名和密码
  - 当用户点击登录按钮
  - 那么系统应该验证用户身份并跳转到主页
- **定义完成**：
  - 登录界面开发完成
  - 用户身份验证逻辑实现
  - 登录成功后的页面跳转
  - 错误处理和提示实现
```

### Step 3: 技术设计文档生成 (35%)
```mermaid
flowchart TD
    A[需求分析文档] --> B[design-generator启动]
    B --> C[架构设计]
    C --> D[组件设计]
    D --> E[接口设计]
    E --> F[数据模型设计]
    F --> G[测试设计]
    G --> H[技术设计文档生成]
```

**技术设计文档模板**：
```markdown
# [项目名称] 技术设计文档

## 1. 系统架构概述
### 1.1 架构原则
### 1.2 技术栈选择
### 1.3 部署架构

## 2. 设计原则
### 2.1 SOLID原则应用
### 2.2 设计模式选择
### 2.3 编码规范

## 3. 系统架构图
```mermaid
graph TB
    A[Web层] --> B[业务层]
    B --> C[数据层]
    B --> D[外部服务]
```

## 4. 类图
```mermaid
classDiagram
    class User {
        +String username
        +String password
        +login()
        +logout()
    }
    
    class UserService {
        +authenticateUser()
        +getUserInfo()
    }
    
    User --> UserService : uses
```

## 5. 流程图
```mermaid
flowchart TD
    A[用户登录] --> B{验证用户名密码}
    B -->|成功| C[生成会话]
    B -->|失败| D[返回错误]
    C --> E[跳转主页]
    D --> F[显示登录页面]
```

## 6. 时序图
```mermaid
sequenceDiagram
    participant U as User
    participant W as Web
    participant S as Service
    participant D as Database
    
    U->>W: 提交登录信息
    W->>S: 验证用户
    S->>D: 查询用户信息
    D-->>S: 返回用户数据
    S-->>W: 验证结果
    W-->>U: 登录成功/失败
```

## 7. 数据模型
### 7.1 实体关系图
### 7.2 数据表设计
### 7.3 数据字典

## 8. API设计
### 8.1 RESTful API规范
### 8.2 API接口定义
### 8.3 错误码设计

## 9. 安全设计
### 9.1 身份验证
### 9.2 权限控制
### 9.3 数据加密

## 10. 扩展性考量
### 10.1 性能优化
### 10.2 可扩展性设计
### 10.3 监控和日志

## 11. Java TDD测试设计
### 11.1 单元测试策略
### 11.2 集成测试计划
### 11.3 测试数据准备
### 11.4 测试覆盖率目标
### 11.5 模拟与存根策略
```

### Step 4: Java TDD测试设计集成 (25%)
```mermaid
graph TD
    A[需求分析] --> B[测试策略设计]
    B --> C[单元测试设计]
    B --> D[集成测试设计]
    B --> E[测试数据设计]
    
    C --> F[TDD红绿重构]
    D --> G[测试环境设计]
    E --> H[Mock策略设计]
    
    F --> I[测试设计文档]
    G --> I
    H --> I
```

**Java TDD测试设计示例**：
```markdown
## 11. Java TDD测试设计

### 11.1 单元测试策略
- **测试框架**：JUnit 5 + Mockito
- **覆盖率目标**：行覆盖率 ≥ 80%，分支覆盖率 ≥ 70%
- **测试原则**：
  - 每个业务规则对应一个测试类
  - 每个public方法对应至少一个测试方法
  - 边界条件和异常情况必须测试

### 11.2 集成测试计划
- **测试范围**：API层、服务层、数据层
- **测试环境**：Spring Boot Test + TestContainers
- **测试数据**：H2内存数据库 + 测试数据集

### 11.3 测试数据准备
```java
@TestConfiguration
public class TestDataConfig {
    
    @Bean
    @Primary
    public UserService mockUserService() {
        return Mockito.mock(UserService.class);
    }
    
    @PostConstruct
    public void setupTestData() {
        // 准备测试数据
    }
}
```

### 11.4 测试覆盖率目标
- **单元测试覆盖率**：≥ 80%
- **集成测试覆盖率**：≥ 60%
- **端到端测试覆盖率**：≥ 40%

### 11.5 模拟与存根策略
- **外部服务Mock**：使用WireMock模拟外部API
- **数据库Mock**：使用H2内存数据库
- **缓存Mock**：使用内存实现替代Redis
```

### Step 5: 文档质量保证与发布 (10%)
```mermaid
graph TD
    A[文档生成完成] --> B[格式检查]
    B --> C[内容验证]
    C --> D[可追溯性检查]
    D --> E[技术债务记录]
    E --> F[文档发布]
```

## 输出文档质量检查

### 需求分析文档检查清单
- [ ] 8个章节结构完整
- [ ] 所有功能点都有唯一ID
- [ ] 用户故事格式标准化
- [ ] 业务规则清晰明确
- [ ] 可追溯性矩阵完整
- [ ] 术语表准确完整

### 技术设计文档检查清单
- [ ] 10个章节+测试设计完整
- [ ] 所有Mermaid图表正确渲染
- [ ] 设计决策都有理由说明
- [ ] Java TDD测试设计完整
- [ ] 技术债务追踪文档生成
- [ ] API接口设计规范
</process>

<criteria>
## 设计文档生成质量标准

### 需求分析文档质量标准
- ✅ 使用requirement-extractor完成文档生成
- ✅ 8章节结构完整且内容充实
- ✅ 功能点ID格式符合[项目代号]-REQ-[序号]
- ✅ 可追溯性矩阵连接需求与测试用例
- ✅ 需求变更记录机制建立

### 技术设计文档质量标准
- ✅ 使用design-generator完成文档生成
- ✅ 10章节+Java TDD测试设计完整
- ✅ 所有架构图使用Mermaid格式正确渲染
- ✅ 每个设计决策都有理由说明
- ✅ 技术债务追踪文档自动生成

### 文档标准化标准
- ✅ 文档路径符合规范（/docs/requirements/, /docs/design/）
- ✅ 文档命名符合规范（[项目代号]-[类型]-[日期].md）
- ✅ 文档格式符合Markdown规范
- ✅ 图表格式符合Mermaid规范

### 可操作性标准
- ✅ 需求分析文档能够指导技术设计
- ✅ 技术设计文档能够指导实际开发
- ✅ 测试设计能够支持TDD流程
- ✅ 文档能够支持项目管理和追踪
</criteria>
</execution>