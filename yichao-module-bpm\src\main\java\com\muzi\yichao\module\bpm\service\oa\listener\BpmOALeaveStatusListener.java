package com.muzi.yichao.module.bpm.service.oa.listener;

import com.muzi.yichao.module.bpm.api.event.BpmProcessInstanceStatusEvent;
import com.muzi.yichao.module.bpm.api.event.BpmProcessInstanceStatusEventListener;
import com.muzi.yichao.module.bpm.service.oa.BpmOALeaveService;
import com.muzi.yichao.module.bpm.service.oa.BpmOALeaveServiceImpl;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * OA 请假单的结果的监听器实现类
 *
 * <AUTHOR>
 */
@Component
public class BpmOALeaveStatusListener extends BpmProcessInstanceStatusEventListener {

    @Resource
    private BpmOALeaveService leaveService;

    @Override
    protected String getProcessDefinitionKey() {
        return BpmOALeaveServiceImpl.PROCESS_KEY;
    }

    @Override
    protected void onEvent(BpmProcessInstanceStatusEvent event) {
        leaveService.updateLeaveStatus(Long.parseLong(event.getBusinessKey()), event.getStatus());
    }

}
