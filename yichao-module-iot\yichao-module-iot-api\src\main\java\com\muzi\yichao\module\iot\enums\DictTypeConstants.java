package com.muzi.yichao.module.iot.enums;

/**
 * IoT 字典类型的枚举类
 *
 * <AUTHOR>
 */
public class DictTypeConstants {

    public static final String PRODUCT_STATUS = "iot_product_status";
    public static final String PRODUCT_DEVICE_TYPE = "iot_product_device_type";
    public static final String NET_TYPE = "iot_net_type";
    public static final String PROTOCOL_TYPE = "iot_protocol_type";
    public static final String DATA_FORMAT = "iot_data_format";
    public static final String VALIDATE_TYPE = "iot_validate_type";

    public static final String DEVICE_STATE = "iot_device_state";
    
    public static final String IOT_DATA_BRIDGE_DIRECTION_ENUM = "iot_data_bridge_direction_enum";
    public static final String IOT_DATA_BRIDGE_TYPE_ENUM = "iot_data_bridge_type_enum";

}
