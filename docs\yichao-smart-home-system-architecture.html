<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>宜巢智能家装系统架构图 - 系统设计文档</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.4.0/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Arial', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #9C27B0 0%, #673AB7 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
            letter-spacing: 2px;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .content {
            padding: 40px;
        }
        
        .section {
            margin-bottom: 60px;
        }
        
        .section h2 {
            color: #9C27B0;
            font-size: 2em;
            margin-bottom: 20px;
            border-bottom: 3px solid #9C27B0;
            padding-bottom: 10px;
        }
        
        .section h3 {
            color: #673AB7;
            font-size: 1.5em;
            margin: 30px 0 15px 0;
        }
        
        .description {
            background: #f8f9fa;
            padding: 20px;
            border-left: 4px solid #9C27B0;
            margin-bottom: 30px;
            border-radius: 0 10px 10px 0;
        }
        
        .mermaid-container {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            overflow: auto;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        
        .feature-card h4 {
            margin: 0 0 15px 0;
            font-size: 1.3em;
        }
        
        .feature-card ul {
            margin: 0;
            padding: 0 0 0 20px;
        }
        
        .feature-card li {
            margin-bottom: 8px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .stat-label {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .toc {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .toc h3 {
            color: #9C27B0;
            margin-top: 0;
        }
        
        .toc ul {
            list-style: none;
            padding: 0;
        }
        
        .toc li {
            margin: 10px 0;
        }
        
        .toc a {
            color: #673AB7;
            text-decoration: none;
            font-weight: 500;
        }
        
        .toc a:hover {
            text-decoration: underline;
        }
        
        .footer {
            background: #f8f9fa;
            padding: 30px;
            text-align: center;
            color: #666;
            border-top: 1px solid #e0e0e0;
        }
        
        @media (max-width: 768px) {
            .content {
                padding: 20px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 1.8em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>宜巢智能家装系统</h1>
            <p>系统架构图与业务流程图</p>
        </div>
        
        <div class="content">
            <!-- 目录 -->
            <div class="toc">
                <h3>📋 目录导航</h3>
                <ul>
                    <li><a href="#overview">🏠 系统概览</a></li>
                    <li><a href="#architecture">🏗️ 系统架构图</a></li>
                    <li><a href="#modules">📦 核心功能模块</a></li>
                    <li><a href="#workflows">🔄 业务流程图</a></li>
                    <li><a href="#integration">🔗 模块集成关系</a></li>
                    <li><a href="#features">✨ 核心特性</a></li>
                </ul>
            </div>
            
            <!-- 系统概览 -->
            <div class="section" id="overview">
                <h2>🏠 系统概览</h2>
                <div class="description">
                    <p><strong>宜巢智能家装系统</strong>是一个全方位的智能家居设计、销售、安装和服务管理平台。系统涵盖了从客户需求收集、方案设计、商品管理、销售订单到工单执行的完整业务链条，为智能家装企业提供数字化转型的完整解决方案。</p>
                </div>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">6</div>
                        <div class="stat-label">核心业务模块</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">50+</div>
                        <div class="stat-label">功能页面</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">14</div>
                        <div class="stat-label">智能家居专业页面</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">100%</div>
                        <div class="stat-label">业务流程覆盖</div>
                    </div>
                </div>
            </div>
            
            <!-- 系统架构图 -->
            <div class="section" id="architecture">
                <h2>🏗️ 系统架构图</h2>
                <div class="description">
                    <p>系统采用模块化设计架构，包含智能家居、商品管理、客户管理、销售管理、项目管理和工单管理六大核心模块，模块间通过数据流和业务流实现深度集成。</p>
                </div>
                
                <div class="mermaid-container">
                    <div class="mermaid">
graph TB
    subgraph "宜巢智能家装系统架构"
        subgraph "业务展示层"
            A1[Web前端界面]
            A2[移动端应用]
            A3[客户端工具]
        end
        
        subgraph "业务应用层"
            subgraph "智能家居模块"
                B1[客户需求管理]
                B2[方案设计管理]
                B3[场景配置管理]
                B4[图纸管理]
                B5[模板方案管理]
                B6[专业计算工具]
            end
            
            subgraph "商品管理模块"
                C1[商品信息管理]
                C2[库存管理]
                C3[采购管理]
                C4[出库管理]
                C5[价格管理]
                C6[序列号管理]
            end
            
            subgraph "客户管理模块"
                D1[客户档案管理]
                D2[客户需求跟踪]
                D3[客户债务管理]
                D4[客户服务记录]
            end
            
            subgraph "销售管理模块"
                E1[销售订单管理]
                E2[合同管理]
                E3[报价管理]
                E4[发货退货管理]
                E5[消费开单]
            end
            
            subgraph "项目管理模块"
                F1[项目计划管理]
                F2[资源调度]
                F3[进度跟踪]
                F4[质量控制]
            end
            
            subgraph "工单管理模块"
                G1[工单创建]
                G2[任务分配]
                G3[执行跟踪]
                G4[质量验收]
            end
        end
        
        subgraph "数据服务层"
            H1[(客户数据库)]
            H2[(商品数据库)]
            H3[(项目数据库)]
            H4[(订单数据库)]
            H5[(工单数据库)]
            H6[(系统配置库)]
        end
        
        subgraph "基础设施层"
            I1[认证授权服务]
            I2[消息通知服务]
            I3[文件存储服务]
            I4[日志监控服务]
            I5[数据备份服务]
        end
    end
    
    %% 业务流关系
    B1 --> B2
    B2 --> B3
    B2 --> B4
    B2 --> E3
    E3 --> E1
    E1 --> F1
    F1 --> G1
    G1 --> G2
    
    %% 数据流关系
    B1 -.-> H1
    B2 -.-> H3
    C1 -.-> H2
    D1 -.-> H1
    E1 -.-> H4
    G1 -.-> H5
    
    %% 模块间集成关系
    B2 --> C1
    E1 --> C2
    F1 --> G1
    D1 --> B1
    
    %% 样式定义
    classDef smartHome fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef goods fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef customer fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    classDef sales fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef project fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef workorder fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef data fill:#f5f5f5,stroke:#424242,stroke-width:2px
    classDef infra fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px
    
    class B1,B2,B3,B4,B5,B6 smartHome
    class C1,C2,C3,C4,C5,C6 goods
    class D1,D2,D3,D4 customer
    class E1,E2,E3,E4,E5 sales
    class F1,F2,F3,F4 project
    class G1,G2,G3,G4 workorder
    class H1,H2,H3,H4,H5,H6 data
    class I1,I2,I3,I4,I5 infra
                    </div>
                </div>
            </div>
            
            <!-- 核心功能模块 -->
            <div class="section" id="modules">
                <h2>📦 核心功能模块</h2>
                
                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>🏠 智能家居模块</h4>
                        <ul>
                            <li>客户需求收集与分析</li>
                            <li>智能方案设计与配置</li>
                            <li>3D场景设计与预览</li>
                            <li>专业工具与计算</li>
                            <li>模板方案管理</li>
                            <li>图纸文档管理</li>
                        </ul>
                    </div>
                    
                    <div class="feature-card">
                        <h4>📦 商品管理模块</h4>
                        <ul>
                            <li>智能设备商品管理</li>
                            <li>多仓库库存管理</li>
                            <li>自动化采购补货</li>
                            <li>序列号追踪管理</li>
                            <li>价格体系管理</li>
                            <li>质量控制体系</li>
                        </ul>
                    </div>
                    
                    <div class="feature-card">
                        <h4>👥 客户管理模块</h4>
                        <ul>
                            <li>客户档案全生命周期</li>
                            <li>智能家居偏好分析</li>
                            <li>服务历史追踪</li>
                            <li>客户债务管理</li>
                            <li>客户满意度管理</li>
                            <li>客户关系维护</li>
                        </ul>
                    </div>
                    
                    <div class="feature-card">
                        <h4>💰 销售管理模块</h4>
                        <ul>
                            <li>智能家居解决方案销售</li>
                            <li>复杂合同管理</li>
                            <li>自动化报价生成</li>
                            <li>订单全生命周期管理</li>
                            <li>售后服务集成</li>
                            <li>销售业绩分析</li>
                        </ul>
                    </div>
                    
                    <div class="feature-card">
                        <h4>📋 项目管理模块</h4>
                        <ul>
                            <li>项目计划与调度</li>
                            <li>资源协调管理</li>
                            <li>进度实时跟踪</li>
                            <li>质量控制体系</li>
                            <li>成本控制管理</li>
                            <li>客户沟通管理</li>
                        </ul>
                    </div>
                    
                    <div class="feature-card">
                        <h4>🔧 工单管理模块</h4>
                        <ul>
                            <li>安装任务管理</li>
                            <li>技师任务分配</li>
                            <li>现场执行跟踪</li>
                            <li>质量验收管理</li>
                            <li>移动端作业支持</li>
                            <li>客户反馈收集</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- 业务流程图 -->
            <div class="section" id="workflows">
                <h2>🔄 业务流程图</h2>
                
                <h3>1. 智能家居业务主流程</h3>
                <div class="description">
                    <p>从客户首次咨询到项目最终交付的完整业务流程，包含需求分析、方案设计、商务洽谈、项目实施等关键环节。</p>
                </div>
                
                <div class="mermaid-container">
                    <div class="mermaid">
flowchart TD
    A[客户咨询] --> B[需求收集]
    B --> C[客户档案建立]
    C --> D[需求分析]
    D --> E[方案设计]
    E --> F[3D场景设计]
    F --> G[设备选型配置]
    G --> H[方案优化]
    H --> I[客户确认]
    I --> J{客户满意?}
    J -->|否| K[方案修改]
    K --> H
    J -->|是| L[方案报价]
    L --> M[商务洽谈]
    M --> N[合同签订]
    N --> O[项目立项]
    O --> P[设备采购]
    P --> Q[安装计划]
    Q --> R[工单创建]
    R --> S[现场安装]
    S --> T[系统调试]
    T --> U[客户验收]
    U --> V[项目交付]
    V --> W[售后服务]
    
    style A fill:#e1f5fe
    style V fill:#e8f5e8
    style J fill:#fff3e0
    style L fill:#f3e5f5
    style S fill:#fce4ec
                    </div>
                </div>
                
                <h3>2. 智能家居设计流程</h3>
                <div class="description">
                    <p>专业的智能家居设计流程，包含需求分析、模板应用、自定义设计、场景配置和专业计算等步骤。</p>
                </div>
                
                <div class="mermaid-container">
                    <div class="mermaid">
flowchart TB
    A1[客户需求分析] --> B1[房屋信息录入]
    B1 --> C1[需求分类整理]
    C1 --> D1[预算分配设置]
    D1 --> E1[方案模板选择]
    E1 --> F1{使用模板?}
    F1 -->|是| G1[应用模板方案]
    F1 -->|否| H1[自定义方案设计]
    G1 --> I1[模板个性化调整]
    H1 --> I1
    I1 --> J1[3D场景构建]
    J1 --> K1[设备布局设计]
    K1 --> L1[智能场景配置]
    L1 --> M1[专业计算验证]
    M1 --> N1[设计图纸生成]
    N1 --> O1[方案效果预览]
    O1 --> P1[客户确认]
    P1 --> Q1{需要调整?}
    Q1 -->|是| R1[方案修改]
    R1 --> J1
    Q1 -->|否| S1[方案定稿]
    S1 --> T1[设备清单生成]
    T1 --> U1[工程量计算]
    U1 --> V1[报价单生成]
    
    style A1 fill:#e1f5fe
    style F1 fill:#fff3e0
    style Q1 fill:#fff3e0
    style S1 fill:#e8f5e8
                    </div>
                </div>
                
                <h3>3. 项目执行流程</h3>
                <div class="description">
                    <p>项目执行阶段的详细流程，从项目启动到最终交付的完整管理过程。</p>
                </div>
                
                <div class="mermaid-container">
                    <div class="mermaid">
flowchart TD
    A2[项目启动] --> B2[项目团队组建]
    B2 --> C2[设备采购]
    C2 --> D2[安装计划制定]
    D2 --> E2[工单任务分解]
    E2 --> F2[技师团队分配]
    F2 --> G2[现场勘测]
    G2 --> H2[施工准备]
    H2 --> I2[设备安装]
    I2 --> J2[系统集成]
    J2 --> K2[功能调试]
    K2 --> L2[场景测试]
    L2 --> M2[系统优化]
    M2 --> N2[用户培训]
    N2 --> O2[验收测试]
    O2 --> P2{验收通过?}
    P2 -->|否| Q2[问题整改]
    Q2 --> K2
    P2 -->|是| R2[项目交付]
    R2 --> S2[项目结算]
    S2 --> T2[客户服务]
    T2 --> U2[维护保障]
    
    style A2 fill:#e1f5fe
    style P2 fill:#fff3e0
    style R2 fill:#e8f5e8
    style U2 fill:#f3e5f5
                    </div>
                </div>
                
                <h3>4. 商品采购与库存流程</h3>
                <div class="description">
                    <p>智能设备的采购、入库、出库和库存管理的完整流程，支持项目需求和库存预警。</p>
                </div>
                
                <div class="mermaid-container">
                    <div class="mermaid">
flowchart LR
    A3[项目需求] --> B3[库存检查]
    B3 --> C3{库存充足?}
    C3 -->|是| D3[库存预留]
    C3 -->|否| E3[采购需求生成]
    E3 --> F3[供应商选择]
    F3 --> G3[采购订单]
    G3 --> H3[货品验收]
    H3 --> I3[入库登记]
    I3 --> J3[序列号管理]
    J3 --> D3
    D3 --> K3[出库申请]
    K3 --> L3[出库审核]
    L3 --> M3[货品出库]
    M3 --> N3[物流配送]
    N3 --> O3[现场接收]
    O3 --> P3[安装使用]
    
    Q3[库存预警] --> E3
    R3[定期盘点] --> B3
    
    style C3 fill:#fff3e0
    style E3 fill:#f3e5f5
    style P3 fill:#e8f5e8
                    </div>
                </div>
            </div>
            
            <!-- 模块集成关系 -->
            <div class="section" id="integration">
                <h2>🔗 模块集成关系</h2>
                <div class="description">
                    <p>各个功能模块通过数据流、业务流和控制流实现深度集成，形成完整的业务闭环。</p>
                </div>
                
                <div class="mermaid-container">
                    <div class="mermaid">
graph TB
    subgraph "模块集成关系图"
        subgraph "前端业务层"
            A[智能家居模块]
            B[客户管理模块]
            C[销售管理模块]
            D[项目管理模块]
            E[工单管理模块]
            F[商品管理模块]
        end
        
        subgraph "数据集成层"
            G[客户数据中心]
            H[商品数据中心]
            I[项目数据中心]
            J[订单数据中心]
            K[库存数据中心]
        end
        
        subgraph "业务服务层"
            L[用户认证服务]
            M[权限管理服务]
            N[消息通知服务]
            O[报表分析服务]
            P[工作流引擎]
        end
    end
    
    %% 业务流集成
    B --> A
    A --> C
    C --> D
    D --> E
    E --> F
    F --> B
    
    %% 数据流集成
    A -.-> G
    A -.-> H
    A -.-> I
    B -.-> G
    C -.-> J
    C -.-> G
    D -.-> I
    E -.-> I
    F -.-> H
    F -.-> K
    
    %% 服务集成
    L --> A
    L --> B
    L --> C
    L --> D
    L --> E
    L --> F
    M --> A
    M --> B
    M --> C
    M --> D
    M --> E
    M --> F
    N --> P
    O --> G
    O --> H
    O --> I
    O --> J
    O --> K
    
    style A fill:#e1f5fe
    style B fill:#e8f5e8
    style C fill:#fff3e0
    style D fill:#f3e5f5
    style E fill:#fce4ec
    style F fill:#e0f2f1
                    </div>
                </div>
            </div>
            
            <!-- 核心特性 -->
            <div class="section" id="features">
                <h2>✨ 核心特性</h2>
                
                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>🎯 全流程数字化</h4>
                        <ul>
                            <li>从售前到售后全流程覆盖</li>
                            <li>无缝业务流转和数据传递</li>
                            <li>实时业务状态跟踪</li>
                            <li>智能化工作流引擎</li>
                        </ul>
                    </div>
                    
                    <div class="feature-card">
                        <h4>🎨 专业设计能力</h4>
                        <ul>
                            <li>3D可视化设计工具</li>
                            <li>智能场景配置系统</li>
                            <li>专业计算工具集成</li>
                            <li>模板化快速设计</li>
                        </ul>
                    </div>
                    
                    <div class="feature-card">
                        <h4>📊 智能数据分析</h4>
                        <ul>
                            <li>客户行为分析</li>
                            <li>销售趋势预测</li>
                            <li>库存优化建议</li>
                            <li>项目效益分析</li>
                        </ul>
                    </div>
                    
                    <div class="feature-card">
                        <h4>🔒 企业级安全</h4>
                        <ul>
                            <li>多层级权限控制</li>
                            <li>数据加密存储</li>
                            <li>操作日志审计</li>
                            <li>灾备容错机制</li>
                        </ul>
                    </div>
                    
                    <div class="feature-card">
                        <h4>📱 移动化办公</h4>
                        <ul>
                            <li>移动端工单管理</li>
                            <li>现场快速录入</li>
                            <li>实时消息推送</li>
                            <li>离线数据同步</li>
                        </ul>
                    </div>
                    
                    <div class="feature-card">
                        <h4>🔧 灵活扩展性</h4>
                        <ul>
                            <li>模块化架构设计</li>
                            <li>开放API接口</li>
                            <li>第三方系统集成</li>
                            <li>个性化定制能力</li>
                        </ul>
                    </div>
                </div>
                
                <h3>🚀 业务价值提升</h3>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">40%+</div>
                        <div class="stat-label">设计效率提升</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">30%+</div>
                        <div class="stat-label">项目管理效率提升</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">25%</div>
                        <div class="stat-label">销售周期缩短</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">30%</div>
                        <div class="stat-label">运营成本降低</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p><strong>宜巢智能家装系统</strong> | 数字化智能家居解决方案平台</p>
            <p>文档版本：v1.0 | 生成时间：2025-07-19 | 系统架构设计文档</p>
        </div>
    </div>
    
    <script>
        // 初始化 Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'cardinal'
            },
            themeVariables: {
                primaryColor: '#9C27B0',
                primaryTextColor: '#ffffff',
                primaryBorderColor: '#673AB7',
                lineColor: '#9C27B0',
                secondaryColor: '#e1f5fe',
                tertiaryColor: '#f8f9fa'
            }
        });
        
        // 平滑滚动
        document.querySelectorAll('.toc a').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>