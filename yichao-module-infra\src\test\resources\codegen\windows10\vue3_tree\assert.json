[{"contentPath": "java/InfraCategoryListReqVO", "filePath": "yichao-module-infra/yichao-module-infra-biz/src/main/java/com.muzi.yichao/module/infra/controller/admin/demo/vo/InfraCategoryListReqVO.java"}, {"contentPath": "java/InfraCategoryRespVO", "filePath": "yichao-module-infra/yichao-module-infra-biz/src/main/java/com.muzi.yichao/module/infra/controller/admin/demo/vo/InfraCategoryRespVO.java"}, {"contentPath": "java/InfraCategorySaveReqVO", "filePath": "yichao-module-infra/yichao-module-infra-biz/src/main/java/com.muzi.yichao/module/infra/controller/admin/demo/vo/InfraCategorySaveReqVO.java"}, {"contentPath": "java/InfraCategoryController", "filePath": "yichao-module-infra/yichao-module-infra-biz/src/main/java/com.muzi.yichao/module/infra/controller/admin/demo/InfraCategoryController.java"}, {"contentPath": "java/InfraCategoryDO", "filePath": "yichao-module-infra/yichao-module-infra-biz/src/main/java/com.muzi.yichao/module/infra/dal/dataobject/demo/InfraCategoryDO.java"}, {"contentPath": "java/InfraCategoryMapper", "filePath": "yichao-module-infra/yichao-module-infra-biz/src/main/java/com.muzi.yichao/module/infra/dal/mysql/demo/InfraCategoryMapper.java"}, {"contentPath": "xml/InfraCategoryMapper", "filePath": "yichao-module-infra/yichao-module-infra-biz/src/main/resources/mapper/demo/InfraCategoryMapper.xml"}, {"contentPath": "java/InfraCategoryServiceImpl", "filePath": "yichao-module-infra/yichao-module-infra-biz/src/main/java/com.muzi.yichao/module/infra/service/demo/InfraCategoryServiceImpl.java"}, {"contentPath": "java/InfraCategoryService", "filePath": "yichao-module-infra/yichao-module-infra-biz/src/main/java/com.muzi.yichao/module/infra/service/demo/InfraCategoryService.java"}, {"contentPath": "java/InfraCategoryServiceImplTest", "filePath": "yichao-module-infra/yichao-module-infra-biz/src/test/java/com.muzi.yichao/module/infra/service/demo/InfraCategoryServiceImplTest.java"}, {"contentPath": "java/ErrorCodeConstants_手动操作", "filePath": "yichao-module-infra/yichao-module-infra-api/src/main/java/com.muzi.yichao/module/infra/enums/ErrorCodeConstants_手动操作.java"}, {"contentPath": "sql/sql", "filePath": "sql/sql.sql"}, {"contentPath": "sql/h2", "filePath": "sql/h2.sql"}, {"contentPath": "vue/index", "filePath": "yichao-ui-admin-vue3/src/views/infra/demo/index.vue"}, {"contentPath": "vue/CategoryForm", "filePath": "yichao-ui-admin-vue3/src/views/infra/demo/CategoryForm.vue"}, {"contentPath": "ts/index", "filePath": "yichao-ui-admin-vue3/src/api/infra/demo/index.ts"}]