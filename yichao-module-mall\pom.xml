<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>yichao</artifactId>
        <groupId>com.muzi.smarthome</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>yichao-module-mall</artifactId>
    <packaging>pom</packaging>

    <name>${project.artifactId}</name>

    <description>
        商城大模块，由 product 商品、promotion 营销、trade 交易、statistics 统计等组成
    </description>
    <modules>
        <module>yichao-module-product</module>
        <module>yichao-module-promotion</module>
        <module>yichao-module-trade</module>
        <module>yichao-module-statistics</module>
        <!--
            特殊：为什么会有 yichao-module-trade-api 呢？
                yichao-module-promotion 和 yichao-module-trade 之间相互循环依赖，所以抽出 yichao-module-trade-api 模块，这样：
                1. yichao-module-promotion 依赖 yichao-module-trade-api
                2. yichao-module-trade 依赖 yichao-module-promotion
            从而不存在相互（循环）依赖，即 yichao-module-trade => yichao-module-promotion => yichao-module-trade-api
         -->
        <module>yichao-module-trade-api</module>
    </modules>

</project>
