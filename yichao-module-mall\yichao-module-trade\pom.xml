<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.muzi.smarthome</groupId>
        <artifactId>yichao-module-mall</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>yichao-module-trade</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        trade 模块，主要实现交易相关功能
        例如：订单、退款、购物车等功能。
    </description>

    <dependencies>
        <dependency>
            <groupId>com.muzi.smarthome</groupId>
            <artifactId>yichao-module-trade-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.muzi.smarthome</groupId>
            <artifactId>yichao-module-product</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.muzi.smarthome</groupId>
            <artifactId>yichao-module-pay</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.muzi.smarthome</groupId>
            <artifactId>yichao-module-promotion</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.muzi.smarthome</groupId>
            <artifactId>yichao-module-member</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.muzi.smarthome</groupId>
            <artifactId>yichao-module-system</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>com.muzi.smarthome</groupId>
            <artifactId>yichao-spring-boot-starter-biz-tenant</artifactId>
        </dependency>
        <dependency>
            <groupId>com.muzi.smarthome</groupId>
            <artifactId>yichao-spring-boot-starter-biz-ip</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.muzi.smarthome</groupId>
            <artifactId>yichao-spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.muzi.smarthome</groupId>
            <artifactId>yichao-spring-boot-starter-security</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.muzi.smarthome</groupId>
            <artifactId>yichao-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.muzi.smarthome</groupId>
            <artifactId>yichao-spring-boot-starter-redis</artifactId>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>com.muzi.smarthome</groupId>
            <artifactId>yichao-spring-boot-starter-test</artifactId>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>com.muzi.smarthome</groupId>
            <artifactId>yichao-spring-boot-starter-excel</artifactId>
        </dependency>
    </dependencies>

</project>
