### 请求 /login 接口 => 成功
POST {{appApi}}/member/auth/login
Content-Type: application/json
tenant-id: {{appTenantId}}

{
  "mobile": "15601691388",
  "password": "admin123"
}

### 请求 /send-sms-code 接口 => 成功
POST {{appApi}}/member/auth/send-sms-code
Content-Type: application/json
tenant-id: {{appTenantId}}

{
  "mobile": "15601691388",
  "scene": 1
}

### 请求 /sms-login 接口 => 成功
POST {{appApi}}/member/auth/sms-login
Content-Type: application/json
tenant-id: {{appTenantId}}
terminal: 30

{
  "mobile": "15601691388",
  "code": 9999
}

### 请求 /social-login 接口 => 成功
POST {{appApi}}/member/auth/social-login
Content-Type: application/json
tenant-id: {{appTenantId}}

{
  "type": 34,
  "code": "0e1oc9000CTjFQ1oim200bhtb61oc90g",
  "state": "default"
}

### 请求 /weixin-mini-app-login 接口 => 成功
POST {{appApi}}/member/auth/weixin-mini-app-login
Content-Type: application/json
tenant-id: {{appTenantId}}

{
  "phoneCode": "618e6412e0c728f5b8fc7164497463d0158a923c9e7fd86af8bba393b9decbc5",
  "loginCode": "001frTkl21JUf94VGxol2hSlff1frTkR"
}

### 请求 /logout 接口 => 成功
POST {{appApi}}/member/auth/logout
Content-Type: application/json
Authorization: Bearer c1b76bdaf2c146c581caa4d7fd81ee66
tenant-id: {{appTenantId}}

### 请求 /auth/refresh-token 接口 => 成功
POST {{appApi}}/member/auth/refresh-token?refreshToken=bc43d929094849a28b3a69f6e6940d70
Content-Type: application/json
tenant-id: {{appTenantId}}

### 请求 /auth/create-weixin-jsapi-signature 接口 => 成功
POST {{appApi}}/member/auth/create-weixin-jsapi-signature?url=http://www.iocoder.cn
Authorization: Bearer {{appToken}}
tenant-id: {{appTenantId}}
