package com.muzi.yichao.module.trade.job.order;

import com.muzi.yichao.framework.quartz.core.handler.JobHandler;
import com.muzi.yichao.framework.tenant.core.job.TenantJob;
import com.muzi.yichao.module.trade.service.order.TradeOrderUpdateService;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * 交易订单的自动过期 Job
 *
 * <AUTHOR>
 */
@Component
public class TradeOrderAutoCancelJob implements JobHandler {

    @Resource
    private TradeOrderUpdateService tradeOrderUpdateService;

    @Override
    @TenantJob
    public String execute(String param) {
        int count = tradeOrderUpdateService.cancelOrderBySystem();
        return String.format("过期订单 %s 个", count);
    }

}
