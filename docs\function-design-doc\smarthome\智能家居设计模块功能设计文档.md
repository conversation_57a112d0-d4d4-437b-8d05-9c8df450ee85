# 智能家装管理平台-智能家居设计模块功能设计文档

## 1. 模块概述

### 1.1 功能定位
智能家居设计模块是智能家装管理平台的创意核心，负责智能家居方案的设计、3D场景构建、设备配置和投影距离计算。该模块通过可视化设计工具，帮助设计师快速创建专业的智能家居解决方案，并为客户提供直观的预览体验。

### 1.2 业务价值
- **可视化设计**：提供直观的3D场景设计环境，提升设计效率
- **标准化方案**：建立标准化的智能家居设计模板和场景库
- **精确计算**：智能计算设备配置和投影距离，确保方案可行性
- **客户体验**：为客户提供沉浸式的方案预览和体验
- **快速迭代**：支持方案的快速修改和优化调整

### 1.3 目标用户
- **设计师**：方案设计、场景构建、设备配置
- **客户**：方案预览、需求反馈、方案确认
- **项目经理**：方案审核、成本评估、可行性分析
- **销售人员**：方案展示、客户沟通、报价生成
- **技术人员**：设备参数配置、技术可行性验证

## 2. 功能架构图

```mermaid
graph TB
    subgraph "智能家装管理平台-智能家居设计模块"
        subgraph "设计方案管理"
            A[方案创建]
            B[方案编辑]
            C[方案版本控制]
        end
        
        subgraph "3D场景设计"
            D[3D建模]
            E[场景配置]
            F[设备布局]
        end
        
        subgraph "场景库管理"
            G[场景模板]
            H[场景分类]
            I[场景预览]
        end
        
        subgraph "设备配置管理"
            J[设备选型]
            K[参数配置]
            L[兼容性检查]
        end
        
        subgraph "计算工具"
            M[投影距离计算]
            N[照明计算]
            O[网络覆盖计算]
        end
        
        subgraph "客户体验"
            P[VR预览]
            Q[AR体验]
            R[方案分享]
        end
        
        subgraph "数据存储层"
            S[(方案数据)]
            T[(场景数据)]
            U[(设备数据)]
            V[(计算数据)]
        end
    end
    
    %% 模块间关系
    A --> S
    B --> S
    C --> S
    D --> T
    E --> T
    F --> T
    G --> T
    H --> T
    I --> T
    J --> U
    K --> U
    L --> U
    M --> V
    N --> V
    O --> V
    
    %% 业务流程关系
    A -.-> D
    D -.-> E
    E -.-> F
    F -.-> J
    J -.-> K
    K -.-> M
    M -.-> P
    
    style A fill:#e1f5fe
    style D fill:#e8f5e8
    style G fill:#fff3e0
    style M fill:#f3e5f5
    style P fill:#fce4ec
```

## 3. 核心业务流程

### 3.1 智能家居设计全流程

```mermaid
flowchart TB
    A[客户需求分析] --> B[选择设计模板]
    B --> C[3D户型导入]
    C --> D[房间功能规划]
    D --> E[智能设备选择]
    E --> F[设备位置布局]
    F --> G[场景功能配置]
    G --> H[投影距离计算]
    H --> I[网络覆盖分析]
    I --> J[设备兼容性检查]
    J --> K{配置是否合理}
    K -->|否| L[调整配置]
    L --> F
    K -->|是| M[3D效果渲染]
    M --> N[客户预览确认]
    N --> O{客户是否满意}
    O -->|否| P[方案修改]
    P --> E
    O -->|是| Q[方案锁定]
    Q --> R[生成产品清单]
    R --> S[成本计算]
    S --> T[方案交付]
    
    style A fill:#e1f5fe
    style C fill:#e8f5e8
    style G fill:#fff3e0
    style M fill:#f3e5f5
    style Q fill:#e0f2f1
    style T fill:#fce4ec
```

## 4. 详细功能设计

### 4.1 设计方案管理

#### 4.1.1 方案列表管理
**功能描述**：智能家居设计方案的统一管理界面

**界面功能**：
- **筛选条件**：
  - 全部类别：按方案类型筛选
  - 所有进度：按设计进度筛选
  - 请选择工程师：按设计师筛选
  - 项目名称：项目名称搜索
  - 客户姓名/手机号：客户信息搜索

- **操作按钮**：
  - 搜索：执行筛选搜索
  - 添加方案：创建新的设计方案
  - 帮助：设计帮助文档

- **数据展示字段**：
  - 编号：方案编号（如757、756、754等）
  - 方案名称：如"万科城市花园七区16号101"、"无线入家"、"Nickey"等
  - 方案进度：当前设计进度状态
  - 客户姓名：项目客户姓名
  - 客户手机：客户联系电话
  - 大屏：显示大屏配置情况
  - 备注：方案备注信息
  - 图纸数量：关联图纸数量
  - 工单数量：关联工单数量
  - 工程师：负责设计师
  - 时间：创建或更新时间

- **快捷操作**：
  - 查看：查看方案详情
  - 删除：删除方案
  - 工单：创建关联工单
  - 生成PDF：导出PDF版本
  - 报价单：生成报价单
  - 删除：删除方案
  - 引用方案：复制现有方案
  - 编辑方案：编辑修改方案
  - 打单：打印施工单
  - 加入待办：加入待办列表

#### 4.1.2 方案创建和编辑
**功能描述**：创建和编辑智能家居设计方案

**方案编辑界面功能**：
- **基础信息设置**：
  - 方案类别：选择方案类型（全屋智能等）
  - 方案名称：如"万科城市花园七区16号101"
  - 工程师：负责设计师选择
  - 关联客户：选择项目客户
  - 客户地址：项目地址信息
  - 地图标注：在地图上标注具体位置
  - 备注：方案说明和特殊要求

- **客户权限设置**：
  - 查看高级位置：是否允许客户查看详细位置
  - 查看报价明细：是否允许客户查看报价详情
  - 隐藏报价商品名：是否隐藏具体商品名称
  - 仅显示整分区报价：是否只显示分区总价
  - 显示高端链接：是否显示高端产品链接

- **进度设置**：
  - 大屏显示：是否在大屏中显示
  - 是否模板：是否设为标准模板

- **安装服务设置**：
  - 按整体安装：统一安装服务
  - 按照完全安装计算：完整安装服务计算
  - 安装服务比例：安装服务费比例设置

#### 4.1.3 方案维护工具
**功能描述**：方案数据的维护和管理工具

**主要功能**：
- **数据导入导出**：支持方案数据的批量导入导出
- **版本控制**：方案版本管理，支持版本比较和回滚
- **模板管理**：将优秀方案设为标准模板
- **数据备份**：方案数据的定期备份和恢复
- **权限控制**：方案访问和编辑权限管理

### 4.2 3D场景设计

#### 4.2.1 3D建模工具
**功能描述**：3D场景的建模和设计工具

**主要功能**：
- **户型导入**：支持CAD图纸导入，自动生成3D户型
- **房间创建**：可视化房间创建和尺寸调整
- **墙体编辑**：墙体的添加、删除、移动和调整
- **门窗设置**：门窗的添加和位置调整
- **层高设置**：房间层高的设置和调整

#### 4.2.2 场景配置
**功能描述**：智能家居场景的配置和设置

**添加场景界面功能**：
- **场景信息**：
  - 场景类别：选择场景类型
  - 填写场景名称：自定义场景名称
  - 自动读取服务商：自动获取服务商信息
  - 自动读取服务方式：获取服务配置方式

- **场景图像**：
  - 场景图片：上传场景图片
  - 场景说明：详细的场景说明文字
  - 场景字号：场景文字显示大小设置

- **3D场景展示**：
  - 3D房屋模型：显示完整的3D房屋模型
  - 设备位置标注：在3D模型中标注智能设备位置
  - 交互操作：支持360度旋转、缩放、平移操作
  - 房间导航：点击不同房间进行切换查看

#### 4.2.3 设备布局设计
**功能描述**：智能设备在3D场景中的布局设计

**主要功能**：
- **设备拖拽**：从设备库拖拽设备到3D场景中
- **位置调整**：在3D场景中调整设备位置
- **角度设置**：设置设备的安装角度和朝向
- **高度设置**：设置设备的安装高度
- **碰撞检测**：检测设备放置位置的合理性

### 4.3 场景库管理

#### 4.3.1 场景列表管理
**功能描述**：智能家居场景模板的统一管理

**界面功能**：
- **场景分类筛选**：按场景类型筛选模板
- **搜索功能**：按场景名称搜索
- **批量操作**：
  - 添加场景：创建新场景模板
  - 下载云场景：从云端下载场景模板
  - 清空：清空场景列表

**场景展示信息**：
- **网页图片**：场景预览图
- **PDF图**：场景PDF文档
- **场景名称**：具体场景名称
- **场景类别**：场景分类标识
- **简介**：场景功能简介
- **操作**：
  - PDF查看：预览PDF文档
  - 编辑：编辑场景模板
  - 删除：删除场景模板

**标准场景模板示例**：
- **前沿科技**：全部场景 - 展示最新智能家居技术
- **全屋出货**：全部场景 - 完整的全屋智能解决方案
- **易来功能简介**：全部场景 - 智能功能介绍场景
- **公司介绍**：全部场景 - 公司品牌介绍场景
- **五星好光**：全部场景 - 智能照明场景
- **服务流程**：全部场景 - 服务流程展示
- **易来**：全部场景 - 品牌核心功能场景
- **会客厅式**：全部场景 - 客厅智能化场景
- **复室场景**：全部场景 - 复式住宅场景
- **客厅无主灯**：全部场景 - 无主灯照明设计

#### 4.3.2 场景模板创建
**功能描述**：创建标准化的智能家居场景模板

**主要功能**：
- **场景定义**：定义场景的功能和适用范围
- **设备配置**：标准化的设备配置方案
- **参数设置**：场景参数的标准化设置
- **效果预览**：场景效果的预览和展示
- **模板保存**：将场景保存为可复用模板

#### 4.3.3 场景模板管理
**功能描述**：场景模板的维护和管理

**主要功能**：
- **模板分类**：按功能和应用场景分类管理
- **版本管理**：模板版本的更新和维护
- **共享机制**：模板的团队共享和权限控制
- **质量控制**：模板质量的审核和认证
- **使用统计**：模板使用情况的统计分析

### 4.4 设备配置管理

#### 4.4.1 设备选型
**功能描述**：智能设备的选择和配置

**主要功能**：
- **设备分类**：按功能分类展示设备（照明、安防、环境等）
- **设备筛选**：按品牌、价格、功能等条件筛选
- **参数对比**：设备参数的对比和选择
- **兼容性检查**：设备间的兼容性验证
- **推荐引擎**：基于需求的智能设备推荐

#### 4.4.2 参数配置
**功能描述**：智能设备的详细参数配置

**主要功能**：
- **基础参数**：设备的基本参数设置
- **功能参数**：设备功能的详细配置
- **通信参数**：设备通信协议和参数设置
- **场景联动**：设备在不同场景下的行为设置
- **自动化规则**：设备自动化控制规则配置

#### 4.4.3 设备清单生成
**功能描述**：根据设计方案生成设备清单

**主要功能**：
- **自动统计**：自动统计方案中使用的所有设备
- **数量计算**：精确计算各类设备的需求数量
- **成本核算**：计算设备的总成本和分项成本
- **供应商匹配**：匹配合适的设备供应商
- **采购清单**：生成详细的设备采购清单

### 4.5 计算工具

#### 4.5.1 投影距离计算
**功能描述**：投影设备距离的精确计算

**投影距离计算界面**：
- **新增功能**：支持添加新的投影距离计算
- **计算参数**：
  - 幕布尺寸（英寸）：输入幕布的尺寸规格
  - 宽度（米）：幕布的实际宽度
  - 高度（米）：幕布的实际高度
  - 操作：编辑、删除计算记录

**计算功能**：
- **投影距离计算**：根据投影仪规格和幕布尺寸计算最佳投影距离
- **画面尺寸计算**：根据投影距离计算可投影的画面尺寸
- **安装位置建议**：提供投影仪的最佳安装位置建议
- **角度调整计算**：计算投影角度和梯形校正参数

#### 4.5.2 照明计算
**功能描述**：智能照明系统的计算和设计

**主要功能**：
- **照度计算**：根据房间尺寸和用途计算所需照度
- **灯具选型**：根据照度需求选择合适的灯具
- **布局优化**：优化灯具的布局和数量
- **能耗计算**：计算照明系统的能耗和成本
- **色温配置**：配置不同场景下的色温设置

#### 4.5.3 网络覆盖计算
**功能描述**：智能家居网络覆盖的计算和优化

**主要功能**：
- **信号强度计算**：计算WiFi信号在不同位置的强度
- **覆盖范围分析**：分析网络设备的覆盖范围
- **设备布局优化**：优化路由器和网关的布局位置
- **带宽需求计算**：计算智能设备的带宽需求
- **网络架构设计**：设计合理的网络架构方案

### 4.6 客户体验

#### 4.6.1 VR预览
**功能描述**：为客户提供VR虚拟现实预览体验

**主要功能**：
- **VR场景生成**：将3D方案转换为VR场景
- **沉浸式体验**：客户可在VR环境中体验智能家居
- **交互操作**：在VR中操作和体验智能设备功能
- **实时修改**：根据客户反馈实时修改方案
- **多平台支持**：支持不同VR设备和平台

#### 4.6.2 AR体验
**功能描述**：增强现实技术的家居设计体验

**主要功能**：
- **实景叠加**：在真实环境中叠加虚拟智能设备
- **移动端体验**：通过手机或平板体验AR效果
- **设备试摆**：在真实空间中试摆智能设备
- **效果对比**：对比安装前后的效果差异
- **客户互动**：客户可自主体验和调整方案

#### 4.6.3 方案分享
**功能描述**：方案的多渠道分享和展示

**主要功能**：
- **在线分享**：生成在线链接供客户查看
- **移动端查看**：客户可通过手机查看方案
- **PDF导出**：生成PDF版本的方案文档
- **视频演示**：生成方案的视频演示
- **社交分享**：支持微信、朋友圈等社交平台分享

## 5. 界面设计规范

### 5.1 方案列表界面
- **筛选区域**：顶部多维度筛选条件
- **操作工具栏**：搜索、新增、帮助等操作按钮
- **方案卡片**：方案信息的卡片式展示
- **快捷操作**：悬浮操作菜单，提供快速操作入口

### 5.2 3D设计界面
- **工具栏**：左侧设备库和工具面板
- **3D视窗**：中央3D场景展示区域
- **属性面板**：右侧设备属性和参数配置
- **操作控制**：底部视角控制和操作按钮

### 5.3 场景配置界面
- **场景选择**：顶部场景类型选择
- **配置表单**：左侧场景参数配置表单
- **实时预览**：右侧3D场景实时预览
- **保存操作**：底部保存和取消操作

## 6. 权限控制

### 6.1 角色权限矩阵

| 功能模块 | 设计师 | 客户 | 项目经理 | 销售人员 | 管理员 |
|----------|--------|------|----------|----------|--------|
| 方案查看 | ✅ | ✅ | ✅ | ✅ | ✅ |
| 方案创建 | ✅ | ❌ | ✅ | ❌ | ✅ |
| 方案编辑 | ✅ | ❌ | ✅ | ❌ | ✅ |
| 3D设计 | ✅ | ❌ | ❌ | ❌ | ✅ |
| 场景配置 | ✅ | ❌ | ❌ | ❌ | ✅ |
| 模板管理 | ✅ | ❌ | ✅ | ❌ | ✅ |
| 计算工具 | ✅ | ❌ | ✅ | ✅ | ✅ |
| VR/AR体验 | ✅ | ✅ | ✅ | ✅ | ✅ |

### 6.2 数据权限控制
- **方案权限**：只能查看和编辑分配的方案
- **客户权限**：只能查看自己的方案，不能编辑
- **模板权限**：按级别控制模板的使用和编辑权限
- **功能权限**：按角色控制高级功能的使用权限

## 7. 技术实现建议

### 7.1 3D渲染技术
- **WebGL**：基于WebGL的3D渲染引擎
- **Three.js**：3D图形库，支持复杂场景渲染
- **材质系统**：PBR材质系统，提供真实的视觉效果
- **光照系统**：动态光照和阴影系统

### 7.2 VR/AR技术
- **WebXR**：Web端VR/AR解决方案
- **A-Frame**：WebVR框架
- **AR.js**：Web端AR库
- **移动端支持**：iOS ARKit、Android ARCore

### 7.3 计算引擎
- **物理引擎**：Three.js物理引擎
- **数学计算库**：Math.js数学计算库
- **几何计算**：CSG几何计算库
- **优化算法**：遗传算法、粒子群算法

## 8. 数据库设计建议

### 8.1 核心数据表

#### 设计方案表 (design_schemes)
```sql
CREATE TABLE design_schemes (
    id BIGINT PRIMARY KEY,
    scheme_code VARCHAR(50) UNIQUE,
    scheme_name VARCHAR(200),
    project_id BIGINT,
    designer_id BIGINT,
    scheme_category VARCHAR(50),
    scheme_progress VARCHAR(20),
    customer_id BIGINT,
    customer_address TEXT,
    scheme_status VARCHAR(20),
    is_template BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

#### 3D场景表 (scene_3d)
```sql
CREATE TABLE scene_3d (
    id BIGINT PRIMARY KEY,
    scheme_id BIGINT,
    scene_name VARCHAR(200),
    scene_type VARCHAR(50),
    scene_data JSON,
    scene_image VARCHAR(500),
    scene_description TEXT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

#### 设备配置表 (device_configs)
```sql
CREATE TABLE device_configs (
    id BIGINT PRIMARY KEY,
    scheme_id BIGINT,
    device_id BIGINT,
    device_name VARCHAR(200),
    device_type VARCHAR(50),
    device_params JSON,
    position_x DECIMAL(10,2),
    position_y DECIMAL(10,2),
    position_z DECIMAL(10,2),
    rotation_x DECIMAL(10,2),
    rotation_y DECIMAL(10,2),
    rotation_z DECIMAL(10,2),
    created_at TIMESTAMP
);
```

#### 投影距离计算表 (projection_calculations)
```sql
CREATE TABLE projection_calculations (
    id BIGINT PRIMARY KEY,
    scheme_id BIGINT,
    screen_size_inch DECIMAL(5,1),
    screen_width_meter DECIMAL(5,2),
    screen_height_meter DECIMAL(5,2),
    projection_distance DECIMAL(5,2),
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

## 9. 性能优化建议

### 9.1 3D渲染优化
- **LOD技术**：多级细节模型，根据距离调整模型精度
- **纹理优化**：纹理压缩和动态加载
- **几何优化**：几何体合并和简化
- **渲染优化**：视锥体裁剪、遮挡剔除

### 9.2 数据传输优化
- **数据压缩**：3D模型和纹理数据压缩
- **增量更新**：只传输变更的数据
- **CDN加速**：静态资源CDN分发
- **缓存策略**：客户端和服务端缓存

### 9.3 用户体验优化
- **渐进式加载**：优先加载关键内容
- **预加载策略**：预测用户行为，提前加载资源
- **响应式设计**：适配不同设备和屏幕尺寸
- **交互优化**：流畅的3D操作体验

## 10. 集成接口设计

### 10.1 项目管理集成
- **项目信息同步**：获取项目基础信息和客户需求
- **方案状态同步**：设计进度与项目进度同步
- **成本数据传递**：方案成本数据传递给项目管理
- **变更通知**：方案变更通知项目管理

### 10.2 工单管理集成
- **安装清单生成**：根据方案生成工单安装清单
- **施工图纸提供**：提供详细的施工图纸和说明
- **技术参数传递**：设备技术参数传递给工单
- **安装指导**：提供设备安装位置和参数指导

### 10.3 客户管理集成
- **客户信息获取**：获取客户基础信息和偏好
- **方案分享**：向客户分享设计方案
- **反馈收集**：收集客户对方案的反馈意见
- **满意度评价**：客户对设计方案的满意度评价

---

**文档版本**：v1.0  
**编写日期**：2025-07-07  
**编写人员**：AI系统架构师  
**审核状态**：待审核  

**项目成果**：智能家装管理平台智能家居设计模块完整功能设计方案，涵盖3D可视化设计、场景配置、设备选型、计算工具等核心功能，为智能家居方案设计提供专业的工具平台和技术支撑。