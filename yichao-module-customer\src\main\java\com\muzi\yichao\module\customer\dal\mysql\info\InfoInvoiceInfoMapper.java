package com.muzi.yichao.module.customer.dal.mysql.info;

import java.util.*;

import com.muzi.yichao.framework.common.pojo.PageResult;
import com.muzi.yichao.framework.common.pojo.PageParam;
import com.muzi.yichao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.muzi.yichao.framework.mybatis.core.mapper.BaseMapperX;
import com.muzi.yichao.module.customer.dal.dataobject.info.InfoInvoiceInfoDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 客户发票信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InfoInvoiceInfoMapper extends BaseMapperX<InfoInvoiceInfoDO> {

    default PageResult<InfoInvoiceInfoDO> selectPage(PageParam reqVO, Long customerId) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InfoInvoiceInfoDO>()
            .eq(InfoInvoiceInfoDO::getCustomerId, customerId)
            .orderByDesc(InfoInvoiceInfoDO::getId));
    }

    default int deleteByCustomerId(Long customerId) {
        return delete(InfoInvoiceInfoDO::getCustomerId, customerId);
    }

	default int deleteByCustomerIds(List<Long> customerIds) {
	    return deleteBatch(InfoInvoiceInfoDO::getCustomerId, customerIds);
	}

}
