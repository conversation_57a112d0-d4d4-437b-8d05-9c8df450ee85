package com.muzi.yichao.module.customer.dal.dataobject.info;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.muzi.yichao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 客户状态变更记录 DO
 *
 * <AUTHOR>
 */
@TableName("customer_info_status_log")
@KeySequence("customer_info_status_log_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InfoStatusLogDO extends BaseDO {

    /**
     * 记录ID
     */
    @TableId
    private Long id;
    /**
     * 客户ID
     */
    private Long customerId;
    /**
     * 原状态
     */
    private Integer oldStatus;
    /**
     * 新状态
     */
    private Integer newStatus;
    /**
     * 变更原因
     */
    private String changeReason;
    /**
     * 操作人员ID
     */
    private Long operatorId;
    /**
     * 操作人员姓名
     */
    private String operatorName;
    /**
     * 审批状态：0-无需审批，1-待审批，2-已通过，3-已拒绝
     */
    private Integer approvalStatus;
    /**
     * 审批人ID
     */
    private Long approvalUserId;
    /**
     * 审批时间
     */
    private LocalDateTime approvalTime;
    /**
     * 审批备注
     */
    private String approvalRemark;

}