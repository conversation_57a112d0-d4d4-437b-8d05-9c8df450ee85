package com.muzi.yichao.module.infra.dal.mysql.demo;

import java.util.*;

import com.muzi.yichao.framework.common.pojo.PageResult;
import com.muzi.yichao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.muzi.yichao.framework.mybatis.core.mapper.BaseMapperX;
import com.muzi.yichao.module.infra.dal.dataobject.demo.InfraStudentDO;
import org.apache.ibatis.annotations.Mapper;
import com.muzi.yichao.module.infra.controller.admin.demo.vo.*;

/**
 * 学生 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InfraStudentMapper extends BaseMapperX<InfraStudentDO> {

    default PageResult<InfraStudentDO> selectPage(InfraStudentPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InfraStudentDO>()
                .likeIfPresent(InfraStudentDO::getName, reqVO.getName())
                .eqIfPresent(InfraStudentDO::getBirthday, reqVO.getBirthday())
                .eqIfPresent(InfraStudentDO::getSex, reqVO.getSex())
                .eqIfPresent(InfraStudentDO::getEnabled, reqVO.getEnabled())
                .betweenIfPresent(InfraStudentDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(InfraStudentDO::getId));
    }

}