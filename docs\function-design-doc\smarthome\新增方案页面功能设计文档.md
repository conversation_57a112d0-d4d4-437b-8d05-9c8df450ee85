# SaaS智能家装CRM系统 - 新增方案页面功能设计文档

## 1. 页面概述

### 1.1 页面目的
新增方案页面用于创建新的智能家居设计方案，提供方案基本信息录入、客户关联、需求匹配和初始配置功能。该页面是方案设计流程的起点，为后续的详细设计和配置奠定基础。

### 1.2 业务价值
- 标准化方案创建流程，确保方案信息的完整性
- 建立方案与客户需求的关联关系，提升方案针对性
- 提供方案初始配置功能，加速方案设计进程
- 为方案管理和跟踪提供基础数据支持

### 1.3 页面位置
- **所属模块**: 智能家居模块
- **业务流程位置**: 需求收集 → **方案创建** → 方案设计
- **关联页面**: 
  - 方案列表页面（入口页面）
  - 客户需求页面（需求数据来源）
  - 模板方案页面（模板应用）

## 2. 新增方案页面操作流程图

```mermaid
flowchart TD
    A[用户点击新增方案] --> B[跳转新增方案页面]
    B --> C[权限验证]
    C --> D{权限验证通过?}
    D -->|否| E[显示权限不足提示]
    D -->|是| F[加载页面表单]

    F --> G[显示方案基本信息区]
    F --> H[显示客户关联区]
    F --> I[显示需求匹配区]
    F --> J[显示初始配置区]

    G --> K[方案名称输入]
    G --> L[方案类型选择]
    G --> M[设计师分配]
    G --> N[预算设置]

    H --> O[客户搜索选择]
    O --> P[加载客户信息]
    P --> Q[显示客户详情]
    Q --> R[加载客户需求]

    I --> S[显示需求列表]
    S --> T[需求关联选择]
    T --> U[需求优先级设置]

    J --> V[方案模板选择]
    J --> W[基础配置设置]
    J --> X[时间计划设置]

    K --> Y[表单验证]
    L --> Y
    M --> Y
    N --> Y
    O --> Y

    Y --> Z{验证通过?}
    Z -->|否| AA[显示错误提示]
    Z -->|是| BB[保存方案信息]

    BB --> CC[生成方案编号]
    CC --> DD[创建方案档案]
    DD --> EE[关联客户需求]
    EE --> FF[初始化方案配置]

    FF --> GG[保存成功提示]
    GG --> HH[跳转方案列表]

    style A fill:#e1f5fe
    style D fill:#fff3e0
    style E fill:#ffebee
    style Z fill:#f3e5f5
    style BB fill:#e8f5e8
```

### 流程说明
新增方案页面的操作流程包含以下核心环节：

1. **页面访问与权限验证**：从方案列表页面跳转，进行权限验证
2. **方案基本信息录入**：录入方案名称、类型、设计师和预算等基本信息
3. **客户关联与需求匹配**：选择关联客户并匹配相关需求
4. **初始配置设置**：选择方案模板和进行基础配置
5. **数据保存与跳转**：验证数据后保存方案并跳转回列表页面

## 3. 详细功能设计

### 3.1 方案基本信息区

#### 3.1.1 方案信息录入
**功能描述**: 录入方案的基本信息

**录入字段**:
- **方案名称**: 方案的名称，必填字段，支持中英文
- **方案描述**: 方案的简要描述，可选字段
- **方案类型**: 下拉选择（全屋智能/局部改造/新房装修/旧房改造）
- **方案标签**: 多选标签，用于方案分类和检索
- **预计预算**: 方案的预算范围，支持区间设置
- **预计工期**: 方案实施的预计工期，单位天

#### 3.1.2 人员分配设置
**功能描述**: 分配方案相关的负责人员

**人员分配**:
- **主设计师**: 方案的主要负责设计师，必填
- **协助设计师**: 协助设计的设计师，可选
- **项目经理**: 项目实施的项目经理，可选
- **销售顾问**: 负责客户沟通的销售顾问

### 3.2 客户关联区

#### 3.2.1 客户搜索选择
**功能描述**: 搜索和选择关联的客户

**搜索功能**:
- **客户姓名搜索**: 支持客户姓名的模糊搜索
- **联系方式搜索**: 支持手机号码的搜索
- **客户编号搜索**: 支持客户编号的精确搜索
- **下拉选择**: 提供客户列表的下拉选择

#### 3.2.2 客户信息展示
**功能描述**: 展示选中客户的基本信息

**展示信息**:
- **客户姓名**: 客户的真实姓名
- **联系方式**: 客户的联系电话
- **房屋地址**: 客户的房屋地址
- **房屋信息**: 房屋面积、类型、装修状态
- **历史方案**: 该客户的历史方案记录

### 3.3 需求匹配区

#### 3.3.1 需求列表展示
**功能描述**: 展示客户的智能家居需求

**需求信息**:
- **需求分类**: 安防、照明、家电、环境、影音等分类
- **需求描述**: 客户的具体需求描述
- **优先级**: 需求的重要程度
- **预算分配**: 各需求的预算分配
- **实现状态**: 需求的实现状态

#### 3.3.2 需求关联设置
**功能描述**: 设置方案与需求的关联关系

**关联设置**:
- **需求选择**: 选择方案要实现的需求
- **实现程度**: 设置需求的实现程度（完全实现/部分实现/暂不实现）
- **实施阶段**: 设置需求在哪个阶段实现（一期/二期/三期）
- **备注说明**: 对需求实现的备注说明

### 3.4 初始配置区

#### 3.4.1 方案模板选择
**功能描述**: 选择适合的方案模板作为基础

**模板选择**:
- **模板分类**: 按房型、预算、功能等分类展示模板
- **模板预览**: 预览模板的基本配置和效果
- **模板应用**: 选择模板并应用到当前方案
- **自定义配置**: 选择不使用模板，完全自定义配置

#### 3.4.2 基础配置设置
**功能描述**: 设置方案的基础配置参数

**配置参数**:
- **控制方式**: 选择智能控制的主要方式（APP/语音/面板/传感器）
- **网络架构**: 选择网络架构方案（有线/无线/混合）
- **品牌偏好**: 设置设备品牌的偏好选择
- **扩展性**: 设置系统的扩展性要求

#### 3.4.3 时间计划设置
**功能描述**: 设置方案实施的时间计划

**时间计划**:
- **开始时间**: 方案实施的计划开始时间
- **完成时间**: 方案实施的计划完成时间
- **里程碑**: 设置关键的时间节点和里程碑
- **缓冲时间**: 设置时间缓冲和风险预留

### 3.5 表单操作区

#### 3.5.1 数据验证功能
**功能描述**: 验证录入数据的完整性和准确性

**验证规则**:
- **必填字段验证**: 检查必填字段是否完整
- **格式验证**: 验证数据格式的正确性
- **逻辑验证**: 验证数据逻辑的合理性
- **重复验证**: 检查是否存在重复的方案

#### 3.5.2 保存操作功能
**功能描述**: 提供方案数据的保存操作

**保存选项**:
- **保存草稿**: 保存当前录入信息为草稿状态
- **保存并继续**: 保存信息并继续编辑配置
- **保存并返回**: 保存信息并返回方案列表
- **取消操作**: 取消当前操作并返回列表

## 4. 用户界面设计

### 4.1 页面布局设计
- **顶部导航区**: 页面标题和返回按钮
- **表单录入区**: 分区域的表单录入界面
- **信息展示区**: 客户信息和需求信息展示
- **底部操作区**: 保存、取消等操作按钮

### 4.2 表单设计规范
- **分步骤录入**: 按照信息类型分步骤录入
- **字段分组**: 相关字段进行分组显示
- **必填标识**: 必填字段使用红色星号标识
- **实时验证**: 输入过程中进行实时验证

### 4.3 交互设计规范
- **智能提示**: 根据输入内容提供智能建议
- **自动填充**: 选择客户后自动填充相关信息
- **进度指示**: 显示表单填写的完成进度
- **错误提示**: 清晰的错误信息和修正建议

## 5. 数据流向

### 5.1 数据输入
- **来源**: 用户手动录入 + 客户需求页面数据 + 模板方案数据
- **格式**: 结构化的方案基础数据

### 5.2 数据输出
- **流向**: 方案列表页面（新增的方案记录）
- **存储**: 方案数据库表，建立方案档案

### 5.3 业务关联
- **前置页面**: 方案列表页面（入口）
- **数据来源**: 客户需求页面、模板方案页面
- **后续流程**: 方案详细设计和配置

## 6. 权限控制

### 6.1 访问权限
- **设计师**: 可以创建自己负责的方案
- **项目经理**: 可以创建和分配方案
- **销售人员**: 可以为客户创建方案

### 6.2 操作权限
- **创建权限**: 方案创建权限控制
- **客户关联**: 客户数据访问权限
- **模板使用**: 方案模板使用权限
- **人员分配**: 人员分配权限控制

## 7. 异常处理

### 7.1 数据异常
- **保存失败**: 数据保存失败的重试机制
- **客户数据**: 客户数据加载失败的处理
- **模板数据**: 模板数据加载失败的处理

### 7.2 操作异常
- **权限不足**: 权限不足的友好提示
- **网络异常**: 网络连接异常的处理
- **会话超时**: 会话超时的重新登录引导

---

**文档版本**: v1.0
**编写日期**: 2025-07-14
**编写人员**: AI系统架构师
**审核状态**: 待审核
**对应图片**: 方案列表-新增方案页面.png
