# SaaS智能家装CRM系统 - 客户新增模块功能需求文档

## 文档信息
- **文档版本**: v1.0
- **创建时间**: 2025-07-17
- **编写人员**: AI系统架构师
- **审核状态**: 待审核
- **文档类型**: 功能需求规格说明书

---

## 1. 模块概述

### 1.1 模块定位
客户新增模块是SaaS智能家装CRM系统的核心基础模块，负责为企业建立完整的客户档案管理体系。该模块将原有的会员卡办理功能进行全面升级，实现客户信息的标准化录入、多维度分类管理和全生命周期跟踪。

### 1.2 业务价值
- **标准化管理**：建立统一的客户信息录入标准，确保数据完整性和一致性
- **多租户支持**：支持多门店客户信息统一管理，实现连锁经营的数据协同
- **精准营销**：提供灵活的客户分类和标签体系，支持精准营销和客户分析
- **推荐网络**：建立完善的客户推荐关系链，支持转介绍业务发展
- **业务协同**：与财务、设计、施工等业务模块深度集成，形成业务闭环

### 1.3 技术架构
- **分层架构**：采用Controller-Service-Mapper-DO四层架构
- **多租户支持**：基于TenantBaseDO实现数据隔离
- **权限控制**：集成Spring Security实现细粒度权限管控
- **数据验证**：多层次数据验证确保数据质量
- **事务管理**：确保数据操作的原子性和一致性

## 2. 功能规格说明

### 2.1 客户基本信息管理

**功能描述**：提供客户基础身份信息的录入和管理功能

**必填字段规范**：
- **门店信息**：自动获取当前用户所属门店，管理员可选择其他门店
- **客户类别**：
  - 服务商客户：提供安装、维修等服务的合作伙伴
  - 品牌家装客户：与品牌方合作的装修客户
  - 个人客户：普通个人消费者
  - 企业客户：企业级客户
- **来源渠道**：
  - 设计师推荐：通过设计师介绍获得
  - 大众点评：通过点评平台了解
  - 网络推广：通过网络广告获得
  - 朋友介绍：通过朋友推荐
  - 门店到访：直接到店咨询
  - 其他渠道：其他获客方式
- **客户姓名**：支持中英文，2-20字符，不允许特殊字符
- **手机号码**：11位手机号，系统内唯一性验证，支持国际格式

**选填字段规范**：
- **微信号**：支持微信号、手机号、QQ号格式
- **个人信息**：性别、生日、证件类型、证件号码、公司名称
- **备注信息**：最多500字符的补充说明

### 2.2 服务团队配置

**功能描述**：为客户配置专属的服务团队和责任人

**跟进人设置**：
- 默认选择当前登录用户
- 支持从当前门店员工中选择
- 提供员工姓名搜索功能
- 支持后续变更跟进人

**设计师分配**：
- 可选择门店设计师列表
- 支持设计师姓名搜索
- 允许暂不分配，后续指定

### 2.3 客户推荐关系管理

**功能描述**：建立和维护客户推荐关系网络

**推荐人选择机制**：
- 弹窗展示现有客户列表
- 支持按姓名、手机号搜索
- 建立双向推荐关系记录
- 用于推荐奖励计算和关系分析

**推荐关系验证**：
- 防止循环推荐
- 推荐人状态验证
- 推荐关系有效性检查

### 2.4 账户信息设置

**功能描述**：设置客户登录账户和有效期管理

**账户配置项**：
- **发卡日期**：默认当前日期，支持修改
- **到期日期**：支持永久有效或自定义到期时间
- **登录密码**：最少8位，包含字母和数字，BCrypt加密存储
- **密码确认**：确保密码输入一致性

### 2.5 财务信息管理

**功能描述**：设置客户的初始财务状态

**财务配置项**：
- **收款账户**：选择门店收款账户，关联财务结算
- **初始存款**：客户初始充值金额，支持小数点后两位
- **红包金额**：营销活动赠送金额，有使用期限
- **初始积分**：客户初始积分，整数验证

### 2.6 收货地址管理

**功能描述**：管理客户的收货地址信息

**地址信息字段**：
- **收货人信息**：姓名、联系电话
- **地址信息**：省市区三级联动选择、详细地址、邮政编码
- **地址标签**：家庭住址、公司地址、其他类型
- **默认设置**：支持设置默认收货地址

**地址管理功能**：
- 支持添加多个收货地址
- 地址信息编辑和删除
- 默认地址设置和变更
- 地址信息格式验证

## 3. 业务流程设计

### 3.1 客户新增主流程

```
1. 权限验证 → 2. 表单初始化 → 3. 分步信息录入 → 4. 数据验证 → 5. 档案创建 → 6. 成功反馈
```

**详细步骤说明**：
1. **权限验证**：检查用户是否具有客户新增权限
2. **表单初始化**：自动填充门店等默认信息
3. **分步信息录入**：按照设计的步骤逐步收集信息
4. **数据验证**：进行格式验证、唯一性验证、业务规则验证
5. **档案创建**：生成客户编号，保存客户档案
6. **成功反馈**：显示创建结果，提供后续操作选项

### 3.2 数据验证流程

**实时验证**：
- 字段失焦时进行格式验证
- 手机号唯一性实时检查
- 密码强度实时提示

**提交前验证**：
- 必填字段完整性检查
- 数据格式规范性验证
- 业务规则一致性验证

### 3.3 异常处理机制

**输入异常处理**：
- 格式错误：提供明确的格式要求提示
- 重复数据：提示手机号已存在，建议查找现有客户
- 网络异常：提供重试机制和离线保存

**系统异常处理**：
- 保存失败：提示失败原因，保留用户输入
- 服务异常：提供友好的错误提示和联系方式

## 4. 接口定义

### 4.1 客户管理接口

```java
// 创建客户
POST /customer/customer/create
// 更新客户信息  
PUT /customer/customer/update
// 获取客户详情
GET /customer/customer/get/{id}
// 客户分页查询
GET /customer/customer/page
// 删除客户
DELETE /customer/customer/delete/{id}
```

### 4.2 地址管理接口

```java
// 添加收货地址
POST /customer/address/create
// 更新收货地址
PUT /customer/address/update
// 删除收货地址
DELETE /customer/address/delete/{id}
// 设置默认地址
PUT /customer/address/set-default/{id}
// 获取客户地址列表
GET /customer/address/list/{customerId}
```

### 4.3 推荐关系接口

```java
// 建立推荐关系
POST /customer/referral/create
// 获取推荐关系
GET /customer/referral/get/{customerId}
// 推荐关系统计
GET /customer/referral/statistics
```

## 5. 权限控制

### 5.1 操作权限

- **系统管理员**：可为任意门店添加客户，查看所有客户信息
- **门店经理**：只能为本门店添加客户，管理本门店客户
- **销售人员**：可添加客户，默认自己为跟进人，主要操作自己跟进的客户
- **客服人员**：只读权限，可查看客户信息但不能修改

### 5.2 数据权限

- **多租户隔离**：基于tenant_id实现数据隔离
- **门店数据隔离**：只能查看和操作本门店客户数据
- **跟进人权限**：销售人员主要操作自己跟进的客户

## 6. 集成点设计

### 6.1 与系统模块集成

- **用户管理模块**：获取员工信息、门店信息
- **权限管理模块**：权限验证、数据权限控制
- **字典管理模块**：客户类别、来源渠道等字典数据

### 6.2 与业务模块集成

- **财务模块**：收款账户、客户账户初始化
- **订单模块**：客户信息关联、收货地址使用
- **营销模块**：客户标签、推荐关系、积分管理

## 7. 数据模型设计

### 7.1 核心实体

- **客户主实体**：包含客户基本信息、分类信息、服务信息、账户信息、财务信息
- **收货地址实体**：包含地址信息、联系人信息、地址标签
- **推荐关系实体**：包含推荐人、被推荐人、推荐时间、奖励信息

### 7.2 关系设计

- **客户与地址**：一对多关系，一个客户可以有多个收货地址
- **客户与推荐关系**：多对多关系，通过推荐关系表建立联系
- **客户与员工**：多对一关系，跟进人和设计师关联

## 8. 性能要求

### 8.1 响应时间要求

- **页面加载时间**：首次加载不超过2秒
- **数据保存时间**：客户信息保存不超过3秒
- **查询响应时间**：客户列表查询不超过1秒

### 8.2 并发性能要求

- **支持并发用户数**：单门店支持50个并发用户同时操作
- **数据一致性**：确保高并发下的数据一致性
- **系统稳定性**：7×24小时稳定运行

## 9. 安全要求

### 9.1 数据安全

- **密码加密**：使用BCrypt算法加密存储客户密码
- **敏感信息保护**：身份证号、手机号等敏感信息加密存储
- **数据备份**：定期备份客户数据，确保数据安全

### 9.2 访问安全

- **权限验证**：所有操作都需要进行权限验证
- **操作日志**：记录所有客户信息的操作日志
- **数据审计**：提供数据变更审计功能

## 10. 客户新增操作流程图

```mermaid
flowchart TD
    A[用户点击新增客户] --> B[权限验证]
    B --> C{权限验证通过?}
    C -->|否| D[显示权限不足提示]
    C -->|是| E[显示客户新增表单]

    E --> F[第一步：基本信息录入]
    F --> G[门店信息自动填充]
    F --> H[客户类别选择]
    F --> I[来源渠道选择]
    F --> J[姓名输入]
    F --> K[手机号输入]
    F --> L[微信号输入]

    J --> M[姓名格式验证]
    K --> N[手机号格式验证]
    K --> O[手机号唯一性验证]

    M --> P{验证通过?}
    N --> P
    O --> P
    P -->|否| Q[显示错误提示]
    P -->|是| R[第二步：服务信息录入]

    R --> S[跟进人选择]
    R --> T[设计师选择]
    S --> U[默认当前用户]
    T --> V[可选择或留空]

    U --> W[第三步：个人信息录入]
    V --> W
    W --> X[性别选择]
    W --> Y[生日选择]
    W --> Z[证件信息录入]
    W --> AA[公司信息录入]

    AA --> BB[第四步：推荐关系设置]
    BB --> CC[推荐人选择]
    CC --> DD{是否有推荐人?}
    DD -->|是| EE[弹出客户选择窗口]
    DD -->|否| FF[跳过推荐人设置]

    EE --> GG[搜索现有客户]
    GG --> HH[选择推荐人]
    HH --> II[建立推荐关系]

    FF --> JJ[第五步：账户信息设置]
    II --> JJ
    JJ --> KK[发卡日期设置]
    JJ --> LL[到期日期设置]
    JJ --> MM[密码设置]
    JJ --> NN[确认密码]

    MM --> OO[密码强度验证]
    NN --> PP[密码一致性验证]
    OO --> QQ{验证通过?}
    PP --> QQ
    QQ -->|否| RR[显示密码错误提示]
    QQ -->|是| SS[第六步：财务信息设置]

    SS --> TT[收款账户选择]
    SS --> UU[初始存款设置]
    SS --> VV[红包金额设置]
    SS --> WW[初始积分设置]

    TT --> XX[第七步：特殊标识设置]
    UU --> XX
    VV --> XX
    WW --> XX
    XX --> YY[安装师傅标识]
    XX --> ZZ[客户类型选择]
    XX --> AAA[APP账号设置]
    XX --> BBB[发票需求设置]

    YY --> CCC[第八步：收货地址管理]
    ZZ --> CCC
    AAA --> CCC
    BBB --> CCC
    CCC --> DDD[添加收货地址]
    DDD --> EEE[地址信息录入]
    EEE --> FFF[地址验证]
    FFF --> GGG{地址验证通过?}
    GGG -->|否| HHH[显示地址错误提示]
    GGG -->|是| III[保存地址信息]

    III --> JJJ[第九步：信息确认]
    JJJ --> KKK[显示信息预览]
    KKK --> LLL[用户确认信息]
    LLL --> MMM{信息确认无误?}
    MMM -->|否| NNN[返回修改]
    MMM -->|是| OOO[提交客户信息]

    OOO --> PPP[数据验证]
    PPP --> QQQ[生成客户编号]
    QQQ --> RRR[创建客户账户]
    RRR --> SSS[保存客户档案]
    SSS --> TTT[建立推荐关系]
    TTT --> UUU[初始化财务账户]
    UUU --> VVV{保存成功?}
    VVV -->|否| WWW[显示保存失败提示]
    VVV -->|是| XXX[显示成功提示]
    XXX --> YYY[跳转客户列表]

    style A fill:#e1f5fe
    style C fill:#fff3e0
    style D fill:#ffebee
    style P fill:#fff3e0
    style Q fill:#ffebee
    style QQ fill:#fff3e0
    style RR fill:#ffebee
    style GGG fill:#fff3e0
    style HHH fill:#ffebee
    style MMM fill:#fff3e0
    style VVV fill:#fff3e0
    style WWW fill:#ffebee
    style XXX fill:#e8f5e8
```

## 11. 数据字典设计

### 11.1 客户类别枚举

| 值 | 标签 | 描述 |
|---|---|---|
| 1 | 服务商客户 | 提供安装、维修等服务的合作伙伴 |
| 2 | 品牌家装客户 | 与品牌方合作的装修客户 |
| 3 | 个人客户 | 普通个人消费者 |
| 4 | 企业客户 | 企业级客户 |

### 11.2 来源渠道枚举

| 值 | 标签 | 描述 |
|---|---|---|
| 1 | 设计师推荐 | 通过设计师介绍获得 |
| 2 | 大众点评 | 通过点评平台了解 |
| 3 | 网络推广 | 通过网络广告获得 |
| 4 | 朋友介绍 | 通过朋友推荐 |
| 5 | 门店到访 | 直接到店咨询 |
| 6 | 其他渠道 | 其他获客方式 |

### 11.3 客户类型枚举

| 值 | 标签 | 描述 |
|---|---|---|
| 1 | 前装客户 | 新房装修客户 |
| 2 | 后装客户 | 已装修房屋的客户 |
| 3 | 改造客户 | 旧房改造客户 |

### 11.4 性别枚举

| 值 | 标签 | 描述 |
|---|---|---|
| 0 | 未知 | 未设置性别 |
| 1 | 男 | 男性 |
| 2 | 女 | 女性 |

### 11.5 证件类型枚举

| 值 | 标签 | 描述 |
|---|---|---|
| 1 | 身份证 | 中华人民共和国居民身份证 |
| 2 | 护照 | 护照 |
| 3 | 其他 | 其他证件类型 |

### 11.6 地址标签枚举

| 值 | 标签 | 描述 |
|---|---|---|
| 1 | 家 | 家庭住址 |
| 2 | 公司 | 公司地址 |
| 3 | 其他 | 其他类型地址 |

## 12. 客户编号生成规则

### 12.1 编号格式

**格式规范**：`门店代码(2位) + 年月(4位) + 流水号(3位)`

**示例**：
- SH202507001：上海门店2025年7月第1个客户
- BJ202507002：北京门店2025年7月第2个客户
- GZ202508001：广州门店2025年8月第1个客户

### 12.2 生成逻辑

```java
/**
 * 客户编号生成逻辑
 * @param storeCode 门店代码
 * @return 客户编号
 */
public String generateCustomerNo(String storeCode) {
    // 获取当前年月
    String yearMonth = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM"));

    // 获取当前门店当月的客户数量
    int count = getCustomerCountByStoreAndMonth(storeCode, yearMonth);

    // 生成流水号（3位，不足补0）
    String sequence = String.format("%03d", count + 1);

    // 组合客户编号
    return storeCode + yearMonth + sequence;
}
```

### 12.3 唯一性保证

- 数据库层面：customer_no字段设置唯一索引
- 应用层面：生成时加锁，确保并发安全
- 重试机制：生成失败时自动重试

## 13. 验证规则详细说明

### 13.1 手机号验证

**格式要求**：
- 11位数字
- 以1开头
- 第二位为3、4、5、6、7、8、9中的一位

**验证正则**：`^1[3-9]\d{9}$`

**唯一性验证**：
- 在同一租户内手机号必须唯一
- 支持跨租户手机号重复

### 13.2 密码强度验证

**强度要求**：
- 最少8位字符
- 至少包含一个字母
- 至少包含一个数字
- 可包含特殊字符

**验证正则**：`^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$`

### 13.3 身份证号验证

**格式要求**：
- 18位字符
- 前17位为数字
- 最后一位为数字或X

**验证逻辑**：
- 格式验证
- 校验码验证
- 出生日期合理性验证

### 13.4 微信号验证

**格式要求**：
- 6-20位字符
- 字母、数字、下划线、减号
- 不能以数字开头

**验证正则**：`^[a-zA-Z][-_a-zA-Z0-9]{5,19}$`

## 14. 测试用例设计

### 14.1 功能测试用例

#### 14.1.1 客户新增成功场景

**测试步骤**：
1. 登录系统，进入客户新增页面
2. 填写所有必填字段
3. 点击保存按钮
4. 验证客户创建成功

**预期结果**：
- 客户信息保存成功
- 生成唯一客户编号
- 跳转到客户列表页面
- 显示成功提示信息

#### 14.1.2 手机号重复验证

**测试步骤**：
1. 输入已存在的手机号
2. 点击保存按钮

**预期结果**：
- 显示手机号已存在的错误提示
- 不允许保存客户信息

#### 14.1.3 必填字段验证

**测试步骤**：
1. 不填写必填字段
2. 点击保存按钮

**预期结果**：
- 显示必填字段提示
- 不允许保存客户信息

### 14.2 性能测试用例

#### 14.2.1 并发创建客户

**测试场景**：50个用户同时创建客户

**性能指标**：
- 响应时间 < 3秒
- 成功率 > 99%
- 无数据冲突

#### 14.2.2 大数据量查询

**测试场景**：10万客户数据的查询性能

**性能指标**：
- 分页查询响应时间 < 1秒
- 模糊搜索响应时间 < 2秒

### 14.3 安全测试用例

#### 14.3.1 权限验证测试

**测试场景**：无权限用户尝试创建客户

**预期结果**：
- 返回权限不足错误
- 不允许执行操作

#### 14.3.2 SQL注入测试

**测试场景**：在输入框中输入SQL注入代码

**预期结果**：
- 输入被正确转义
- 不会执行恶意SQL

---

**文档版本**: v1.0
**最后更新**: 2025-07-17
**文档状态**: 完成
