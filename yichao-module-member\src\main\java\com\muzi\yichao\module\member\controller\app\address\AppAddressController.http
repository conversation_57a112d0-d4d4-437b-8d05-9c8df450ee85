### 请求 /create 接口 => 成功
POST {{appApi}}//member/address/create
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{appToken}}

{
  "name": "yunai",
  "mobile": "15601691300",
  "areaId": "610632",
  "postCode": "200000",
  "detailAddress": "muzi 233 号 666 室",
  "defaulted": true
}

### 请求 /update 接口 => 成功
PUT {{appApi}}//member/address/update
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{appToken}}

{
  "id": "1",
  "name": "yunai888",
  "mobile": "15601691300",
  "areaId": "610632",
  "postCode": "200000",
  "detailAddress": "muzi 233 号 666 室",
  "defaulted": false
}

### 请求 /delete 接口 => 成功
DELETE {{appApi}}//member/address/delete?id=2
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{appToken}}

### 请求 /get 接口 => 成功
GET {{appApi}}//member/address/get?id=1
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{appToken}}

### 请求 /get-default 接口 => 成功
GET {{appApi}}//member/address/get-default
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{appToken}}

### 请求 /list 接口 => 成功
GET {{appApi}}//member/address/list
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{appToken}}
