package com.muzi.yichao.module.customer.dal.dataobject.info;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.muzi.yichao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 客户账户操作日志 DO
 *
 * <AUTHOR>
 */
@TableName("customer_info_account_log")
@KeySequence("customer_info_account_log_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InfoAccountLogDO extends BaseDO {

    /**
     * 日志ID
     */
    @TableId
    private Long id;
    /**
     * 客户ID
     */
    private Long customerId;
    /**
     * 操作类型：1-延期，2-重置密码，3-锁定，4-解锁，5-登录
     *
     * 枚举 {@link TODO account_operation_type 对应的类}
     */
    private Integer operationType;
    /**
     * 操作详情（JSON格式）
     */
    private String operationDetail;
    /**
     * 操作人员ID
     */
    private Long operatorId;
    /**
     * 操作人员姓名
     */
    private String operatorName;
    /**
     * 操作IP
     */
    private String operationIp;
    /**
     * 操作结果：1-成功，2-失败
     */
    private Integer operationResult;
    /**
     * 操作备注
     */
    private String operationRemark;

}