package com.muzi.yichao.module.infra.dal.mysql.demo;

import java.util.*;

import com.muzi.yichao.framework.common.pojo.PageResult;
import com.muzi.yichao.framework.common.pojo.PageParam;
import com.muzi.yichao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.muzi.yichao.framework.mybatis.core.mapper.BaseMapperX;
import com.muzi.yichao.module.infra.dal.dataobject.demo.InfraStudentContactDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 学生联系人 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InfraStudentContactMapper extends BaseMapperX<InfraStudentContactDO> {

    default PageResult<InfraStudentContactDO> selectPage(PageParam reqVO, Long studentId) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InfraStudentContactDO>()
            .eq(InfraStudentContactDO::getStudentId, studentId)
            .orderByDesc(InfraStudentContactDO::getId));
    }

    default int deleteByStudentId(Long studentId) {
        return delete(InfraStudentContactDO::getStudentId, studentId);
    }

}