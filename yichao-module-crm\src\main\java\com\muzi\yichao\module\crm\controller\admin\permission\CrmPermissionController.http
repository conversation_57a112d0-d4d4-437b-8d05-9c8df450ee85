### 请求 /add
POST {{baseUrl}}/crm/permission/create
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenantId}}

{
  "userId": 1,
  "bizType": 2,
  "bizId": 2,
  "level": 1
}

### 请求 /update
PUT {{baseUrl}}/crm/permission/update
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenantId}}

{
  "userId": 1,
  "bizType": 2,
  "bizId": 2,
  "level": 1,
  "id": 1
}

### 请求 /delete
DELETE {{baseUrl}}/crm/permission/delete?bizType=2&bizId=1&id=1
Authorization: Bearer {{token}}
tenant-id: {{adminTenantId}}

