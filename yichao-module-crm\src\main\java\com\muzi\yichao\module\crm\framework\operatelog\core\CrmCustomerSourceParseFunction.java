package com.muzi.yichao.module.crm.framework.operatelog.core;

import cn.hutool.core.util.StrUtil;
import com.muzi.yichao.framework.dict.core.DictFrameworkUtils;
import com.mzt.logapi.service.IParseFunction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.muzi.yichao.module.crm.enums.DictTypeConstants.CRM_CUSTOMER_SOURCE;

/**
 * CRM 客户来源的 {@link IParseFunction} 实现类
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class CrmCustomerSourceParseFunction implements IParseFunction {

    public static final String NAME = "getCustomerSource";

    @Override
    public boolean executeBefore() {
        return true; // 先转换值后对比
    }

    @Override
    public String functionName() {
        return NAME;
    }

    @Override
    public String apply(Object value) {
        if (StrUtil.isEmptyIfStr(value)) {
            return "";
        }
        return DictFrameworkUtils.parseDictDataLabel(CRM_CUSTOMER_SOURCE, value.toString());
    }

}
