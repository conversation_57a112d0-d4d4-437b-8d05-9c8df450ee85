/*
 SaaS智能家装CRM系统 - 客户管理模块数据库设计
 
 模块名称：客户管理模块
 版本：v1.1
 创建时间：2025-07-17
 更新时间：2025-07-17
 数据库：MySQL 8.0+
 字符集：utf8mb4
 
 包含表：
 1. customer_info - 客户主表
 2. customer_info_address - 客户收货地址表
 3. customer_info_referral - 客户推荐关系表
 4. customer_info_status_log - 客户状态变更记录表
 5. customer_info_points_record - 客户积分记录表
 6. customer_info_account_log - 客户账户操作日志表
 7. customer_info_invoice_info - 客户发票信息表
 
 说明：
 - 所有表都支持多租户，继承租户ID字段
 - 使用逻辑删除，deleted字段标识删除状态
 - 包含审计字段：creator, create_time, updater, update_time
 - 遵循现有系统的命名规范和架构设计
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ========================================
-- 表结构创建
-- ========================================

-- ----------------------------
-- 客户主表
-- ----------------------------
DROP TABLE IF EXISTS `customer_info`;
CREATE TABLE `customer_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '客户ID',
  `customer_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户编号',
  `store_id` bigint NOT NULL COMMENT '门店ID',
  `category` tinyint NOT NULL COMMENT '客户类别：1-服务商客户，2-品牌家装客户，3-个人客户，4-企业客户',
  `source` tinyint NOT NULL COMMENT '来源渠道：1-设计师推荐，2-大众点评，3-网络推广，4-朋友介绍，5-门店到访，6-其他',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户姓名',
  `mobile` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '手机号码',
  `wechat` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '微信号',
  `sex` tinyint NULL DEFAULT 0 COMMENT '性别：0-未知，1-男，2-女',
  `birthday` date NULL DEFAULT NULL COMMENT '生日',
  `id_type` tinyint NULL DEFAULT NULL COMMENT '证件类型：1-身份证，2-护照，3-其他',
  `id_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '证件号码',
  `company_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '公司名称',
  `follower_user_id` bigint NOT NULL COMMENT '跟进人ID',
  `designer_user_id` bigint NULL DEFAULT NULL COMMENT '设计师ID',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '登录密码（加密）',
  `issue_date` date NOT NULL COMMENT '发卡日期',
  `expire_date` date NULL DEFAULT NULL COMMENT '到期日期',
  `account_id` bigint NOT NULL COMMENT '收款账户ID',
  `initial_deposit` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '初始存款',
  `bonus_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '红包金额',
  `initial_points` int NOT NULL DEFAULT 0 COMMENT '初始积分',
  `is_installer` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否安装师傅',
  `customer_type` tinyint NOT NULL COMMENT '客户类型：1-前装客户，2-后装客户，3-改造客户',
  `app_account` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'APP账号',
  `need_invoice` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否需要发票',
  `referrer_id` bigint NULL DEFAULT NULL COMMENT '推荐人ID',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注信息',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮箱地址',
  `position` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '职位',
  `customer_manager_id` bigint NULL DEFAULT NULL COMMENT '客户经理ID',
  `vip_level` tinyint NOT NULL DEFAULT 0 COMMENT 'VIP等级：0-普通，1-VIP1，2-VIP2，3-VIP3',
  `customer_tags` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '客户标签（JSON格式）',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-正常，2-冻结，3-黑名单，4-VIP',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_customer_no` (`customer_no`) USING BTREE COMMENT '客户编号唯一索引',
  UNIQUE KEY `uk_mobile` (`mobile`, `tenant_id`) USING BTREE COMMENT '手机号唯一索引',
  KEY `idx_store_id` (`store_id`) USING BTREE COMMENT '门店索引',
  KEY `idx_follower_user_id` (`follower_user_id`) USING BTREE COMMENT '跟进人索引',
  KEY `idx_referrer_id` (`referrer_id`) USING BTREE COMMENT '推荐人索引',
  KEY `idx_create_time` (`create_time`) USING BTREE COMMENT '创建时间索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 CHARACTER SET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户主表';

-- ----------------------------
-- 客户收货地址表
-- ----------------------------
DROP TABLE IF EXISTS `customer_info_address`;
CREATE TABLE `customer_info_address` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '地址ID',
  `customer_id` bigint NOT NULL COMMENT '客户ID',
  `receiver_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '收货人姓名',
  `receiver_mobile` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '收货人手机',
  `province_id` int NOT NULL COMMENT '省份ID',
  `city_id` int NOT NULL COMMENT '城市ID',
  `district_id` int NOT NULL COMMENT '区县ID',
  `detail_address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '详细地址',
  `postal_code` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮政编码',
  `address_label` tinyint NOT NULL DEFAULT 1 COMMENT '地址标签：1-家，2-公司，3-其他',
  `is_default` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否默认地址',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_customer_id` (`customer_id`) USING BTREE COMMENT '客户ID索引',
  KEY `idx_create_time` (`create_time`) USING BTREE COMMENT '创建时间索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 CHARACTER SET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户收货地址表';

-- ----------------------------
-- 客户推荐关系表
-- ----------------------------
DROP TABLE IF EXISTS `customer_info_referral`;
CREATE TABLE `customer_info_referral` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关系ID',
  `referrer_id` bigint NOT NULL COMMENT '推荐人ID',
  `referee_id` bigint NOT NULL COMMENT '被推荐人ID',
  `referral_time` datetime NOT NULL COMMENT '推荐时间',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态：1-有效，2-无效',
  `reward_amount` decimal(10,2) NULL DEFAULT NULL COMMENT '推荐奖励金额',
  `reward_status` tinyint NULL DEFAULT 0 COMMENT '奖励状态：0-未发放，1-已发放',
  `remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_referrer_referee` (`referrer_id`, `referee_id`) USING BTREE COMMENT '推荐关系唯一索引',
  KEY `idx_referrer_id` (`referrer_id`) USING BTREE COMMENT '推荐人索引',
  KEY `idx_referee_id` (`referee_id`) USING BTREE COMMENT '被推荐人索引',
  KEY `idx_referral_time` (`referral_time`) USING BTREE COMMENT '推荐时间索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 CHARACTER SET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户推荐关系表';

-- ----------------------------
-- 客户状态变更记录表
-- ----------------------------
DROP TABLE IF EXISTS `customer_info_status_log`;
CREATE TABLE `customer_info_status_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `customer_id` bigint NOT NULL COMMENT '客户ID',
  `old_status` tinyint NOT NULL COMMENT '原状态',
  `new_status` tinyint NOT NULL COMMENT '新状态',
  `change_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '变更原因',
  `operator_id` bigint NOT NULL COMMENT '操作人员ID',
  `operator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作人员姓名',
  `approval_status` tinyint NOT NULL DEFAULT 0 COMMENT '审批状态：0-无需审批，1-待审批，2-已通过，3-已拒绝',
  `approval_user_id` bigint NULL DEFAULT NULL COMMENT '审批人ID',
  `approval_time` datetime NULL DEFAULT NULL COMMENT '审批时间',
  `approval_remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '审批备注',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_customer_id` (`customer_id`) USING BTREE COMMENT '客户ID索引',
  KEY `idx_create_time` (`create_time`) USING BTREE COMMENT '创建时间索引',
  KEY `idx_operator_id` (`operator_id`) USING BTREE COMMENT '操作人员索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 CHARACTER SET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户状态变更记录表';

-- ----------------------------
-- 客户积分记录表
-- ----------------------------
DROP TABLE IF EXISTS `customer_info_points_record`;
CREATE TABLE `customer_info_points_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `customer_id` bigint NOT NULL COMMENT '客户ID',
  `record_type` tinyint NOT NULL COMMENT '记录类型：1-获得积分，2-消费积分，3-积分调整，4-积分过期',
  `points` int NOT NULL COMMENT '积分数量',
  `balance_before` int NOT NULL COMMENT '操作前积分余额',
  `balance_after` int NOT NULL COMMENT '操作后积分余额',
  `source_type` tinyint NULL DEFAULT NULL COMMENT '来源类型：1-消费获得，2-活动奖励，3-签到获得，4-推荐奖励，5-手动调整',
  `source_id` bigint NULL DEFAULT NULL COMMENT '来源ID（关联消费记录等）',
  `exchange_type` tinyint NULL DEFAULT NULL COMMENT '兑换类型：1-商品兑换，2-服务兑换，3-现金兑换',
  `exchange_id` bigint NULL DEFAULT NULL COMMENT '兑换ID',
  `expire_date` date NULL DEFAULT NULL COMMENT '到期日期',
  `operator_id` bigint NULL DEFAULT NULL COMMENT '操作人员ID',
  `operator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '操作人员姓名',
  `operation_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '操作原因',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_customer_id` (`customer_id`) USING BTREE COMMENT '客户ID索引',
  KEY `idx_record_type` (`record_type`) USING BTREE COMMENT '记录类型索引',
  KEY `idx_create_time` (`create_time`) USING BTREE COMMENT '创建时间索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 CHARACTER SET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户积分记录表';

-- ----------------------------
-- 客户账户操作日志表
-- ----------------------------
DROP TABLE IF EXISTS `customer_info_account_log`;
CREATE TABLE `customer_info_account_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `customer_id` bigint NOT NULL COMMENT '客户ID',
  `operation_type` tinyint NOT NULL COMMENT '操作类型：1-延期，2-重置密码，3-锁定，4-解锁，5-登录',
  `operation_detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '操作详情（JSON格式）',
  `operator_id` bigint NULL DEFAULT NULL COMMENT '操作人员ID',
  `operator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '操作人员姓名',
  `operation_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '操作IP',
  `operation_result` tinyint NOT NULL DEFAULT 1 COMMENT '操作结果：1-成功，2-失败',
  `operation_remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '操作备注',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_customer_id` (`customer_id`) USING BTREE COMMENT '客户ID索引',
  KEY `idx_operation_type` (`operation_type`) USING BTREE COMMENT '操作类型索引',
  KEY `idx_create_time` (`create_time`) USING BTREE COMMENT '创建时间索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 CHARACTER SET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户账户操作日志表';

-- ----------------------------
-- 客户发票信息表
-- ----------------------------
DROP TABLE IF EXISTS `customer_info_invoice_info`;
CREATE TABLE `customer_info_invoice_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '发票信息ID',
  `customer_id` bigint NOT NULL COMMENT '客户ID',
  `invoice_type` tinyint NOT NULL COMMENT '发票类型：1-普通发票，2-专用发票',
  `invoice_title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '发票抬头',
  `tax_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '税号',
  `company_address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '公司地址',
  `company_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '公司电话',
  `bank_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '开户银行',
  `bank_account` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '银行账号',
  `receiver_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '收票人姓名',
  `receiver_phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '收票人电话',
  `receiver_address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '收票人地址',
  `auto_invoice` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否自动开票',
  `invoice_threshold` decimal(10,2) NULL DEFAULT NULL COMMENT '开票阈值',
  `is_default` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否默认发票信息',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_customer_id` (`customer_id`) USING BTREE COMMENT '客户ID索引',
  KEY `idx_invoice_type` (`invoice_type`) USING BTREE COMMENT '发票类型索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 CHARACTER SET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户发票信息表';

-- ========================================
-- 字典数据配置
-- ========================================

-- ----------------------------
-- 字典类型数据
-- ----------------------------
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES
('客户类别', 'customer_category', 0, '客户分类管理', '1', NOW(), '1', NOW(), b'0'),
('客户来源渠道', 'customer_source', 0, '客户来源渠道管理', '1', NOW(), '1', NOW(), b'0'),
('客户类型', 'customer_type', 0, '客户装修类型管理', '1', NOW(), '1', NOW(), b'0'),
('证件类型', 'id_type', 0, '证件类型管理', '1', NOW(), '1', NOW(), b'0'),
('地址标签', 'address_label', 0, '收货地址标签管理', '1', NOW(), '1', NOW(), b'0'),
('客户状态', 'customer_status', 0, '客户状态管理', '1', NOW(), '1', NOW(), b'0'),
('VIP等级', 'vip_level', 0, 'VIP等级管理', '1', NOW(), '1', NOW(), b'0'),
('积分记录类型', 'points_record_type', 0, '积分记录类型管理', '1', NOW(), '1', NOW(), b'0'),
('积分来源类型', 'points_source_type', 0, '积分来源类型管理', '1', NOW(), '1', NOW(), b'0'),
('积分兑换类型', 'points_exchange_type', 0, '积分兑换类型管理', '1', NOW(), '1', NOW(), b'0'),
('账户操作类型', 'account_operation_type', 0, '账户操作类型管理', '1', NOW(), '1', NOW(), b'0'),
('发票类型', 'invoice_type', 0, '发票类型管理', '1', NOW(), '1', NOW(), b'0');

-- ----------------------------
-- 字典数据
-- ----------------------------
-- 客户类别
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES
(1, '服务商客户', '1', 'customer_category', 0, '1', NOW(), '1', NOW(), b'0'),
(2, '品牌家装客户', '2', 'customer_category', 0, '1', NOW(), '1', NOW(), b'0'),
(3, '个人客户', '3', 'customer_category', 0, '1', NOW(), '1', NOW(), b'0'),
(4, '企业客户', '4', 'customer_category', 0, '1', NOW(), '1', NOW(), b'0');

-- 来源渠道
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES
(1, '设计师推荐', '1', 'customer_source', 0, '1', NOW(), '1', NOW(), b'0'),
(2, '大众点评', '2', 'customer_source', 0, '1', NOW(), '1', NOW(), b'0'),
(3, '网络推广', '3', 'customer_source', 0, '1', NOW(), '1', NOW(), b'0'),
(4, '朋友介绍', '4', 'customer_source', 0, '1', NOW(), '1', NOW(), b'0'),
(5, '门店到访', '5', 'customer_source', 0, '1', NOW(), '1', NOW(), b'0'),
(6, '其他渠道', '6', 'customer_source', 0, '1', NOW(), '1', NOW(), b'0');

-- 客户类型
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES
(1, '前装客户', '1', 'customer_type', 0, '1', NOW(), '1', NOW(), b'0'),
(2, '后装客户', '2', 'customer_type', 0, '1', NOW(), '1', NOW(), b'0'),
(3, '改造客户', '3', 'customer_type', 0, '1', NOW(), '1', NOW(), b'0');

-- 证件类型
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES
(1, '身份证', '1', 'id_type', 0, '1', NOW(), '1', NOW(), b'0'),
(2, '护照', '2', 'id_type', 0, '1', NOW(), '1', NOW(), b'0'),
(3, '其他', '3', 'id_type', 0, '1', NOW(), '1', NOW(), b'0');

-- 地址标签
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES
(1, '家', '1', 'address_label', 0, '1', NOW(), '1', NOW(), b'0'),
(2, '公司', '2', 'address_label', 0, '1', NOW(), '1', NOW(), b'0'),
(3, '其他', '3', 'address_label', 0, '1', NOW(), '1', NOW(), b'0');

-- 客户状态
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES
(1, '正常', '1', 'customer_status', 0, '1', NOW(), '1', NOW(), b'0'),
(2, '冻结', '2', 'customer_status', 0, '1', NOW(), '1', NOW(), b'0'),
(3, '黑名单', '3', 'customer_status', 0, '1', NOW(), '1', NOW(), b'0'),
(4, 'VIP', '4', 'customer_status', 0, '1', NOW(), '1', NOW(), b'0');

-- VIP等级
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES
(1, '普通', '0', 'vip_level', 0, '1', NOW(), '1', NOW(), b'0'),
(2, 'VIP1', '1', 'vip_level', 0, '1', NOW(), '1', NOW(), b'0'),
(3, 'VIP2', '2', 'vip_level', 0, '1', NOW(), '1', NOW(), b'0'),
(4, 'VIP3', '3', 'vip_level', 0, '1', NOW(), '1', NOW(), b'0');

-- 积分记录类型
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES
(1, '获得积分', '1', 'points_record_type', 0, '1', NOW(), '1', NOW(), b'0'),
(2, '消费积分', '2', 'points_record_type', 0, '1', NOW(), '1', NOW(), b'0'),
(3, '积分调整', '3', 'points_record_type', 0, '1', NOW(), '1', NOW(), b'0'),
(4, '积分过期', '4', 'points_record_type', 0, '1', NOW(), '1', NOW(), b'0');

-- 积分来源类型
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES
(1, '消费获得', '1', 'points_source_type', 0, '1', NOW(), '1', NOW(), b'0'),
(2, '活动奖励', '2', 'points_source_type', 0, '1', NOW(), '1', NOW(), b'0'),
(3, '签到获得', '3', 'points_source_type', 0, '1', NOW(), '1', NOW(), b'0'),
(4, '推荐奖励', '4', 'points_source_type', 0, '1', NOW(), '1', NOW(), b'0'),
(5, '手动调整', '5', 'points_source_type', 0, '1', NOW(), '1', NOW(), b'0');

-- 积分兑换类型
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES
(1, '商品兑换', '1', 'points_exchange_type', 0, '1', NOW(), '1', NOW(), b'0'),
(2, '服务兑换', '2', 'points_exchange_type', 0, '1', NOW(), '1', NOW(), b'0'),
(3, '现金兑换', '3', 'points_exchange_type', 0, '1', NOW(), '1', NOW(), b'0');

-- 账户操作类型
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES
(1, '延期', '1', 'account_operation_type', 0, '1', NOW(), '1', NOW(), b'0'),
(2, '重置密码', '2', 'account_operation_type', 0, '1', NOW(), '1', NOW(), b'0'),
(3, '锁定', '3', 'account_operation_type', 0, '1', NOW(), '1', NOW(), b'0'),
(4, '解锁', '4', 'account_operation_type', 0, '1', NOW(), '1', NOW(), b'0'),
(5, '登录', '5', 'account_operation_type', 0, '1', NOW(), '1', NOW(), b'0');

-- 发票类型
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES
(1, '普通发票', '1', 'invoice_type', 0, '1', NOW(), '1', NOW(), b'0'),
(2, '专用发票', '2', 'invoice_type', 0, '1', NOW(), '1', NOW(), b'0');

SET FOREIGN_KEY_CHECKS = 1;

-- ========================================
-- 执行说明
-- ========================================
/*
执行顺序：
1. 先执行表结构创建部分
2. 再执行字典数据配置部分

注意事项：
1. 确保system_dict_type和system_dict_data表已存在
2. 客户编号(customer_no)需要在应用层生成，格式：门店代码+年月+流水号
3. 手机号在租户内唯一，支持多租户数据隔离
4. 推荐关系表防止重复推荐，一对推荐人和被推荐人只能有一条记录
5. 地址表支持一个客户多个收货地址，但只能有一个默认地址
6. 状态变更记录支持审批流程，重要状态变更需要审批
7. 积分记录支持过期管理和兑换功能
8. 发票信息支持自动开票设置
9. 账户操作日志记录所有账户相关操作

性能优化：
1. 为常用查询字段创建了索引
2. 手机号和客户编号创建了唯一索引
3. 外键关联字段创建了普通索引
4. 时间字段创建了索引以支持时间范围查询
5. 状态字段创建了索引以支持状态筛选

扩展性：
1. 预留了奖励金额和奖励状态字段，支持推荐奖励功能
2. 备注字段支持业务扩展需求
3. 状态字段支持数据状态管理
4. JSON字段支持灵活的数据存储需求
5. 审批流程支持工作流扩展
6. 操作日志支持审计追踪

业务支持：
1. 支持客户基本信息管理和编辑
2. 支持客户状态变更和审批流程
3. 支持客户收货地址管理
4. 支持客户推荐关系管理
5. 支持客户积分管理和兑换功能
6. 支持客户账户操作日志记录
7. 支持客户发票信息管理
8. 支持多租户数据隔离
9. 支持客户VIP等级管理
10. 支持完整的审计追踪
*/
