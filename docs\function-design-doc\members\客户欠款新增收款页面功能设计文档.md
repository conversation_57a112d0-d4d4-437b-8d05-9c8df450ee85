# 客户管理模块 - 客户欠款新增收款页面功能设计文档

## 1. 页面概述

### 1.1 页面目的
客户欠款新增收款页面是智能家装管理平台客户管理系统的核心财务业务页面，负责手动登记客户的还款信息，更新欠款余额，实现收款登记、财务流水记录和欠款核销的完整流程。该页面通过规范化的收款流程，确保财务数据的准确性和客户欠款管理的有效性。

### 1.2 业务价值
- 提供标准化的客户收款登记功能，规范收款流程和财务管理
- 实现收款与欠款的自动冲抵，确保欠款余额的实时准确性
- 支持多种收款方式和账户管理，满足不同收款场景需求
- 建立完整的收款流水记录，支持财务核算和审计追溯
- 提供订单关联核销功能，实现收款与业务单据的精确匹配

### 1.3 页面入口
- **主要入口**: 客户管理 → 客户欠款 → 新增收款
- **快速入口**: 客户详情页面的快速收款链接
- **业务入口**: 销售订单、欠款管理等业务场景的收款操作

### 1.4 功能架构
客户欠款新增收款页面包含四个核心功能区域：
- **客户信息展示**: 客户基本信息和当前欠款状况展示
- **收款信息管理**: 收款数据的录入和账户管理
- **核销说明管理**: 订单关联核销和备注说明
- **操作流程控制**: 收款的提交、验证和流程控制

### 1.5 业务背景
- **收款登记**: 手动登记客户的还款操作
- **财务流水**: 自动生成财务收款流水记录
- **欠款冲抵**: 自动冲抵欠款金额，更新欠款余额
- **订单核销**: 可关联销售订单进行手动核销

## 2. 客户欠款新增收款页面操作流程图

```mermaid
flowchart TD
    A[用户访问新增收款页面] --> B[权限验证]
    B --> C{权限验证通过?}
    C -->|否| D[显示权限不足提示]
    C -->|是| E[页面初始化]
    
    E --> F[加载客户信息展示区]
    E --> G[加载收款信息填写区]
    E --> H[加载核销说明区]
    E --> I[显示操作按钮]
    
    F --> J[客户基本信息展示]
    J --> K[客户姓名手机]
    J --> L[应收金额显示]
    J --> M[已收金额显示]
    J --> N[含定金金额显示]
    
    L --> O{应收金额状态}
    O -->|负值| P[红色显示欠款金额]
    O -->|正值| Q[显示溢收金额]
    O -->|零值| R[显示已结清状态]
    
    G --> S[收款信息自动填充]
    S --> T[收款单号自动生成]
    S --> U[经手人默认当前用户]
    S --> V[收款日期默认当天]
    
    G --> W[收款信息手动录入]
    W --> X[收款账户选择]
    W --> Y[本次收款金额输入]
    W --> Z[优惠金额输入]
    W --> AA[余额支付输入]
    
    X --> BB[账户类型选择]
    BB --> CC[现金账户]
    BB --> DD[支付宝账户]
    BB --> EE[对公账户]
    BB --> FF[其他账户]
    
    Y --> GG{收款金额验证}
    GG -->|金额≤0| HH[显示金额错误提示]
    GG -->|金额>0| II[金额验证通过]
    
    Z --> JJ{优惠金额验证}
    JJ -->|金额<0| KK[显示优惠金额错误提示]
    JJ -->|金额≥0| LL[优惠金额验证通过]
    
    AA --> MM{余额支付验证}
    MM -->|超过可用余额| NN[显示余额不足提示]
    MM -->|余额充足| OO[余额支付验证通过]
    
    H --> PP[核销说明管理]
    PP --> QQ[关联订单选择]
    PP --> RR[备注信息录入]
    
    QQ --> SS[点击详情按钮]
    SS --> TT[跳转订单选择界面]
    TT --> UU[订单列表展示]
    UU --> VV[选择关联订单]
    VV --> WW[订单信息回填]
    
    RR --> XX[备注内容输入]
    XX --> YY[常用备注模板]
    YY --> ZZ[测试费退还]
    YY --> AAA[客户现场还款]
    YY --> BBB[其他自定义备注]
    
    I --> CCC[操作按钮功能]
    CCC --> DDD[提交收款]
    CCC --> EEE[取消操作]
    
    DDD --> FFF{提交前校验}
    FFF -->|校验失败| GGG[显示校验错误提示]
    FFF -->|校验通过| HHH[执行收款处理]
    
    HHH --> III[收款金额计算]
    III --> JJJ[总收款 = 本次收款 + 优惠金额 + 余额支付]
    JJJ --> KKK{收款金额验证}
    KKK -->|超过欠款金额| LLL[显示超额收款提示]
    KKK -->|金额合理| MMM[收款金额验证通过]
    
    MMM --> NNN[数据处理逻辑]
    NNN --> OOO[更新客户欠款余额]
    OOO --> PPP[客户欠款 = 原始欠款 - 总收款]
    PPP --> QQQ{欠款余额状态}
    QQQ -->|余额=0| RRR[状态更新为已结清]
    QQQ -->|余额<0| SSS[仍为欠款状态]
    QQQ -->|余额>0| TTT[提示溢收待处理]
    
    NNN --> UUU[生成收款流水记录]
    UUU --> VVV[客户收款流水表]
    UUU --> WWW[财务账户收支记录]
    UUU --> XXX[欠款单记录更新]
    
    NNN --> YYY[订单核销处理]
    YYY --> ZZZ{是否关联订单?}
    ZZZ -->|是| AAAA[更新订单收款状态]
    ZZZ -->|否| BBBB[跳过订单处理]
    
    AAAA --> CCCC[订单收款金额更新]
    CCCC --> DDDD[订单状态变更]
    
    RRR --> EEEE[收款完成通知]
    SSS --> EEEE
    TTT --> EEEE
    BBBB --> EEEE
    DDDD --> EEEE
    
    EEEE --> FFFF[返回客户欠款列表]
    
    EEE --> GGGG{确认取消操作?}
    GGGG -->|否| HHHH[继续编辑]
    GGGG -->|是| IIII[清空页面数据]
    IIII --> FFFF
    
    style A fill:#e1f5fe
    style C fill:#fff3e0
    style D fill:#ffebee
    style O fill:#fff3e0
    style P fill:#ffebee
    style Q fill:#fff8e1
    style R fill:#e8f5e8
    style GG fill:#fff3e0
    style HH fill:#ffebee
    style JJ fill:#fff3e0
    style KK fill:#ffebee
    style MM fill:#fff3e0
    style NN fill:#ffebee
    style FFF fill:#fff3e0
    style GGG fill:#ffebee
    style KKK fill:#fff3e0
    style LLL fill:#fff8e1
    style QQQ fill:#fff3e0
    style RRR fill:#e8f5e8
    style TTT fill:#fff8e1
    style ZZZ fill:#fff3e0
    style GGGG fill:#fff3e0
    style EEEE fill:#e8f5e8
```

### 流程说明
客户欠款新增收款页面的操作流程主要包含以下几个核心环节：

1. **权限验证与页面初始化**：验证用户收款权限，初始化页面各功能区域
2. **客户信息展示**：展示客户基本信息和当前欠款状况，包括应收、已收、定金等
3. **收款信息录入**：录入收款账户、金额、优惠、余额支付等信息，实时验证
4. **核销说明管理**：关联订单选择和备注信息录入
5. **提交前校验**：验证收款金额合理性和业务规则
6. **数据处理逻辑**：更新欠款余额、生成流水记录、处理订单核销

## 3. 详细功能设计

### 3.1 客户信息展示功能

#### 3.1.1 客户基本信息展示
**功能描述**：展示客户的基本信息和身份识别

**展示内容**：
- **客户姓名**：客户的真实姓名
- **联系电话**：客户的主要联系方式
- **客户编号**：系统内客户的唯一标识
- **客户类别**：客户的分类标识

#### 3.1.2 欠款状况展示
**功能描述**：展示客户当前的欠款状况和财务信息

**财务信息**：
- **应收金额**：当前客户总欠款金额
  - 负值显示：红色显示，表示客户欠款
  - 正值显示：显示溢收金额
  - 零值显示：显示已结清状态
- **已收金额**：已收款总金额统计
- **含定金**：系统记录的已收定金金额
  - 计入可用抵扣金额
  - 影响收款计算逻辑

**示例展示**：
```
应收金额：-3647.97 元 （红色显示，表示客户欠款）
已收金额：4999.00 元
含定金：1000.00 元
```

### 3.2 收款信息管理功能

#### 3.2.1 收款单据信息
**功能描述**：管理收款单据的基础信息

**单据信息**：
- **收款单号**：系统自动生成唯一标识
  - 自动生成（如：SKD...）
  - 唯一性保证
  - 支持自定义编号规则
- **经手人**：当前操作人
  - 系统默认当前登录人
  - 支持手动选择其他用户
  - 权限控制可选范围
- **收款日期**：实际收款的日期
  - 默认当天日期
  - 支持手动调整
  - 日期格式验证

#### 3.2.2 收款账户管理
**功能描述**：管理收款的账户信息

**账户类型**：
- **现金账户**：现金收款
- **支付宝账户**：支付宝收款
- **对公账户**：银行对公账户收款
- **其他账户**：其他收款方式

**账户功能**：
- **下拉选择**：从可用账户中选择
- **账户验证**：验证账户的有效性
- **权限控制**：基于用户权限控制可选账户

#### 3.2.3 收款金额管理
**功能描述**：管理各种收款金额的录入

**金额类型**：
- **本次收款**：此次收款金额
  - 数值输入，必填字段
  - 非负数验证
  - 不能超过欠款金额
- **优惠金额**：给予客户减免的欠款
  - 数值输入，可选字段
  - 非负数验证
  - 计入总收款金额
- **余额支付**：客户使用可用余额抵扣
  - 数值输入，可选字段
  - 非现金流，不影响账户
  - 不能超过客户可用余额

### 3.3 核销说明管理功能

#### 3.3.1 订单关联核销
**功能描述**：关联销售订单进行收款核销

**关联功能**：
- **订单选择**：可选关联销售单/欠款单据
- **详情按钮**：跳转至关联单据选择界面
- **核销匹配**：便于收款与订单的精确匹配
- **金额验证**：关联的收款金额不得超过订单金额

**选择流程**：
1. 点击【详情】按钮
2. 跳转订单选择界面
3. 展示可关联的订单列表
4. 选择具体订单
5. 订单信息自动回填

#### 3.3.2 备注信息管理
**功能描述**：管理收款的备注说明信息

**备注功能**：
- **自由备注**：支持输入自由备注说明
- **常用模板**：提供常用备注模板
  - "测试费退还"
  - "客户现场还款"
  - "定金转收款"
  - "其他自定义备注"
- **字符限制**：限制备注的字符长度
- **格式验证**：验证备注内容的格式

### 3.4 数据验证功能

#### 3.4.1 提交前校验
**功能描述**：提交收款前的完整性校验

**校验规则**：
- **金额合理性验证**：
  ```
  本次收款 + 优惠金额 + 余额支付 ≤ 欠款金额
  ```
- **必填字段验证**：收款账户、收款金额不能为空
- **关联订单验证**：若关联订单，收款金额不得超限
- **余额充足性验证**：余额支付不超过可用余额

#### 3.4.2 业务规则验证
**功能描述**：验证收款业务规则的合理性

**业务验证**：
- **收款权限验证**：验证用户的收款操作权限
- **客户状态验证**：验证客户状态是否允许收款
- **账户状态验证**：验证收款账户状态正常
- **金额范围验证**：验证收款金额在合理范围内

### 3.5 数据处理逻辑

#### 3.5.1 欠款余额更新
**功能描述**：自动更新客户的欠款余额

**计算逻辑**：
```
客户欠款金额 = 原始欠款 - 本次收款 - 优惠金额 - 余额支付
```

**状态判断**：
- **余额 = 0**：状态更新为"已结清"
- **余额 < 0**：仍为欠款状态
- **余额 > 0**：提示溢收待处理

#### 3.5.2 流水记录生成
**功能描述**：自动生成相关的流水记录

**记录类型**：
- **客户收款流水表**：记录客户收款明细
- **财务模块账户收支记录**：记录账户资金变动
- **欠款单记录**：更新欠款单状态和金额

#### 3.5.3 订单核销处理
**功能描述**：处理与订单的核销关联

**核销逻辑**：
- **订单收款金额更新**：更新订单的收款金额
- **订单状态变更**：根据收款情况更新订单状态
- **核销记录生成**：生成收款与订单的核销记录

### 3.6 操作流程控制

#### 3.6.1 提交收款功能
**功能描述**：提交收款信息并执行相关业务逻辑

**提交流程**：
1. **数据完整性验证**：验证所有必填信息
2. **业务规则验证**：验证业务逻辑规则
3. **收款处理执行**：执行收款业务逻辑
4. **数据更新**：更新相关数据记录
5. **通知发送**：发送收款完成通知

#### 3.6.2 取消操作功能
**功能描述**：取消当前收款编辑

**取消流程**：
- **确认提示**：确认是否取消当前操作
- **数据保护**：提醒保存未提交的数据
- **页面清空**：清空页面编辑数据
- **返回列表**：返回客户欠款列表页面

## 4. 用户界面设计

### 4.1 页面布局设计
- **客户信息展示区**：客户基本信息和欠款状况展示
- **收款信息填写区**：收款数据录入和账户选择
- **核销说明区**：订单关联和备注信息
- **操作按钮区**：提交、取消等操作

### 4.2 交互设计规范
- **智能填充**：自动填充收款单号、经手人、日期等信息
- **实时验证**：金额输入时实时验证合理性
- **友好提示**：清晰的错误提示和操作指导
- **状态标识**：直观的欠款状态颜色标识

### 4.3 响应式设计
- **PC端**：完整功能展示，多区域布局
- **平板端**：适配中等屏幕，关键功能优先
- **移动端**：简化布局，核心功能保留

## 5. 权限控制

### 5.1 功能权限
- **页面访问权限**：新增收款页面访问权限
- **收款操作权限**：客户收款操作权限
- **账户选择权限**：收款账户选择权限
- **订单关联权限**：订单核销关联权限

### 5.2 数据权限
- **客户权限**：只能为有权限的客户进行收款
- **账户权限**：只能使用有权限的收款账户
- **金额权限**：收款金额的权限控制

## 6. 异常处理

### 6.1 业务异常
- **金额超限**：收款金额超过欠款金额的处理
- **余额不足**：客户余额不足的处理
- **账户异常**：收款账户异常的处理
- **订单冲突**：订单核销冲突的处理

### 6.2 系统异常
- **网络异常**：网络连接异常的处理和重试
- **数据保存失败**：数据保存失败的处理和恢复
- **权限异常**：权限验证失败的友好提示
- **系统错误**：系统错误的异常处理和用户提示

---

**文档版本**：v1.0  
**编写日期**：2025-07-02  
**编写人员**：AI系统架构师  
**审核状态**：待审核
