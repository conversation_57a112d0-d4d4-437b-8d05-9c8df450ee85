<execution>
<constraint>
## 基于学习文档的严格约束
- **8章节结构强制**：必须严格按照学习文档中定义的8个章节结构
- **4个核心目标导向**：生成的.mdc文件必须直接支持需求分析、技术设计、测试用例生成、TDD流程指导
- **章节3重点强化**：核心规则与逻辑章节是关键输入，必须详尽完整
- **章节8实用性**：测试指引必须提供具体可执行的JUnit/TestNG代码示例
- **业务规则清晰性**：所有业务规则必须用清晰、无歧义的自然语言描述
- **接口定义完整性**：每个public方法都必须包含完整的前置条件、后置条件、异常处理
</constraint>

<rule>
## 增强的.mdc生成规则
- **章节完整性**：8个章节必须全部存在且内容充实
- **业务规则精确性**：每个业务规则都要有具体的触发条件和执行结果
- **数据校验详细性**：每个字段都要明确数据类型、约束条件、业务含义
- **测试用例具体性**：提供可直接运行的JUnit/TestNG代码模板
- **TDD步骤操作性**：每个TDD步骤都要有具体的代码示例
- **异常处理完整性**：每个异常都要有错误码、触发条件、处理方式
</rule>

<guideline>
## 基于学习文档的优化指导
- **章节3重点投入**：核心规则与逻辑章节投入60%的分析精力
- **业务语言优先**：业务规则用自然语言，技术实现用精确术语
- **测试驱动思维**：每个业务规则都要考虑如何测试
- **实例化说明**：抽象概念都要有具体的代码示例
- **可追溯性**：文档内容能够追溯到具体的代码行
- **版本化管理**：.mdc文件要与Java文件保持同步更新
</guideline>

<process>
## 基于学习文档的完整生成流程

### Step 1: 深度代码分析 (30%)
```mermaid
flowchart TD
    A[读取Java源文件] --> B[解析包结构和imports]
    B --> C[分析类定义和注解]
    C --> D[提取所有方法和字段]
    D --> E[识别业务逻辑片段]
    E --> F[分析异常处理机制]
    F --> G[构建依赖关系图]
```

**详细分析要点**：
- **包结构分析**：确定文件在项目架构中的位置
- **注解识别**：Spring、JPA、Validation等注解的业务含义
- **方法分析**：public方法的完整签名和职责
- **业务逻辑识别**：if-else、switch、循环中的业务规则
- **异常分析**：自定义异常、标准异常的触发条件
- **依赖识别**：内部依赖和外部依赖的完整映射

### Step 2: 8章节内容生成 (50%)

#### 章节1-2：基础信息生成
```mermaid
graph LR
    A[文件路径分析] --> B[包结构解析]
    B --> C[架构定位推理]
    C --> D[职责识别]
    D --> E[目标提取]
```

**生成模板**：
```markdown
**一、文件概览与架构上下文**
* **文件名**: [从路径提取，如UserService.java]
* **文件路径**: [完整相对路径，如src/main/java/com/example/user/service/UserService.java]
* **所属包/模块**: [包路径，如com.example.user.service 或 用户管理核心模块]
* **项目目录规范引用**: [分析包结构，推断架构模式，如DDD、分层架构等]
* **文件定位理由**: [详细解释为什么放在这个位置，与架构规范的对应关系]
* **简要描述**: [1-2句话概括核心功能]

**二、主要职责与目标**
* [基于类名、方法名、注释提取的核心职责]
* [识别解决的具体业务问题]
* [在系统架构中的关键角色]
```

#### 章节3：核心规则与逻辑提取（重点强化）
```mermaid
graph TD
    A[方法体分析] --> B[业务规则识别]
    A --> C[数据校验提取]
    A --> D[算法逻辑分析]
    A --> E[状态机识别]
    A --> F[权限规则分析]
    A --> G[配置依赖识别]
    
    B --> H[自然语言描述]
    C --> I[字段约束详述]
    D --> J[算法步骤分解]
    E --> K[状态转换图]
    F --> L[权限矩阵]
    G --> M[配置影响分析]
```

**增强的提取策略**：
- **业务规则提取**：
  ```java
  // 示例代码分析
  if (user.getVipLevel() >= 5 && user.getAnnualSpending() >= 10000) {
      // 业务规则：用户VIP等级达到5级且年度有效消费金额满10000元，则在次月1日自动升级为白金会员
  }
  ```
- **数据校验规则**：
  ```java
  @NotNull
  @DecimalMin(value = "0.01", message = "订单金额必须大于0")
  @DecimalMax(value = "999999.99", message = "订单金额不能超过999999.99")
  private BigDecimal orderAmount;
  // 提取：订单金额字段，BigDecimal类型，非空，范围0.01-999999.99，最多2位小数
  ```

#### 章节4-5：依赖与接口分析
```mermaid
flowchart LR
    A[Import语句分析] --> B[内部依赖识别]
    A --> C[外部依赖识别]
    D[方法签名分析] --> E[参数详述]
    D --> F[返回值分析]
    D --> G[异常处理完整描述]
    
    B --> H[依赖原因分析]
    C --> I[外部服务用途]
    E --> J[参数校验规则]
    F --> K[返回值结构]
    G --> L[异常码和消息]
```

**接口定义增强模板**：
```markdown
**接口/方法签名**: `public OrderResponse createOrder(UserContext userContext, CreateOrderRequest request) throws OrderCreationException, InvalidInputException`

**功能描述**: 创建新订单，包含库存检查、价格计算、支付准备等完整流程

**调用上下文/前置条件**:
- 用户必须已认证 (userContext包含有效的用户ID和角色信息)
- 请求参数request中的商品ID列表必须在商品库中存在且为可售状态
- 用户账户状态必须为正常 (非冻结、非黑名单)

**输入参数**:
- userContext: UserContext类型，必需，包含userId(Long)、userRole(String)、sessionId(String)
- request: CreateOrderRequest类型，必需，包含：
  - productIds: List<Long>，非空，商品ID列表
  - quantities: List<Integer>，非空，对应商品数量，必须>0
  - deliveryAddress: String，非空，收货地址
  - paymentMethod: PaymentType枚举，必需

**输出/返回值**:
- 成功时：OrderResponse类型，包含：
  - orderId: Long，新创建的订单ID
  - orderStatus: OrderStatus枚举，初始状态为PENDING_PAYMENT
  - totalAmount: BigDecimal，订单总金额
  - estimatedDeliveryDate: LocalDateTime，预计送达时间

**后置条件/副作用**:
- 数据库t_order表新增一条记录，状态为PENDING_PAYMENT
- 向库存系统发送锁定库存请求
- 向支付系统发送支付准备请求
- 记录操作日志到t_order_log表

**异常/错误处理**:
- OrderCreationException: 订单创建失败
  - 错误码: ORD_ERR_001
  - 触发条件: 库存不足、价格计算错误、支付准备失败
  - 返回消息: "订单创建失败，原因：[具体原因]"
- InvalidInputException: 输入参数校验失败
  - 错误码: VALIDATION_ERR_002
  - 触发条件: 参数为空、格式错误、业务校验失败
  - 返回消息: 包含详细的字段错误信息

**幂等性要求**: 通过requestId保证幂等性，相同requestId的重复请求返回相同结果

**事务要求**: 整个方法体在单个数据库事务中执行，失败时回滚所有操作
```

#### 章节6-7：常量和最佳实践
```mermaid
graph TD
    A[常量字段扫描] --> B[枚举类型分析]
    C[代码复杂度分析] --> D[性能瓶颈识别]
    C --> E[最佳实践提取]
    C --> F[注意事项识别]
    
    B --> G[业务含义解释]
    D --> H[优化建议]
    E --> I[编码规范]
    F --> J[陷阱预警]
```

#### 章节8：测试指引强化（重点）
```mermaid
flowchart TD
    A[业务规则] --> B[单元测试设计]
    C[接口方法] --> D[集成测试场景]
    E[异常情况] --> F[边界测试用例]
    G[依赖关系] --> H[Mock策略设计]
    
    B --> I[JUnit代码模板]
    D --> J[TestNG集成测试]
    F --> K[参数化测试]
    H --> L[Mockito配置]
```

**增强的测试指引模板**：
```markdown
**单元测试关键点**:
针对业务规则"用户VIP等级达到5级且年消费满10000元，则自动升级"的测试：

```java
@Test
void testVipUpgradeRule_LevelNotEnough() {
    // Given: 用户等级4级，消费15000元
    User user = User.builder().vipLevel(4).annualSpending(15000).build();
    
    // When: 检查升级条件
    boolean shouldUpgrade = userService.shouldUpgradeToVip(user);
    
    // Then: 不应该升级
    assertFalse(shouldUpgrade);
}

@Test
void testVipUpgradeRule_SpendingNotEnough() {
    // Given: 用户等级5级，消费8000元
    User user = User.builder().vipLevel(5).annualSpending(8000).build();
    
    // When: 检查升级条件
    boolean shouldUpgrade = userService.shouldUpgradeToVip(user);
    
    // Then: 不应该升级
    assertFalse(shouldUpgrade);
}

@Test
void testVipUpgradeRule_BothConditionsMet() {
    // Given: 用户等级5级，消费12000元
    User user = User.builder().vipLevel(5).annualSpending(12000).build();
    
    // When: 检查升级条件
    boolean shouldUpgrade = userService.shouldUpgradeToVip(user);
    
    // Then: 应该升级
    assertTrue(shouldUpgrade);
}
```

**集成测试场景**:
```java
@SpringBootTest
@Transactional
class OrderServiceIntegrationTest {
    
    @Autowired
    private OrderService orderService;
    
    @MockBean
    private InventoryService inventoryService;
    
    @Test
    void testCreateOrder_Success() {
        // Given: 模拟库存充足
        when(inventoryService.checkStock(anyList())).thenReturn(true);
        when(inventoryService.lockStock(anyList())).thenReturn(true);
        
        UserContext userContext = UserContext.builder()
            .userId(1L)
            .userRole("CUSTOMER")
            .build();
            
        CreateOrderRequest request = CreateOrderRequest.builder()
            .productIds(Arrays.asList(1L, 2L))
            .quantities(Arrays.asList(1, 2))
            .deliveryAddress("北京市朝阳区xxx")
            .paymentMethod(PaymentType.ALIPAY)
            .build();
        
        // When: 创建订单
        OrderResponse response = orderService.createOrder(userContext, request);
        
        // Then: 验证结果
        assertNotNull(response.getOrderId());
        assertEquals(OrderStatus.PENDING_PAYMENT, response.getOrderStatus());
        assertTrue(response.getTotalAmount().compareTo(BigDecimal.ZERO) > 0);
        
        // 验证副作用
        verify(inventoryService).lockStock(anyList());
        // 验证数据库记录
        Order savedOrder = orderRepository.findById(response.getOrderId()).orElse(null);
        assertNotNull(savedOrder);
        assertEquals(OrderStatus.PENDING_PAYMENT, savedOrder.getStatus());
    }
}
```

**TDD开发步骤建议**:
1. **红阶段**：编写失败测试
2. **绿阶段**：编写最少代码使测试通过
3. **重构阶段**：优化代码结构

**测试数据构造与Mocking策略**:
- 边界值测试：金额=0、负数、超大数值
- 异常场景：网络超时、数据库异常、第三方服务失败
- Mock策略：外部依赖全部Mock，数据库操作使用H2内存数据库
```

### Step 3: 质量检查与输出 (20%)
```mermaid
graph TD
    A[内容完整性检查] --> B[8章节齐全检查]
    B --> C[业务规则清晰度检查]
    C --> D[测试用例可执行性检查]
    D --> E[4个目标支持度检查]
    E --> F[最终输出]
```

**质量检查清单**：
- [ ] 8个章节结构完整
- [ ] 章节3核心规则详尽
- [ ] 章节5接口定义完整
- [ ] 章节8测试指引可执行
- [ ] 能直接支持需求分析
- [ ] 能指导技术设计
- [ ] 能生成测试用例
- [ ] 能支持TDD流程
</process>

<criteria>
## 基于学习文档的质量标准

### 完整性标准
- ✅ 8个章节结构完整，每个章节内容充实
- ✅ 章节3核心规则涵盖所有业务逻辑
- ✅ 章节5接口定义包含所有public方法
- ✅ 章节8测试指引提供完整的测试方案

### 准确性标准
- ✅ 业务规则用清晰无歧义的自然语言描述
- ✅ 技术细节与代码实际行为完全一致
- ✅ 异常处理包含完整的错误码和消息
- ✅ 依赖关系准确反映代码中的实际依赖

### 可操作性标准
- ✅ 能直接生成详细的需求分析方案
- ✅ 能指导完整的技术设计方案
- ✅ 能生成可执行的测试用例集
- ✅ 能提供具体的TDD流程指导

### 实用性标准
- ✅ 测试用例提供完整的JUnit/TestNG代码
- ✅ TDD步骤提供具体的开发指导
- ✅ Mock策略提供详细的实现方案
- ✅ 文档格式符合Markdown规范

### 一致性标准
- ✅ 同一项目内所有.mdc文件格式统一
- ✅ 术语使用一致，避免歧义
- ✅ 章节结构严格遵循8章节模板
- ✅ 代码示例风格统一
</criteria>
</execution>