使用角色java-tdd-archiect 结合以下步骤自动化执行基于XMind测试用例的开发流程：
1. 项目初始化与测试用例读取
您好，我需要基于XMind测试用例开发一个Java项目。
测试用例文件路径是@[Markdown文件]，主要功能是[简要描述功能]。
任务清单文件路径是@「任务清单」。
技术设计文档路径是@「技术设计文档」。
请首先帮我分析markdown测试用例的结构，提取主要测试点和测试场景，然后为我生成一个Maven项目结构，并设计初始的测试类。
2. 测试代码生成
现在请基于XMind测试用例中的[测试模块名称]模块，编写对应的JUnit 5测试类。

要求：
1. 测试方法命名遵循[method]_[condition]_[expectation]格式
2. 使用given-when-then注释清晰标注测试步骤
3. 适当使用Mockito模拟依赖
4. 确保测试最初会失败（遵循TDD红色阶段）


3. 功能实现
现在测试已经准备好了，请实现相应的功能代码使测试通过。

要求：
1. 符合SOLID原则
2. 清晰的命名和适当的注释
3. 最小实现，确保测试通过
4. 代码结构符合控制器-服务-仓库分层架构

4. 重构与优化
测试已通过，现在请对代码进行重构优化，但确保测试仍然通过。

主要优化点：
1. 消除重复代码
2. 提取通用方法
3. 优化性能或内存使用
4. 确保代码可读性和可维护性


5. 进度更新与文档
请为当前完成的功能更新README.md文件，并生成一份简要的测试报告。

报告应包含：
1. 已实现的功能点
2. 测试覆盖率情况
3. 下一步计划
4. 已知问题或限制