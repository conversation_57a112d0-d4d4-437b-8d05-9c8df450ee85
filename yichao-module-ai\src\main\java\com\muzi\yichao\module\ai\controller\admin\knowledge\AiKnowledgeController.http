### 创建知识库
POST {{baseUrl}}/ai/knowledge/create
Content-Type: application/json
Authorization: {{token}}
tenant-id: {{adminTenantId}}

{
  "name": "测试标题",
  "description": "测试描述",
  "embeddingModelId": 30,
  "topK": 3,
  "similarityThreshold": 0.5,
  "status": 0
}

### 更新知识库
PUT {{baseUrl}}/ai/knowledge/update
Content-Type: application/json
Authorization: {{token}}
tenant-id: {{adminTenantId}}

{
  "id": 1,
  "name": "测试标题（更新）",
  "description": "测试描述",
  "embeddingModelId": 30,
  "topK": 5,
  "similarityThreshold": 0.6,
  "status": 0
}

### 获取知识库分页
GET {{baseUrl}}/ai/knowledge/page?pageNo=1&pageSize=10
Authorization: {{token}}
tenant-id: {{adminTenantId}}