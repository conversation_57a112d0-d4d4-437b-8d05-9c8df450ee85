# 商品管理模块 - 商品出库功能设计文档

## 1. 模块概述

### 1.1 模块目的
商品出库模块是智能家装管理平台商品管理系统的核心业务模块，负责商品出库流程管理、库存扣减控制、出库单据管理和出库明细跟踪。该模块通过规范化的出库流程，确保库存数据的准确性和出库业务的可追溯性。

### 1.2 业务价值
- 建立完整的出库单据管理体系，规范出库流程和审批机制
- 支持多种出库类型管理，满足销售、调拨、借用、返修等不同业务场景
- 提供多维度的出库数据查询和统计分析，支持库存管理决策
- 实现出库单生命周期管理，从新增到出库完成的全流程跟踪
- 建立出库数据的双视图展示，支持出库单和出库明细的不同查看需求

### 1.3 功能架构
商品出库模块包含六个核心功能：
- **出库单管理**: 出库单据的新增、编辑、审核和状态管理
- **双视图展示**: 出库单总览和出库明细两种视图模式
- **筛选查询功能**: 多维度出库数据筛选和精确查询
- **出库类型管理**: 销售、调拨、借用、返修等多种出库类型
- **审核出库流程**: 出库单审核和库存自动扣减处理
- **数据统计分析**: 出库数量、金额、类型等统计分析

## 2. 商品出库模块操作流程图

```mermaid
flowchart TD
    A[用户访问商品出库页面] --> B[权限验证]
    B --> C{权限验证通过?}
    C -->|否| D[显示权限不足提示]
    C -->|是| E[加载出库数据]
    
    E --> F[显示顶部导航标签]
    E --> G[显示筛选搜索区域]
    E --> H[加载出库数据表格]
    
    F --> I[出库单视图]
    F --> J[出库明细视图]
    
    I --> K[出库单总览展示]
    J --> L[出库明细记录展示]
    
    G --> M[筛选条件设置]
    M --> N[经手人筛选]
    M --> O[状态筛选]
    M --> P[往来单位筛选]
    M --> Q[分店筛选]
    M --> R[类型筛选]
    M --> S[单号查询]
    M --> T[出库日期范围]
    M --> U[备注搜索]
    M --> V[电话查询]
    
    N --> W[组合筛选处理]
    O --> W
    P --> W
    Q --> W
    R --> W
    S --> W
    T --> W
    U --> W
    V --> W
    
    W --> X[点击搜索按钮]
    X --> Y[发送筛选请求]
    Y --> Z[更新出库数据表格]
    
    G --> AA[操作按钮功能]
    AA --> BB[新增出库单]
    AA --> CC[清空筛选]
    AA --> DD[打印功能]
    
    BB --> EE[跳转新增页面]
    EE --> FF[选择出库类型]
    FF --> GG[填写出库信息]
    GG --> HH[选择往来单位]
    GG --> II[添加出库商品]
    GG --> JJ[设置出库库房]
    GG --> KK[填写备注信息]
    KK --> LL[保存出库单]
    LL --> MM{保存成功?}
    MM -->|否| NN[显示保存失败提示]
    MM -->|是| OO[返回出库单列表]
    
    CC --> PP[清空所有筛选条件]
    PP --> QQ[重新加载默认数据]
    
    DD --> RR{是否有数据?}
    RR -->|否| SS[打印按钮禁用]
    RR -->|是| TT[选择打印内容]
    TT --> UU[生成打印文件]
    
    H --> VV[出库单数据展示]
    VV --> WW[行级操作按钮]
    WW --> XX[查看详情]
    WW --> YY[审核操作]
    WW --> ZZ[编辑操作]
    WW --> AAA[删除操作]
    WW --> BBB[导出操作]
    
    XX --> CCC[显示出库单详情]
    CCC --> DDD[出库商品明细]
    CCC --> EEE[往来单位信息]
    CCC --> FFF[出库流程记录]
    
    YY --> GGG{审核权限检查}
    GGG -->|无权限| HHH[显示权限不足]
    GGG -->|有权限| III[审核确认界面]
    III --> JJJ{确认审核?}
    JJJ -->|否| KKK[取消审核]
    JJJ -->|是| LLL[执行审核操作]
    LLL --> MMM[更新出库单状态]
    MMM --> NNN[扣减库存数量]
    NNN --> OOO[生成出库记录]
    OOO --> PPP[发送出库通知]
    
    ZZ --> QQQ{编辑权限检查}
    QQQ -->|无权限| RRR[显示权限不足]
    QQQ -->|状态不允许| SSS[显示状态限制提示]
    QQQ -->|可编辑| TTT[跳转编辑页面]
    TTT --> UUU[修改出库信息]
    UUU --> VVV[保存修改]
    
    AAA --> WWW{删除权限检查}
    WWW -->|无权限| XXX[显示权限不足]
    WWW -->|状态不允许| YYY[显示状态限制提示]
    WWW -->|可删除| ZZZ[删除确认]
    ZZZ --> AAAA{确认删除?}
    AAAA -->|否| BBBB[取消删除]
    AAAA -->|是| CCCC[执行删除操作]
    CCCC --> DDDD[更新数据列表]
    
    BBB --> EEEE[选择导出格式]
    EEEE --> FFFF[生成导出文件]
    
    style A fill:#e1f5fe
    style C fill:#fff3e0
    style D fill:#ffebee
    style GGG fill:#fff3e0
    style HHH fill:#ffebee
    style JJJ fill:#fff3e0
    style QQQ fill:#fff3e0
    style RRR fill:#ffebee
    style SSS fill:#fff8e1
    style WWW fill:#fff3e0
    style XXX fill:#ffebee
    style YYY fill:#fff8e1
    style ZZZ fill:#fff3e0
    style AAAA fill:#fff3e0
    style PPP fill:#e8f5e8
```

### 流程说明
商品出库模块的操作流程主要包含以下几个核心环节：

1. **权限验证与双视图加载**：验证用户访问权限，提供出库单总览和出库明细两种视图模式
2. **多维度筛选查询**：支持经手人、状态、往来单位、分店、类型、单号、日期、备注、电话等多条件筛选
3. **出库单生命周期管理**：从新增、编辑到审核、出库完成的完整流程管理
4. **出库类型业务处理**：支持销售出库、调拨出库、借用出库、返修出库等多种业务场景
5. **审核出库流程**：出库单审核后自动扣减库存，生成出库记录
6. **数据导出和打印**：支持出库数据的导出和打印功能

## 3. 详细功能设计

### 3.1 双视图展示功能

#### 3.1.1 出库单视图
**功能描述**: 以出库单为维度展示出库数据总览

**视图特点**:
- **出库单总览**: 显示出库单据的汇总信息
- **单据级操作**: 提供出库单级别的操作功能
- **状态管理**: 展示出库单的审核和处理状态
- **往来单位**: 显示出库对应的客户或单位信息

**数据字段**:
- **日期**: 出库单据创建日期
- **单号**: 唯一出库单编号
- **往来单位**: 客户/供应商/内部单位名称
- **联系人**: 对应联系人姓名
- **联系电话**: 联系人电话号码
- **出库数量**: 出库商品总数量（多商品合计）
- **出库库房**: 实际出库的仓库位置
- **出库类型**: 销售、调拨、借用、返修等
- **经手人**: 实施出库操作的人员
- **出库状态**: 待审核、已出库等状态
- **备注信息**: 出库说明、物流要求等

#### 3.1.2 出库明细视图
**功能描述**: 以商品明细为维度展示出库记录

**视图特点**:
- **商品明细**: 显示每个商品的出库明细
- **明细级操作**: 提供商品明细级别的操作
- **库存跟踪**: 跟踪每个商品的库存变化
- **成本核算**: 支持出库成本的明细核算

**明细信息**:
- **商品信息**: 商品编号、名称、规格、型号
- **出库数量**: 具体商品的出库数量
- **出库单价**: 商品的出库单价
- **出库金额**: 商品出库的总金额
- **批次信息**: 商品的批次或序列号信息
- **库存变化**: 出库前后的库存数量变化

#### 3.1.3 视图切换功能
**功能描述**: 支持两种视图间的无缝切换

**切换特性**:
- **标签页切换**: 使用标签页形式切换视图
- **状态保持**: 保持筛选条件和分页状态
- **数据同步**: 确保两个视图的数据一致性
- **缓存机制**: 缓存视图状态，提升用户体验

### 3.2 筛选查询功能

#### 3.2.1 多维度筛选条件
**功能描述**: 提供全面的出库数据筛选功能

**筛选字段**:
- **经手人筛选**: 按负责出库操作的人员筛选
  - 下拉选择经手人员
  - 支持经手人姓名搜索
  - 显示每个经手人的出库单数量
- **状态筛选**: 按出库单状态筛选
  - 待审核：等待审核的出库单
  - 已出库：已完成出库的单据
  - 已取消：已取消的出库单
  - 审核中：正在审核流程中的单据
- **往来单位筛选**: 按客户或供应商筛选
  - 下拉选择往来单位
  - 支持单位名称搜索
  - 显示每个单位的出库记录数
- **分店筛选**: 按门店或分店筛选（多门店支持）
  - 下拉选择分店
  - 支持多分店选择
  - 权限控制分店范围
- **类型筛选**: 按出库类型筛选
  - 销售出库：正常销售流程
  - 调拨出库：仓库间内部调货
  - 借用出库：暂时借出，可能退还
  - 返修出库：售后服务流程的物料出库

**查询功能**:
- **单号查询**: 出库单号的精确查询
  - 支持完整单号搜索
  - 支持单号模糊匹配
  - 支持批量单号查询
- **日期范围查询**: 出库日期范围筛选
  - 起始日期选择器
  - 结束日期选择器
  - 快速日期选择（今天、本周、本月）
- **备注搜索**: 备注字段的模糊搜索
  - 支持关键词搜索
  - 支持多关键词组合
  - 不区分大小写匹配
- **电话查询**: 联系人电话的查询
  - 支持完整电话号码搜索
  - 支持电话号码部分匹配
  - 支持手机号段搜索

#### 3.2.2 筛选操作功能
**功能描述**: 筛选相关的操作和管理功能

**操作按钮**:
- **搜索按钮**: 执行筛选条件查询
  - 组合所有筛选条件
  - 实时更新数据表格
  - 显示筛选结果数量
- **清空按钮**: 清除所有筛选条件
  - 一键重置所有筛选
  - 恢复默认数据展示
  - 主动刷新表格为默认状态
- **高级筛选**: 更多筛选条件选项
  - 金额范围筛选
  - 商品类别筛选
  - 自定义字段筛选

### 3.3 出库单管理功能

#### 3.3.1 出库单新增功能
**功能描述**: 创建新的出库单据，支持多种出库类型和智能化的操作流程

**页面入口**:
- **主要入口**: 商品出库模块 → 点击【新增】按钮
- **快速入口**: 库存管理相关页面的快速出库链接
- **业务入口**: 销售订单、调拨申请等业务单据的出库操作

**功能架构**:
商品出库新增页面包含四个核心功能区域：
- **基础信息管理**: 出库单基础元数据的录入和管理
- **往来单位管理**: 客户、供应商等往来单位的选择和信息联动
- **商品选择管理**: 商品的选择、展示和出库信息录入
- **操作流程控制**: 出库单的保存、提交和流程控制

**基础信息管理**:
- **出库单号**: 系统生成唯一出库单编号
  - 自动生成（如：CK...）
  - 唯一性保证
  - 支持自定义编号规则
- **出库日期**: 当前出库日期
  - 默认当天日期
  - 支持手动调整
  - 日期格式验证
- **出库店**: 当前门店或仓库名称
  - 系统自动带出
  - 基于用户权限确定
  - 影响库存扣减范围
- **经手人**: 执行出库动作的用户
  - 默认当前账号
  - 支持下拉选择其他用户
  - 权限控制可选范围

**出库类型管理**:
- **销售出库**: 正常销售业务出库
  - 可关联销售单据
  - 需要结算金额
  - 影响销售收入统计
- **调拨出库**: 仓库间调拨出库
  - 需指定入库店
  - 不影响总库存
  - 生成调拨记录
- **借用出库**: 临时借用出库
  - 需设置预计归还时间
  - 生成借用记录
  - 支持归还跟踪
- **返厂出库**: 退换货返厂出库
  - 出库后生成退换货任务
  - 关联售后服务
  - 支持质量追溯

**往来单位管理功能**:
- **往来单位类型选择**: 选择往来单位的类型，控制搜索范围
  - 客户：销售业务的客户单位
  - 供应商：采购退货等业务的供应商
  - 经销商：渠道业务的经销商
  - 工程项目部：工程项目相关的内部单位
- **往来单位搜索选择**: 搜索和选择具体的往来单位
  - 弹窗选择器：点击搜索弹出单位选择器
  - 过滤显示：基于选择的类型过滤单位列表
  - 搜索功能：支持单位名称、编码搜索
  - 信息展示：显示单位名称、联系人、电话等信息
- **信息自动联动**: 选择往来单位后自动填充相关信息
  - 名称自动填充：自动填充往来单位名称
  - 联系电话自动填充：自动填充主联系人电话
  - 地址信息联动：自动填充单位地址信息
  - 历史记录关联：显示历史出库记录

**商品选择管理功能**:
- **商品选择器展示**: 右侧商品选择器的展示和交互
  - 商品缩略图：快速识别商品视觉形态
  - 编号品名：商品编码 + 中文名称（支持SN提示）
  - 计量单位：如"个"、"台"等计量单位
  - 当前库存：当前库存数量（重要参考）
  - 选择操作：点击商品后加入明细表
- **商品搜索功能**: 多种方式的商品搜索和定位
  - 扫码搜索：支持扫码枪快速定位商品
    - 条码识别
    - 自动匹配商品
    - 快速添加到明细
  - 手动搜索：商品名称、编码搜索
    - 模糊匹配
    - 实时搜索建议
    - 搜索结果高亮
- **库存可用性检查**: 检查商品库存的可用性
  - 库存数量显示：实时显示当前库存
  - 可用库存计算：扣除已预占库存
  - 库存不足提醒：库存不足时的提醒
  - 库存预警：接近安全库存的预警

**商品明细管理功能**:
- **明细信息展示**: 展示已选商品的详细信息
  - 商品图片：商品图标展示
  - 商品编码：唯一标识码
  - 商品品名：展示品名（含SN提示）
  - 型号规格：商品型号和规格属性
  - 计量单位：计量单位显示
  - 当前库存：库存数量参考
- **出库信息录入**: 录入商品的出库相关信息
  - 出库数量：用户需填写的核心字段
    - 默认为0，必须填写
    - 数值验证（非负数）
    - 库存充足性验证
    - 决定库存扣减数量
  - 商品备注：每件商品独立备注
    - 如"展示样品"、"配件缺失"
    - 支持常用备注选择
    - 便于出库后追溯
- **序列号管理**: 管理启用SN控制的商品序列号
  - SN标识显示：商品品名显示SN提示
  - 序列号录入：出库数量与序列号数量匹配
  - 序列号选择：从可用序列号中选择
  - 唯一性验证：确保序列号不重复使用

**数据验证功能**:
- **基础数据验证**: 验证出库单基础信息的完整性
  - 必填字段验证：往来单位、出库类型等必填
  - 格式验证：日期格式、数量格式等
  - 业务规则验证：出库类型与往来单位类型匹配
  - 权限验证：操作权限和数据权限验证
- **库存验证功能**: 验证库存的充足性和可用性
  - 库存充足性：出库数量不超过可用库存
  - 库存状态：验证商品库存状态正常
  - 预占库存：考虑已预占的库存数量
  - 安全库存：检查是否低于安全库存

**操作流程控制**:
- **保存功能**: 保存出库单并执行相关业务逻辑
  - 数据完整性验证：验证所有必填信息
  - 业务规则验证：验证业务逻辑规则
  - 出库单生成：生成正式出库单号
  - 库存预扣减：预扣减库存数量
  - 状态更新：更新出库单状态
- **取消功能**: 取消当前出库单编辑
  - 确认提示：确认是否取消当前操作
  - 数据清空：清空页面编辑数据
  - 返回列表：返回商品出库列表页面
- **返回功能**: 返回出库单列表页面
  - 数据保护：提醒保存未提交的数据
  - 页面跳转：返回出库单列表页面
  - 状态恢复：恢复列表页面状态

#### 3.3.2 出库单编辑功能
**功能描述**: 编辑未审核的出库单据

**编辑权限**:
- **状态限制**: 仅待审核状态可编辑
- **权限控制**: 需要出库编辑权限
- **经手人限制**: 主要允许经手人编辑

**编辑内容**:
- **往来单位变更**: 修改客户或单位信息
- **商品调整**: 增删改出库商品
- **数量调整**: 修改出库数量
- **库房变更**: 修改出库库房
- **备注更新**: 更新备注和说明信息

### 3.4 出库类型业务管理

#### 3.4.1 销售出库管理
**功能描述**: 正常销售流程的出库管理

**业务特点**:
- **库存减少**: 出库后永久减少库存
- **收入确认**: 与销售收入确认关联
- **客户管理**: 与客户档案关联
- **发票开具**: 支持销售发票开具

**流程控制**:
- **订单关联**: 与销售订单关联
- **价格确认**: 确认销售价格
- **收款状态**: 检查收款状态
- **发货确认**: 发货和物流确认

#### 3.4.2 调拨出库管理
**功能描述**: 仓库间内部调货的出库管理

**业务特点**:
- **内部调拨**: 不影响总库存，仅改变库存分布
- **成本转移**: 成本在不同库房间转移
- **无收入**: 不产生销售收入
- **双向记录**: 同时产生调出和调入记录

**流程控制**:
- **调拨申请**: 基于调拨申请单出库
- **目标库房**: 确认调入的目标库房
- **运输管理**: 调拨运输过程管理
- **接收确认**: 目标库房接收确认

#### 3.4.3 借用出库管理
**功能描述**: 暂时借出商品的出库管理

**业务特点**:
- **临时出库**: 暂时减少库存
- **预期归还**: 设置预期归还时间
- **借用跟踪**: 跟踪借用状态和归还情况
- **押金管理**: 可能涉及押金收取

**流程控制**:
- **借用申请**: 基于借用申请出库
- **归还期限**: 设置归还期限
- **逾期提醒**: 逾期归还提醒
- **归还处理**: 归还时的入库处理

#### 3.4.4 返修出库管理
**功能描述**: 售后服务流程的物料出库

**业务特点**:
- **售后服务**: 与售后服务单关联
- **更换出库**: 更换故障商品的出库
- **维修配件**: 维修用配件的出库
- **质保管理**: 与质保期管理关联

**流程控制**:
- **服务单关联**: 与售后服务单关联
- **故障确认**: 确认故障和维修方案
- **配件需求**: 确认维修配件需求
- **服务完成**: 服务完成确认

### 3.5 审核出库流程

#### 3.5.1 出库审核功能
**功能描述**: 出库单的审核和批准流程

**审核流程**:
- **审核权限验证**: 检查用户审核权限
- **单据状态检查**: 确认单据可审核状态
- **库存可用性检查**: 验证库存是否充足
- **审核信息录入**:
  - 审核意见填写
  - 审核结果选择（通过/拒绝）
  - 审核时间记录
- **审核结果处理**:
  - 通过：更新状态为已出库
  - 拒绝：返回待审核状态，记录拒绝原因

#### 3.5.2 库存自动扣减
**功能描述**: 审核通过后的库存自动处理

**扣减流程**:
- **库存检查**: 再次确认库存可用性
- **库存扣减**: 按出库数量扣减库存
- **成本核算**: 计算出库成本
- **库存记录**: 生成库存变动记录
- **出库确认**: 生成最终出库记录

**异常处理**:
- **库存不足**: 库存不足时的处理机制
- **扣减失败**: 库存扣减失败的回滚机制
- **数据一致性**: 确保数据一致性

### 3.6 数据统计分析

#### 3.6.1 出库统计功能
**功能描述**: 出库数据的统计和分析

**统计维度**:
- **按类型统计**: 各种出库类型的数量和金额统计
- **按时间统计**: 按日、周、月的出库趋势分析
- **按商品统计**: 各商品的出库数量和频次统计
- **按客户统计**: 各客户的出库数量和金额统计

**统计指标**:
- **出库数量**: 总出库数量统计
- **出库金额**: 总出库金额统计
- **出库频次**: 出库操作频次统计
- **平均单价**: 出库商品平均单价

#### 3.6.2 数据导出打印
**功能描述**: 出库数据的导出和打印功能

**导出功能**:
- **导出格式**: Excel、CSV、PDF格式导出
- **导出范围**: 当前筛选结果或全部数据
- **字段选择**: 自定义导出字段
- **数据权限**: 按权限控制导出内容

**打印功能**:
- **打印模板**: 标准出库单打印模板
- **批量打印**: 支持批量出库单打印
- **自定义打印**: 自定义打印格式和内容
- **打印预览**: 打印前预览功能

## 4. 用户界面设计

### 4.1 页面布局设计
- **顶部导航区**: 面包屑导航和视图切换标签
- **筛选搜索区**: 多维度筛选条件和操作按钮
- **数据展示区**: 出库单或出库明细数据表格
- **功能按钮区**: 每条记录的操作按钮

### 4.2 交互设计规范
- **标签页切换**: 清晰的视图切换设计
- **状态标识**: 直观的出库状态颜色和图标
- **操作确认**: 重要操作的确认弹窗
- **数据加载**: 加载状态和进度提示

### 4.3 响应式设计
- **PC端**: 完整功能展示，多列表格布局
- **平板端**: 适配中等屏幕，关键信息优先
- **移动端**: 卡片式布局，核心功能保留

## 5. 权限控制

### 5.1 功能权限
- **查看权限**: 出库数据查看权限
- **新增权限**: 出库单新增权限
- **编辑权限**: 出库单编辑权限
- **审核权限**: 出库单审核权限
- **删除权限**: 出库单删除权限

### 5.2 数据权限
- **门店权限**: 只能操作本门店出库数据
- **客户权限**: 按客户范围控制访问
- **商品权限**: 按商品类别控制出库范围

## 6. 异常处理

### 6.1 业务异常
- **库存不足**: 库存不足时的提醒和处理
- **审核异常**: 审核流程异常的处理
- **扣减异常**: 库存扣减失败的处理

### 6.2 系统异常
- **数据异常**: 出库数据异常的检测和修复
- **同步异常**: 数据同步失败的重试机制
- **权限异常**: 权限验证失败的处理

---

**文档版本**: v1.0
**编写日期**: 2025-07-02
**编写人员**: AI系统架构师
**审核状态**: 待审核
