<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.muzi.yichao.module.iot.dal.mysql.device.IotDeviceMapper">

    <select id="selectDeviceCountGroupByState" resultType="java.util.Map">
        SELECT 
            state AS `key`,
            COUNT(1) AS `value`
        FROM iot_device
        WHERE deleted = 0
        GROUP BY state
    </select>

    <select id="selectDeviceCountMapByProductId" resultType="java.util.Map">
        SELECT
            product_id AS `key`,
            COUNT(1) AS `value`
        FROM iot_device
        WHERE deleted = 0
        GROUP BY product_id
    </select>

</mapper>