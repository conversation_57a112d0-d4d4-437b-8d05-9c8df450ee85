CREATE TABLE IF NOT EXISTS `product_sku` (
    `id` bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    `spu_id` bigint NOT NULL COMMENT 'spu编号',
    `properties` varchar(512)  DEFAULT NULL COMMENT '属性数组，JSON 格式',
    `price` int NOT NULL DEFAULT '-1' COMMENT '商品价格，单位：分',
    `market_price` int DEFAULT NULL COMMENT '市场价，单位：分',
    `cost_price` int NOT NULL DEFAULT '-1' COMMENT '成本价，单位： 分',
    `bar_code` varchar(64)  DEFAULT NULL COMMENT 'SKU 的条形码',
    `pic_url` varchar(256)  NOT NULL COMMENT '图片地址',
    `stock` int DEFAULT NULL COMMENT '库存',
    `weight` double DEFAULT NULL COMMENT '商品重量，单位：kg 千克',
    `volume` double DEFAULT NULL COMMENT '商品体积，单位：m^3 平米',
    `sub_commission_first_price` int DEFAULT NULL COMMENT '一级分销的佣金，单位：分',
    `sub_commission_second_price` int DEFAULT NULL COMMENT '二级分销的佣金，单位：分',
    `sales_count` int DEFAULT NULL COMMENT '商品销量',
    "creator" varchar(64) DEFAULT '',
    "create_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updater" varchar(64) DEFAULT '',
    "update_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted" bit NOT NULL DEFAULT FALSE,
    "tenant_id" bigint not null default  '0',
    PRIMARY KEY("id")
) COMMENT '商品sku';

CREATE TABLE IF NOT EXISTS `product_spu` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '商品 SPU 编号，自增',
    `name` varchar(128) NOT NULL COMMENT '商品名称',
    `keyword` varchar(256) NOT NULL COMMENT '关键字',
    `introduction` varchar(256) NOT NULL COMMENT '商品简介',
    `description` text NOT NULL COMMENT '商品详情',
    `bar_code` varchar(64) NOT NULL COMMENT '条形码',
    `category_id` bigint NOT NULL COMMENT '商品分类编号',
    `brand_id` int DEFAULT NULL COMMENT '商品品牌编号',
    `pic_url` varchar(256) NOT NULL COMMENT '商品封面图',
    `slider_pic_urls` varchar(2000)  DEFAULT '' COMMENT '商品轮播图地址\n 数组，以逗号分隔\n 最多上传15张',
    `video_url` varchar(256) DEFAULT NULL COMMENT '商品视频',
    `unit` tinyint NOT NULL COMMENT '单位',
    `sort` int NOT NULL DEFAULT '0' COMMENT '排序字段',
    `status` tinyint NOT NULL COMMENT '商品状态: 0 上架（开启） 1 下架（禁用）-1 回收',
    `spec_type` bit(1) NOT NULL COMMENT '规格类型：0 单规格 1 多规格',
    `price` int NOT NULL DEFAULT '-1' COMMENT '商品价格，单位使用：分',
    `market_price` int NOT NULL COMMENT '市场价，单位使用：分',
    `cost_price` int NOT NULL DEFAULT '-1' COMMENT '成本价，单位： 分',
    `stock` int NOT NULL DEFAULT '0' COMMENT '库存',
    `delivery_template_id` bigint NOT NULL COMMENT '物流配置模板编号',
    `recommend_hot` bit(1) NOT NULL COMMENT '是否热卖推荐: 0 默认 1 热卖',
    `recommend_benefit` bit(1) NOT NULL COMMENT '是否优惠推荐: 0 默认 1 优选',
    `recommend_best` bit(1) NOT NULL COMMENT '是否精品推荐: 0 默认 1 精品',
    `recommend_new` bit(1) NOT NULL COMMENT '是否新品推荐: 0 默认 1 新品',
    `recommend_good` bit(1) NOT NULL COMMENT '是否优品推荐',
    `give_integral` int NOT NULL COMMENT '赠送积分',
    `give_coupon_template_ids` varchar(512)  DEFAULT '' COMMENT '赠送的优惠劵编号的数组',
    `sub_commission_type` bit(1) NOT NULL COMMENT '分销类型',
    `activity_orders` varchar(16) NOT NULL DEFAULT '' COMMENT '活动显示排序0=默认, 1=秒杀，2=砍价，3=拼团',
    `sales_count` int DEFAULT '0' COMMENT '商品销量',
    `virtual_sales_count` int DEFAULT '0' COMMENT '虚拟销量',
    `browse_count` int DEFAULT '0' COMMENT '商品点击量',
    "creator" varchar(64) DEFAULT '',
    "create_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updater" varchar(64) DEFAULT '',
    "update_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted" bit NOT NULL DEFAULT FALSE,
    "tenant_id" bigint not null default  '0',
    PRIMARY KEY("id")
) COMMENT '商品spu';

CREATE TABLE IF NOT EXISTS `product_category` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '分类编号',
    `parent_id` bigint NOT NULL COMMENT '父分类编号',
    `name` varchar(255) NOT NULL COMMENT '分类名称',
    `pic_url` varchar(255) NOT NULL COMMENT '移动端分类图',
    `big_pic_url` varchar(255) DEFAULT NULL COMMENT 'PC 端分类图',
    `sort` int DEFAULT '0' COMMENT '分类排序',
    `status` tinyint NOT NULL COMMENT '开启状态',
    "creator" varchar(64) DEFAULT '',
    "create_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updater" varchar(64) DEFAULT '',
    "update_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted" bit NOT NULL DEFAULT FALSE,
    "tenant_id" bigint not null default  '0',
    PRIMARY KEY("id")
) COMMENT '商品分类';

CREATE TABLE IF NOT EXISTS `product_brand` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '品牌编号',
    `name` varchar(255) NOT NULL COMMENT '品牌名称',
    `pic_url` varchar(255) NOT NULL COMMENT '品牌图片',
    `sort` int DEFAULT '0' COMMENT '品牌排序',
    `description` varchar(1024) DEFAULT NULL COMMENT '品牌描述',
    `status` tinyint NOT NULL COMMENT '状态',
    "creator" varchar(64) DEFAULT '',
    "create_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updater" varchar(64) DEFAULT '',
    "update_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted" bit NOT NULL DEFAULT FALSE,
    "tenant_id" bigint not null default  '0',
    PRIMARY KEY("id")
) COMMENT '商品品牌';

CREATE TABLE IF NOT EXISTS `product_property` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
    `name` varchar(64) DEFAULT NULL COMMENT '规格名称',
    `status` tinyint DEFAULT NULL COMMENT '状态： 0 开启 ，1 禁用',
    "creator" varchar(64) DEFAULT '',
    "create_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updater" varchar(64) DEFAULT '',
    "update_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted" bit NOT NULL DEFAULT FALSE,
    "tenant_id" bigint not null default  '0',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY("id")
) COMMENT '规格名称';

CREATE TABLE IF NOT EXISTS `product_property_value` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
    `property_id` bigint DEFAULT NULL COMMENT '规格键id',
    `name` varchar(128) DEFAULT NULL COMMENT '规格值名字',
    `status` tinyint DEFAULT NULL COMMENT '状态： 1 开启 ，2 禁用',
    "creator" varchar(64) DEFAULT '',
    "create_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updater" varchar(64) DEFAULT '',
    "update_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted" bit NOT NULL DEFAULT FALSE,
    "tenant_id" bigint not null default  '0',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY("id")
) COMMENT '规格值';

DROP TABLE IF EXISTS `product_comment` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '评论编号，主键自增',
    `user_id` bigint DEFAULT NULL COMMENT '评价人的用户编号关联 MemberUserDO 的 id 编号',
    `user_nickname` varchar(255) DEFAULT NULL COMMENT '评价人名称',
    `user_avatar` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '评价人头像',
    `anonymous` bit(1) DEFAULT NULL COMMENT '是否匿名',
    `order_id` bigint DEFAULT NULL COMMENT '交易订单编号关联 TradeOrderDO 的 id 编号',
    `order_item_id` bigint DEFAULT NULL COMMENT '交易订单项编号关联 TradeOrderItemDO 的 id 编号',
    `spu_id` bigint DEFAULT NULL COMMENT '商品 SPU 编号关联 ProductSpuDO 的 id',
    `spu_name` varchar(255) DEFAULT NULL COMMENT '商品 SPU 名称',
    `sku_id` bigint DEFAULT NULL COMMENT '商品 SKU 编号关联 ProductSkuDO 的 id 编号',
    `visible` bit(1) DEFAULT NULL COMMENT '是否可见true:显示false:隐藏',
    `scores` tinyint DEFAULT NULL COMMENT '评分星级1-5分',
    `description_scores` tinyint DEFAULT NULL COMMENT '描述星级1-5 星',
    `benefit_scores` tinyint DEFAULT NULL COMMENT '服务星级1-5 星',
    `content` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '评论内容',
    `pic_urls` varchar(4096) DEFAULT NULL COMMENT '评论图片地址数组',
    `reply_status` bit(1) DEFAULT NULL COMMENT '商家是否回复',
    `reply_user_id` bigint DEFAULT NULL COMMENT '回复管理员编号关联 AdminUserDO 的 id 编号',
    `reply_content` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '商家回复内容',
    `reply_time` datetime DEFAULT NULL COMMENT '商家回复时间',
    `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 26 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '商品评论';