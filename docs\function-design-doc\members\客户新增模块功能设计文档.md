# SaaS智能家装CRM系统 - 客户新增模块功能设计文档

## 1. 模块概述

### 1.1 模块目的
客户新增模块是SaaS智能家装CRM系统的基础模块，负责为企业添加新客户，建立完整的客户档案。该模块将原有的会员卡办理功能进行调整和扩展，实现客户信息的全面录入、账户创建和档案管理。

### 1.2 业务价值
- 建立标准化的客户信息录入流程，确保数据完整性和准确性
- 支持多门店客户信息统一管理，实现连锁经营的客户数据一致性
- 提供灵活的客户分类和标签管理，支持精准营销和客户分析
- 建立完善的客户推荐关系网络，支持客户转介绍业务发展
- 实现客户账户的初始化设置，为后续业务操作奠定基础

### 1.3 功能架构
客户新增模块包含三个核心功能：
- **客户信息录入**: 完整的客户基本信息、服务信息、个人信息录入
- **收货地址管理**: 客户收货地址的添加、编辑和管理
- **客户档案创建**: 信息验证、账户创建和档案提交

## 2. 客户新增模块操作流程图

```mermaid
flowchart TD
    A[用户点击新增客户] --> B[权限验证]
    B --> C{权限验证通过?}
    C -->|否| D[显示权限不足提示]
    C -->|是| E[显示客户新增表单]

    E --> F[第一步：基本信息录入]
    F --> G[门店信息自动填充]
    F --> H[客户类别选择]
    F --> I[来源渠道选择]
    F --> J[姓名输入]
    F --> K[手机号输入]
    F --> L[微信号输入]

    J --> M[姓名格式验证]
    K --> N[手机号格式验证]
    K --> O[手机号唯一性验证]

    M --> P{验证通过?}
    N --> P
    O --> P
    P -->|否| Q[显示错误提示]
    P -->|是| R[第二步：服务信息录入]

    R --> S[跟进人选择]
    R --> T[设计师选择]
    S --> U[默认当前用户]
    T --> V[可选择或留空]

    U --> W[第三步：个人信息录入]
    V --> W
    W --> X[性别选择]
    W --> Y[生日选择]
    W --> Z[证件信息录入]
    W --> AA[公司信息录入]

    AA --> BB[第四步：推荐关系设置]
    BB --> CC[推荐人选择]
    CC --> DD{是否有推荐人?}
    DD -->|是| EE[弹出客户选择窗口]
    DD -->|否| FF[跳过推荐人设置]

    EE --> GG[搜索现有客户]
    GG --> HH[选择推荐人]
    HH --> II[建立推荐关系]

    FF --> JJ[第五步：账户信息设置]
    II --> JJ
    JJ --> KK[发卡日期设置]
    JJ --> LL[到期日期设置]
    JJ --> MM[密码设置]
    JJ --> NN[确认密码]

    MM --> OO[密码强度验证]
    NN --> PP[密码一致性验证]
    OO --> QQ{验证通过?}
    PP --> QQ
    QQ -->|否| RR[显示密码错误提示]
    QQ -->|是| SS[第六步：特殊标识设置]

    SS --> TT[安装师傅标识]
    SS --> UU[客户类型选择]

    TT --> VV[第七步：收货地址管理]
    UU --> VV
    VV --> WW[添加收货地址]
    WW --> XX[地址信息录入]
    XX --> YY[地址验证]
    YY --> ZZ{地址验证通过?}
    ZZ -->|否| AAA[显示地址错误提示]
    ZZ -->|是| BBB[保存地址信息]

    BBB --> CCC[第八步：信息确认]
    CCC --> DDD[显示信息预览]
    DDD --> EEE[用户确认信息]
    EEE --> FFF{信息确认无误?}
    FFF -->|否| GGG[返回修改]
    FFF -->|是| HHH[提交客户信息]

    HHH --> III[数据验证]
    III --> JJJ[创建客户账户]
    JJJ --> KKK[生成客户编号]
    KKK --> LLL[保存客户档案]
    LLL --> MMM{保存成功?}
    MMM -->|否| NNN[显示保存失败提示]
    MMM -->|是| OOO[显示成功提示]
    OOO --> PPP[跳转客户列表]

    style A fill:#e1f5fe
    style C fill:#fff3e0
    style D fill:#ffebee
    style P fill:#fff3e0
    style Q fill:#ffebee
    style QQ fill:#fff3e0
    style RR fill:#ffebee
    style ZZ fill:#fff3e0
    style AAA fill:#ffebee
    style FFF fill:#fff3e0
    style MMM fill:#fff3e0
    style NNN fill:#ffebee
    style OOO fill:#e8f5e8
```

### 流程说明
客户新增模块采用分步骤的表单录入流程，确保数据完整性和用户体验：

1. **权限验证与表单初始化**：验证用户新增客户权限，初始化表单并自动填充门店等默认信息
2. **分步骤信息录入**：按照基本信息→服务信息→个人信息→推荐关系→账户信息→特殊标识→收货地址的顺序逐步录入
3. **实时数据验证**：每个步骤都进行相应的格式验证和业务规则验证，如手机号唯一性、密码强度等
4. **推荐关系建立**：支持选择现有客户作为推荐人，建立客户推荐关系网络
5. **信息确认与提交**：最后展示完整信息预览，用户确认后创建客户账户并生成客户档案
6. **异常处理机制**：各个环节都有相应的错误提示和重试机制，确保操作的可靠性

## 3. 详细功能设计

### 2.1 客户信息录入功能

#### 2.1.1 基本信息录入
**功能描述**: 录入客户的基础身份信息和联系方式

**必填字段**:
- **门店**: 默认显示当前登录用户所属门店信息
  - 系统自动获取当前用户门店信息
  - 支持管理员手动选择其他门店
  - 门店信息关联后续权限控制
- **类别**: 客户分类标识
  - 服务商客户：提供服务的合作伙伴
  - 品牌家装客户：品牌合作的装修客户
  - 个人客户：普通个人消费者
  - 企业客户：企业级客户
- **来源**: 客户了解渠道
  - 设计师推荐：通过设计师介绍
  - 大众点评：通过点评平台了解
  - 网络推广：通过网络广告了解
  - 朋友介绍：通过朋友推荐
  - 门店到访：直接到店咨询
  - 其他渠道：其他获客方式
- **姓名**: 客户真实姓名
  - 支持中文、英文姓名
  - 长度限制2-20个字符
  - 不允许特殊字符
- **手机号**: 客户联系电话
  - 11位手机号格式验证
  - 系统内唯一性验证
  - 支持国际手机号格式

**选填字段**:
- **微信号**: 微信联系方式
  - 支持微信号、手机号、QQ号格式
  - 用于微信营销和客户沟通

#### 2.1.2 服务信息录入
**功能描述**: 配置客户的服务团队和责任人信息

**跟进人设置**:
- **跟进人**: 负责服务客户的员工（必填）
  - 下拉框展示当前门店所有员工
  - 支持员工姓名搜索功能
  - 默认选择当前登录用户
  - 支持后续变更跟进人
- **设计师**: 为客户提供设计服务的设计师（选填）
  - 下拉框展示门店设计师列表
  - 支持设计师姓名搜索
  - 可以为空，后续分配

#### 2.1.3 个人信息录入
**功能描述**: 录入客户的详细个人信息

**个人详情字段**:
- **性别**: 男/女/未知（选填）
- **生日**: 客户生日信息（选填）
  - 日期选择器格式
  - 用于生日营销和客户关怀
- **证件类型**: 身份证/护照/其他（选填）
- **证件号**: 对应证件号码（选填）
  - 身份证号格式验证
  - 护照号格式验证
- **公司名称**: 客户所在公司（选填）
  - 用于企业客户管理
  - 支持公司信息关联

#### 2.1.4 推荐关系设置
**功能描述**: 建立客户推荐关系网络

**推荐人选择**:
- **推荐人**: 推荐该客户的现有客户（选填）
  - 点击选择弹出客户选择窗口
  - 展示所有已有客户列表
  - 支持按姓名、手机号搜索推荐人
  - 建立推荐关系链条
  - 用于推荐奖励计算

#### 2.1.5 账户信息设置
**功能描述**: 设置客户账户的基本信息

**账户配置**:
- **发卡日期**: 默认显示当前日期（可修改）
- **到期日期**: 客户有效期限（可选择）
  - 支持永久有效设置
  - 支持自定义到期时间
- **密码**: 客户登录密码（必填）
  - 密码强度验证
  - 最少8位，包含字母和数字
- **确认密码**: 密码确认（必填）
  - 与密码字段一致性验证

#### 2.1.6 特殊标识设置
**功能描述**: 设置客户的特殊身份标识

**标识选项**:
- **是否为安装师傅**: 勾选框标识
  - 标识该客户是否为安装服务人员
  - 影响后续服务流程和权限
- **客户类型**: 前装客户/后装客户/改造客户（必填）
  - 前装客户：新房装修客户
  - 后装客户：已装修房屋的客户
  - 改造客户：旧房改造客户

#### 2.1.7 财务信息设置
**功能描述**: 设置客户的初始财务信息

**财务配置**:
- **收款账户**: 门店收款账户选择（必填）
  - 下拉显示门店收款账户列表
  - 关联财务结算流程
- **初始存款**: 客户初始充值金额（选填，默认0）
  - 数值验证，不允许负数
  - 支持小数点后两位
- **红包**: 赠送的红包金额（选填，默认0）
  - 营销活动赠送金额
  - 有使用期限和规则限制
- **初始积分**: 客户初始积分（选填，默认0）
  - 整数验证
  - 用于积分营销体系

#### 2.1.8 其他信息录入
**功能描述**: 录入其他补充信息

**补充信息**:
- **app账号**: 关联的APP账号（选填）
  - 用于移动端账户关联
- **发票**: 是否需要开具发票（选填）
  - 影响后续开票流程
- **备注信息**: 其他补充说明（选填）
  - 自由文本输入
  - 最多500字符

### 2.2 收货地址管理功能

#### 2.2.1 地址信息录入
**功能描述**: 为客户添加详细的收货地址信息

**地址字段**:
- **收货人姓名**: 收货联系人姓名
  - 可以与客户姓名不同
  - 支持中英文姓名
- **联系电话**: 收货联系电话
  - 11位手机号验证
  - 支持座机号码格式
- **省市区选择**: 三级联动地址选择
  - 省份下拉选择
  - 城市联动更新
  - 区县联动更新
- **详细地址**: 具体收货地址
  - 街道、门牌号等详细信息
  - 最多200字符
- **邮政编码**: 地址对应邮政编码
  - 6位数字验证
  - 可选填写
- **地址标签**: 地址类型标识
  - 家：家庭住址
  - 公司：公司地址
  - 其他：其他类型地址

#### 2.2.2 地址管理操作
**功能描述**: 提供地址的增删改查操作

**管理功能**:
- **添加地址**: 支持添加多个收货地址
  - 无数量限制
  - 每个地址独立管理
- **设置默认**: 设置默认收货地址
  - 只能有一个默认地址
  - 新设置默认会取消原默认
- **编辑地址**: 修改已有地址信息
  - 支持所有字段修改
  - 修改后重新验证
- **删除地址**: 删除不需要的地址
  - 确认删除操作
  - 默认地址删除需重新设置
- **地址验证**: 地址信息的格式验证
  - 必填字段完整性检查
  - 地址格式合理性验证

### 2.3 客户档案创建功能

#### 2.3.1 信息验证流程
**功能描述**: 在提交前对所有录入信息进行验证

**验证规则**:
- **必填字段验证**: 检查所有必填字段是否完整
  - 门店、类别、来源、姓名、手机号、跟进人
  - 收款账户、密码、确认密码、客户类型
- **格式验证**: 检查字段格式是否正确
  - 手机号11位数字格式
  - 身份证号18位格式（如填写）
  - 邮箱格式验证（如填写）
- **唯一性验证**: 检查关键字段的唯一性
  - 手机号在系统中的唯一性
  - 身份证号的唯一性（如填写）
- **密码强度验证**: 检查密码是否符合安全要求
  - 最少8位字符
  - 包含字母和数字
  - 密码与确认密码一致
- **数值验证**: 检查数值字段的合理性
  - 金额字段非负数验证
  - 积分字段整数验证

#### 2.3.2 客户编号生成
**功能描述**: 为新客户生成唯一的客户编号

**编号规则**:
- **编号格式**: 门店代码 + 年月 + 流水号
  - 例：SH202507001（上海门店2025年7月第1个客户）
- **唯一性保证**: 系统自动生成，确保唯一性
- **流水号管理**: 按门店和月份独立计数

#### 2.3.3 档案保存流程
**功能描述**: 将验证通过的客户信息保存到系统

**保存步骤**:
1. **事务开始**: 开启数据库事务
2. **客户信息保存**: 保存客户基本信息
3. **地址信息保存**: 保存收货地址信息
4. **账户创建**: 创建客户登录账户
5. **财务账户初始化**: 初始化客户财务账户
6. **推荐关系建立**: 建立推荐关系记录
7. **操作日志记录**: 记录创建操作日志
8. **事务提交**: 提交所有数据变更

#### 2.3.4 成功反馈机制
**功能描述**: 客户创建成功后的反馈和后续操作

**反馈内容**:
- **成功提示**: 显示客户创建成功消息
- **客户编号**: 展示生成的客户编号
- **后续操作**: 提供后续操作选项
  - 继续添加客户
  - 查看客户详情
  - 返回客户列表

## 3. 用户界面设计

### 3.1 页面布局设计
- **顶部导航**: 面包屑导航和页面标题
- **主表单区域**: 分步骤的信息录入表单
- **侧边栏**: 操作提示和帮助信息
- **底部操作**: 保存、取消、重置按钮

### 3.2 交互设计规范
- **分步骤录入**: 将复杂表单分为多个步骤
- **实时验证**: 字段失焦时进行格式验证
- **智能提示**: 提供输入建议和格式提示
- **进度指示**: 显示当前录入进度

### 3.3 响应式设计
- **PC端**: 多列布局，充分利用屏幕空间
- **平板端**: 两列布局，适配中等屏幕
- **移动端**: 单列布局，优化触控操作

## 4. 权限控制

### 4.1 操作权限
- **系统管理员**: 可以为任意门店添加客户
- **门店经理**: 只能为本门店添加客户
- **销售人员**: 可以添加客户，默认自己为跟进人
- **客服人员**: 只读权限，不能添加客户

### 4.2 数据权限
- **门店隔离**: 只能查看和操作本门店客户
- **跟进人权限**: 销售人员主要操作自己跟进的客户

## 5. 异常处理

### 5.1 输入异常
- **格式错误**: 提供明确的格式要求提示
- **重复数据**: 提示手机号已存在，建议查找现有客户
- **网络异常**: 提供重试机制和离线保存

### 5.2 系统异常
- **保存失败**: 提示保存失败原因，保留用户输入
- **服务异常**: 提供友好的错误提示和联系方式

---

**文档版本**: v1.0
**编写日期**: 2025-07-01
**编写人员**: AI系统架构师
**审核状态**: 待审核
