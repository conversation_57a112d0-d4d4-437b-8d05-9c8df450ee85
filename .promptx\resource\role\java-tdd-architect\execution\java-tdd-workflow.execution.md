<execution>
  <constraint>
    ## 技术栈约束
    - 必须使用Java 17+和Spring Boot 3.x
    - 测试框架：JUnit 5 + Mockito + AssertJ
    - 构建工具：Maven
    - 代码质量：SonarQube/JaCoCo，覆盖率≥90%
    - 开发效率：Lombok注解简化代码
    
    ## TDD原则约束
    - 严格遵循红-绿-重构循环
    - 必须先写测试再实现功能
    - 测试必须初始失败才能验证有效性
    - 分层架构：控制器-服务-仓库分层
  </constraint>
  
  <rule>
    ## 测试代码规则
    - 测试命名：[被测方法]_[测试条件]_[预期结果]
    - 测试结构：given-when-then模式
    - 测试组织：按被测类功能分组
    - 外部依赖：使用Mockito正确隔离
    - 断言优化：使用AssertJ流式断言
    
    ## 实现代码规则
    - 最小实现：编写最简单的代码使测试通过
    - 代码质量：满足SOLID原则、清晰命名、适当注释
    - 错误处理：全面的异常处理与日志记录
    - 重构时机：在测试通过后进行代码重构
  </rule>
  
  <guideline>
    ## 工作流程指南
    - 从XMind测试用例开始分析
    - 识别测试层次和依赖关系
    - 生成测试矩阵确保覆盖完整
    - 优先处理核心功能测试
    - 重点关注边界条件和异常场景
    
    ## 质量保障指南
    - 每次代码修改后自动运行测试
    - 立即修复任何测试失败
    - 按测试用例优先级迭代实现
    - 确保CI管道中测试全部通过
  </guideline>
  
  <process>
    ## 1. 测试用例分析流程
    
    ```mermaid
    flowchart TD
        A[导入XMind测试用例] --> B[提取测试结构]
        B --> C[识别测试层次和优先级]
        C --> D[检测测试依赖关系]
        D --> E[生成测试矩阵]
        E --> F[确认覆盖率]
    ```
    
    **具体步骤：**
    - 分析XMind文件识别关键功能点
    - 提取测试场景和验证点
    - 标记测试优先级和依赖关系
    - 生成功能点与测试场景映射表
    
    ## 2. 测试代码编写流程
    
    ```mermaid
    flowchart TD
        A[编写失败测试] --> B[验证测试失败]
        B --> C[given-when-then结构]
        C --> D[配置Mock依赖]
        D --> E[AssertJ断言]
        E --> F[参数化测试优化]
    ```
    
    **测试代码模板：**
    ```java
    @Test
    void methodName_testCondition_expectedResult() {
        // given
        var input = createTestInput();
        when(mockService.method()).thenReturn(expectedResult);
        
        // when
        var result = serviceUnderTest.method(input);
        
        // then
        assertThat(result).isEqualTo(expectedResult);
    }
    ```
    
    ## 3. 功能实现流程
    
    ```mermaid
    flowchart TD
        A[最小实现] --> B[运行测试]
        B --> C{测试通过?}
        C -->|否| D[修复代码]
        C -->|是| E[代码重构]
        D --> B
        E --> F[再次验证]
        F --> G[提交代码]
    ```
    
    **实现原则：**
    - 编写最简单的代码使测试通过
    - 严格遵循分层架构设计
    - 满足SOLID原则和清晰命名
    - 在测试通过后进行重构优化
    
    ## 4. 验证与交付流程
    
    ```mermaid
    flowchart TD
        A[运行完整测试套件] --> B[检查覆盖率≥90%]
        B --> C[SonarQube质量检测]
        C --> D[生成测试报告]
        D --> E[更新文档]
        E --> F[提交代码]
        F --> G[CI管道验证]
    ```
    
    **交付标准：**
    - 提交规范：[测试ID]-[功能点]-[实现/修复/重构]
    - 更新README.md反映当前进度
    - 生成测试覆盖率报告
    - 确保CI管道全部通过
  </process>
  
  <criteria>
    ## 质量评价标准
    
    ### 测试质量
    - ✅ 测试覆盖率≥90%
    - ✅ 所有测试用例通过
    - ✅ 边界条件和异常场景覆盖
    - ✅ 测试代码可读性和维护性
    
    ### 代码质量
    - ✅ 符合SOLID设计原则
    - ✅ 清晰的命名和适当注释
    - ✅ 完善的异常处理
    - ✅ SonarQube质量检测通过
    
    ### 架构质量
    - ✅ 分层架构清晰
    - ✅ 依赖注入正确配置
    - ✅ 外部依赖正确隔离
    - ✅ 配置管理规范
    
    ### 交付质量
    - ✅ 提交信息规范
    - ✅ 文档更新及时
    - ✅ CI管道稳定通过
    - ✅ 测试报告完整
  </criteria>
</execution>