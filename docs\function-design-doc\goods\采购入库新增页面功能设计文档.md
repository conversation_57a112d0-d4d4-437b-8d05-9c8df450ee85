# 商品管理模块 - 采购入库新增页面功能设计文档

## 1. 页面概述

### 1.1 页面目的
采购入库新增页面是智能家装管理平台商品管理系统的核心业务页面，负责创建新的采购入库单据，支持商品选择、数量设置、采购信息录入等完整的采购单创建流程。该页面通过直观的界面设计和智能化的操作流程，提升采购效率和数据准确性。

### 1.2 业务价值
- 提供便捷的采购单创建功能，支持快速响应库存预警和补货需求
- 实现采购信息的标准化录入，确保采购数据的完整性和准确性
- 支持多种商品录入方式，满足不同场景下的采购需求
- 建立采购与库存的有效联动，实现智能化的库存补充
- 提供完整的采购流程控制，确保采购业务的规范性

### 1.3 页面入口
- **主要入口**：采购入库模块 → 点击【新增】按钮
- **智能入口**：库存预警模块 → 点击【去采购】后跳转（自动带入预警商品）
- **快速入口**：商品管理相关页面的快速采购链接

### 1.4 功能架构
采购入库新增页面包含四个核心功能区域：
- **基础信息管理**：采购单元数据的录入和管理
- **商品选择录入**：多种方式的商品选择和信息录入
- **明细信息管理**：已选商品的明细展示和编辑
- **操作流程控制**：采购单的保存、提交和流程控制

## 2. 采购入库新增页面操作流程图

```mermaid
flowchart TD
    A[用户访问采购入库新增页面] --> B[权限验证]
    B --> C{权限验证通过?}
    C -->|否| D[显示权限不足提示]
    C -->|是| E[页面初始化]
    
    E --> F[加载基础信息区]
    E --> G[加载商品选择区]
    E --> H[初始化明细表格]
    E --> I[显示操作按钮]
    
    F --> J[自动填充默认信息]
    J --> K[门店信息]
    J --> L[当前日期]
    J --> M[采购单号生成]
    
    G --> N[商品录入方式选择]
    N --> O[扫码录入]
    N --> P[手动选择]
    N --> Q[导入云单]
    
    O --> R[扫描商品条码]
    R --> S[商品信息识别]
    S --> T{商品识别成功?}
    T -->|否| U[显示识别失败提示]
    T -->|是| V[添加到明细表格]
    
    P --> W[打开商品选择器]
    W --> X[商品列表展示]
    X --> Y[商品搜索筛选]
    X --> Z[商品信息查看]
    Z --> AA[点击选择商品]
    AA --> V
    
    Q --> BB[云单导入界面]
    BB --> CC[选择云端采购单]
    CC --> DD[导入商品信息]
    DD --> V
    
    V --> EE[明细表格更新]
    EE --> FF[商品信息展示]
    FF --> GG[商品图片]
    FF --> HH[商品编码]
    FF --> II[商品名称]
    FF --> JJ[SN标识]
    FF --> KK[计量单位]
    
    EE --> LL[采购信息录入]
    LL --> MM[采购数量输入]
    LL --> NN[入库位设置]
    LL --> OO[商品备注]
    
    MM --> PP{采购数量验证}
    PP -->|数量≤0| QQ[显示数量错误提示]
    PP -->|数量>0| RR[数量验证通过]
    
    F --> SS[供应商信息管理]
    SS --> TT[供应商选择]
    TT --> UU[供应商信息联动]
    UU --> VV[负责人信息自动填充]
    UU --> WW[联系电话自动填充]
    UU --> XX[欠款信息自动计算]
    
    F --> YY[结算信息管理]
    YY --> ZZ[结算账户选择]
    YY --> AAA[实付金额录入]
    YY --> BBB[备注信息录入]
    
    I --> CCC[操作按钮功能]
    CCC --> DDD[保存草稿]
    CCC --> EEE[提交采购单]
    CCC --> FFF[取消操作]
    CCC --> GGG[返回列表]
    
    DDD --> HHH{数据验证}
    HHH -->|验证失败| III[显示验证错误]
    HHH -->|验证通过| JJJ[保存采购单草稿]
    
    EEE --> KKK{完整性验证}
    KKK -->|信息不完整| LLL[显示必填项提示]
    KKK -->|信息完整| MMM[提交采购单]
    MMM --> NNN[生成采购单号]
    NNN --> OOO[更新采购状态]
    OOO --> PPP[发送提交通知]
    
    FFF --> QQQ{确认取消操作?}
    QQQ -->|否| RRR[继续编辑]
    QQQ -->|是| SSS[清空页面数据]
    SSS --> TTT[返回采购列表]
    
    GGG --> TTT
    
    style A fill:#e1f5fe
    style C fill:#fff3e0
    style D fill:#ffebee
    style T fill:#fff3e0
    style U fill:#ffebee
    style PP fill:#fff3e0
    style QQ fill:#ffebee
    style HHH fill:#fff3e0
    style III fill:#ffebee
    style KKK fill:#fff3e0
    style LLL fill:#ffebee
    style QQQ fill:#fff3e0
    style PPP fill:#e8f5e8
```

### 流程说明
采购入库新增页面的操作流程主要包含以下几个核心环节：

1. **权限验证与页面初始化**：验证用户采购权限，初始化页面各功能区域
2. **基础信息自动填充**：自动填充门店、日期、采购单号等默认信息
3. **多方式商品录入**：支持扫码、手动选择、云单导入三种商品录入方式
4. **商品信息验证**：验证商品识别、数量输入、重复添加等业务规则
5. **供应商信息联动**：选择供应商后自动联动相关信息
6. **采购单保存提交**：支持草稿保存和正式提交，包含完整的数据验证

## 3. 详细功能设计

### 3.1 基础信息管理功能

#### 3.1.1 采购单基础信息
**功能描述**：管理采购单的基础元数据信息

**基础信息字段**：
- **门店信息**：下拉选择采购归属组织/部门
  - 手动选择门店
  - 权限控制可选门店范围
  - 影响后续仓库选择范围
- **仓库信息**：下拉选择商品最终入库位置
  - 基于门店筛选可选仓库
  - 显示仓库当前状态
  - 支持仓库容量提醒
- **采购单号**：采购单的唯一标识
  - 系统自动生成（推荐）
  - 支持手动修改
  - 唯一性验证
- **采购日期**：单据创建时间
  - 默认当前日期
  - 支持手动调整
  - 日期格式验证

#### 3.1.2 供应商信息管理
**功能描述**：管理采购相关的供应商信息

**供应商信息**：
- **供应商选择**：弹窗选择供应商
  - 供应商列表展示
  - 支持搜索和筛选
  - 显示供应商基本信息
- **联动信息自动填充**：
  - 负责人姓名自动带出
  - 联系电话自动填充
  - 欠款信息自动计算显示
- **结算信息**：
  - 结算账户下拉选择
  - 实付金额录入（默认0）
  - 支持多种结算方式

#### 3.1.3 备注信息管理
**功能描述**：记录采购相关的说明信息

**备注功能**：
- **采购备注**：多行文本输入
  - 支持常用备注模板
  - 如"快速补货"、"协议采购"等
  - 字符长度限制
- **操作标识**：系统行为提示
  - 根据来源显示不同标签
  - 如"采购建单"、"引用采购计划"等
  - 便于追溯采购来源

### 3.2 商品选择录入功能

#### 3.2.1 扫码录入功能
**功能描述**：通过扫描商品条码快速录入商品

**扫码功能**：
- **扫码输入框**：页面右上角扫码录入区域
- **条码识别**：自动识别商品条码信息
- **商品匹配**：匹配系统中的商品信息
- **快速添加**：识别成功后自动添加到明细表格
- **错误处理**：识别失败时提供友好提示

#### 3.2.2 手动选择功能
**功能描述**：从商品列表中手动选择商品

**选择功能**：
- **商品选择器**：右侧商品选择面板
- **商品展示**：图片 + 编码 + 品名 + SN标识 + 当前库存
- **搜索筛选**：支持商品名称、编码搜索
- **分类筛选**：按商品分类筛选
- **库存参考**：显示当前库存数量
- **点击选择**：点击商品后添加到明细表格

#### 3.2.3 云单导入功能
**功能描述**：从云平台或外部系统导入采购单信息

**导入功能**：
- **云单选择**：选择云端采购单或外部单据
- **信息预览**：导入前预览商品信息
- **批量导入**：一次性导入多个商品
- **信息校验**：导入后验证商品信息完整性
- **冲突处理**：处理重复商品和信息冲突

### 3.3 明细信息管理功能

#### 3.3.1 商品明细展示
**功能描述**：展示已选商品的详细信息

**明细字段**：
- **商品图片**：商品缩略图展示
- **商品编码**：系统唯一标识编号
- **商品名称**：带SN标识，提示序列号需求
- **计量单位**：个/台/套等计量单位
- **当前库存**：显示当前库存数量（参考）
- **上次采购价**：显示历史采购价格（参考）

#### 3.3.2 采购信息录入
**功能描述**：录入商品的采购相关信息

**录入信息**：
- **采购数量**：当前录入采购数量
  - 数值输入验证
  - 非负数校验
  - 支持键盘快速录入
- **入库位置**：指定入库货架或区域
  - 下拉选择库位
  - 支持新建库位
  - 库位容量提醒
- **商品备注**：该商品特定说明
  - 如"展品"、"无包装"等
  - 支持常用备注选择

#### 3.3.3 序列号管理
**功能描述**：管理启用SN控制的商品序列号

**SN管理**：
- **SN标识显示**：商品名称显示SN标识
- **序列号录入**：采购数量与序列号数量匹配
- **唯一性验证**：序列号唯一性检查
- **批量录入**：支持批量序列号录入

### 3.4 数据验证功能

#### 3.4.1 基础数据验证
**功能描述**：验证采购单基础信息的完整性

**验证规则**：
- **必填字段验证**：门店、仓库、供应商等必填
- **格式验证**：日期格式、金额格式等
- **业务规则验证**：采购数量非负、重复商品检查
- **权限验证**：操作权限和数据权限验证

#### 3.4.2 商品信息验证
**功能描述**：验证商品信息的准确性

**验证内容**：
- **商品存在性**：验证商品在系统中存在
- **商品状态**：验证商品启用状态
- **数量合理性**：验证采购数量合理性
- **重复检查**：同一商品不重复添加
- **SN匹配**：序列号数量与采购数量匹配

### 3.5 操作流程控制

#### 3.5.1 保存功能
**功能描述**：保存采购单草稿

**保存流程**：
- **数据验证**：基础数据完整性验证
- **草稿保存**：保存为草稿状态
- **自动保存**：定时自动保存功能
- **保存提示**：保存成功/失败提示

#### 3.5.2 提交功能
**功能描述**：正式提交采购单

**提交流程**：
- **完整性验证**：所有必填信息完整性检查
- **业务规则验证**：业务逻辑规则验证
- **采购单生成**：生成正式采购单号
- **状态更新**：更新采购单状态
- **通知发送**：发送提交成功通知

#### 3.5.3 取消功能
**功能描述**：取消当前采购单编辑

**取消流程**：
- **确认提示**：确认是否取消当前操作
- **数据清空**：清空页面编辑数据
- **返回列表**：返回采购入库列表页面

## 4. 用户界面设计

### 4.1 页面布局设计
- **顶部基础信息区**：采购单基础信息录入
- **商品选择录入区**：多种商品录入方式
- **明细信息表格区**：已选商品明细展示
- **底部操作按钮区**：保存、提交、取消等操作

### 4.2 交互设计规范
- **智能填充**：自动填充默认信息，减少用户输入
- **实时验证**：输入过程中实时验证和提示
- **友好提示**：清晰的错误提示和操作指导
- **快捷操作**：支持键盘快捷键和批量操作

### 4.3 响应式设计
- **PC端**：完整功能展示，多区域布局
- **平板端**：适配中等屏幕，关键功能优先
- **移动端**：简化布局，核心功能保留

## 5. 权限控制

### 5.1 功能权限
- **页面访问权限**：采购入库新增页面访问权限
- **商品选择权限**：商品选择和录入权限
- **供应商选择权限**：供应商信息查看和选择权限
- **采购单提交权限**：采购单正式提交权限

### 5.2 数据权限
- **门店权限**：只能为有权限的门店创建采购单
- **仓库权限**：只能选择有权限的仓库
- **供应商权限**：只能选择有合作关系的供应商
- **商品权限**：只能采购有权限的商品类别

## 6. 异常处理

### 6.1 业务异常
- **商品识别失败**：扫码或选择商品失败的处理
- **数据验证失败**：输入数据不符合业务规则的处理
- **重复商品处理**：重复添加商品的提示和处理
- **库存不足提醒**：商品库存不足的提醒和建议

### 6.2 系统异常
- **网络异常**：网络连接异常的处理和重试
- **数据保存失败**：数据保存失败的处理和恢复
- **权限异常**：权限验证失败的友好提示
- **系统错误**：系统错误的异常处理和用户提示

---

**文档版本**：v1.0  
**编写日期**：2025-07-02  
**编写人员**：AI系统架构师  
**审核状态**：待审核
