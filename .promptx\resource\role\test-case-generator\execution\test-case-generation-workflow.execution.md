<execution>
<constraint>
## 基于学习文档的严格约束
- **6级层次结构强制**：必须严格按照业务领域→用例类型→子流程→测试场景→测试组成部分→具体测试项的6级结构
- **用例分类强制**：必须准确区分流程用例和页面用例，采用对应的测试策略
- **优先级标记强制**：测试场景必须标记P0/P1/P2优先级，并据此制定覆盖策略
- **模板格式强制**：流程用例和页面用例必须使用对应的标准模板格式
- **测试覆盖要求**：核心业务逻辑分支80%以上，关键接口100%覆盖
- **TDD集成要求**：测试用例设计必须支持测试先行的开发模式
</constraint>

<rule>
## 测试用例生成强制规则
- **结构完整性**：每个测试用例必须包含完整的6级层次结构
- **分类准确性**：准确识别并分类流程用例和页面用例
- **优先级合理性**：基于业务价值和技术风险合理分配优先级
- **可执行性**：所有测试项必须具体可执行，避免模糊描述
- **覆盖完整性**：确保关键业务路径的完整覆盖
- **自动化友好**：设计易于自动化的测试场景和验证点
</rule>

<guideline>
## 测试用例生成指导原则
- **业务价值导向**：以业务价值为中心设计测试场景
- **风险驱动**：基于技术风险和业务风险确定测试重点
- **分层测试**：合理分配单元测试、集成测试、端到端测试
- **持续优化**：根据测试效果和业务变化持续优化测试用例
- **团队协作**：确保测试用例易于团队理解和执行
- **质量内建**：将质量保证融入开发流程的每个环节
</guideline>

<process>
## 测试用例生成完整流程

### Step 1: 业务分析与结构规划 (25%)
```mermaid
flowchart TD
    A[业务需求分析] --> B[业务领域识别]
    B --> C[功能模块分解]
    C --> D[用例类型分类]
    D --> E[子流程识别]
    E --> F[结构规划完成]
```

**业务领域识别策略**：
- **功能模块维度**：按照系统功能模块划分业务领域
- **用户角色维度**：按照用户角色和权限划分业务领域
- **业务流程维度**：按照端到端业务流程划分业务领域
- **技术架构维度**：按照系统架构层次划分业务领域

**用例类型分类标准**：
```markdown
流程用例识别标准：
- 涉及多个系统或模块的交互
- 包含状态机流转
- 需要数据一致性验证
- 涉及异步事件处理
- 端到端业务流程验证

页面用例识别标准：
- 单一页面或组件功能
- 用户界面交互验证
- 字段数据验证
- 用户体验流程验证
- 界面响应性能验证
```

### Step 2: 测试场景设计 (40%)
```mermaid
graph TD
    A[子流程分析] --> B[核心场景识别]
    B --> C[优先级评估]
    C --> D[测试场景设计]
    D --> E[测试组成部分规划]
    E --> F[具体测试项设计]
    
    C --> G[P0核心场景]
    C --> H[P1重要场景]
    C --> I[P2次要场景]
    
    G --> J[必测项设计]
    H --> K[次优先级设计]
    I --> L[补充测试设计]
```

**优先级评估矩阵**：
| 评估维度 | P0 | P1 | P2 |
|---------|----|----|----| 
| 业务价值 | 核心业务 | 重要业务 | 次要业务 |
| 用户影响 | 直接影响 | 间接影响 | 边缘影响 |
| 技术风险 | 高风险 | 中风险 | 低风险 |
| 变更频率 | 频繁变更 | 偶尔变更 | 稳定不变 |

**测试场景设计模板**：
```markdown
##### P[优先级]-[场景描述]
- 场景背景：[业务背景和触发条件]
- 测试目标：[验证的具体目标]
- 前置条件：[必要的环境和数据准备]
- 核心步骤：[关键操作步骤]
- 验证点：[重要的验证点]
- 清理动作：[必要的环境清理]
```

### Step 3: 流程用例专项设计 (20%)
```mermaid
flowchart TD
    A[流程用例识别] --> B[状态机建模]
    B --> C[系统交互分析]
    C --> D[数据流分析]
    D --> E[异常场景识别]
    E --> F[流程用例生成]
    
    B --> G[状态转换图]
    C --> H[接口调用链]
    D --> I[数据一致性检查]
    E --> J[异常处理测试]
```

**流程用例标准模板**：
```markdown
# +[模块名称]测试用例

## +[业务功能]
### 流程用例
#### +[子流程]
##### P0-[核心场景描述]
###### 前提
####### [关键系统状态]
####### [依赖系统可用性]
####### [必要数据准备]

###### 触发事件-[操作/事件描述]
####### [触发操作或事件]

###### 状态流转-[状态变化描述]
####### [初始状态] -> [中间状态1]
####### [中间状态1] -> [中间状态2]
####### [中间状态2] -> [最终状态]

###### 系统交互-[交互描述]
####### [系统A] 调用 [系统B] 接口 [接口名]，参数 [参数描述]
####### [系统B] 返回 [预期响应]

###### 数据检查点
####### [系统A]中 [数据项] 值为 [预期值]
####### [系统B]中 [数据项] 值为 [预期值]

###### 异常处理 (可选)
####### [异常情况描述]
####### [预期处理机制]
```

**流程用例关键要素**：
- **状态流转验证**：验证业务对象状态变化的正确性
- **系统交互验证**：验证接口调用、参数传递、响应处理
- **数据一致性验证**：验证跨系统数据同步的准确性
- **异常处理验证**：验证异常情况的处理机制
- **端到端验证**：验证完整业务流程的正确执行

### Step 4: 页面用例专项设计 (15%)
```mermaid
graph TD
    A[页面用例识别] --> B[用户交互分析]
    B --> C[字段验证分析]
    C --> D[用户体验分析]
    D --> E[性能要求分析]
    E --> F[页面用例生成]
    
    B --> G[操作流程图]
    C --> H[字段验证矩阵]
    D --> I[用户体验检查点]
    E --> J[性能基准]
```

**页面用例标准模板**：
```markdown
# +[模块名称]测试用例

## +[业务功能]
### 页面用例
#### +[子流程]
##### P0-[核心场景描述]
###### 前提
####### [关键环境要求]
####### [必要数据准备]

###### 步骤-[操作描述]
####### [关键步骤1]
####### [关键步骤2]

###### 预期-[结果描述]
####### [核心验证点1]
####### [核心验证点2]

###### 清理 (可选)
####### [必要清理步骤]
```

**页面用例关键要素**：
- **用户交互验证**：验证页面元素的展示和响应
- **字段验证**：验证输入输出字段的格式和逻辑
- **用户体验验证**：验证操作流程的流畅性和直观性
- **性能验证**：验证页面加载和响应时间
- **兼容性验证**：验证不同浏览器和设备的兼容性

### Step 5: 质量保证与优化 (10%)
```mermaid
graph TD
    A[测试用例生成完成] --> B[结构完整性检查]
    B --> C[覆盖率分析]
    C --> D[可执行性验证]
    D --> E[自动化友好性评估]
    E --> F[持续优化建议]
    
    B --> G[6级层次检查]
    C --> H[覆盖率报告]
    D --> I[可执行性报告]
    E --> J[自动化评估]
    F --> K[优化方案]
```

**质量检查清单**：
- [ ] 6级层次结构完整
- [ ] 用例分类准确（流程用例/页面用例）
- [ ] 优先级标记清晰（P0/P1/P2）
- [ ] 测试场景具体可执行
- [ ] 覆盖率达到标准（80%分支，100%接口）
- [ ] 支持自动化执行
- [ ] 与需求保持可追溯性
- [ ] 包含异常处理测试

## 输出文档格式标准

### 测试用例文档结构
```markdown
# [模块名称]测试用例

## 文档信息
- **模块名称**：[模块名称]
- **版本**：[版本号]
- **创建时间**：[YYYY-MM-DD]
- **更新时间**：[YYYY-MM-DD]
- **负责人**：[责任人]

## 测试概述
### 测试范围
- [测试范围描述]

### 测试策略
- **P0优先级**：[P0测试策略]
- **P1优先级**：[P1测试策略]
- **P2优先级**：[P2测试策略]

### 覆盖率目标
- **分支覆盖率**：80%
- **接口覆盖率**：100%
- **业务场景覆盖率**：90%

## +[业务领域1]
### 流程用例
#### +[子流程1]
##### P0-[场景描述]
[按照流程用例模板...]

### 页面用例
#### +[子流程2]
##### P0-[场景描述]
[按照页面用例模板...]

## +[业务领域2]
[重复上述结构...]

## 附录
### 测试数据准备
- [测试数据说明]

### 环境要求
- [测试环境要求]

### 自动化建议
- [自动化实施建议]
```
</process>

<criteria>
## 测试用例生成质量标准

### 结构完整性标准
- ✅ 严格遵循6级层次结构
- ✅ 业务领域覆盖完整
- ✅ 用例分类准确明确
- ✅ 子流程分解合理

### 测试场景质量标准
- ✅ 优先级标记清晰（P0/P1/P2）
- ✅ 测试场景具体可执行
- ✅ 验证点明确具体
- ✅ 异常场景覆盖完整

### 覆盖率标准
- ✅ 核心业务逻辑分支覆盖率≥80%
- ✅ 关键接口覆盖率100%
- ✅ P0场景覆盖率100%
- ✅ 异常处理覆盖率≥70%

### 可执行性标准
- ✅ 测试步骤清晰明确
- ✅ 验证点具体可操作
- ✅ 环境要求明确
- ✅ 数据准备方案完整

### 自动化友好性标准
- ✅ 测试场景易于自动化
- ✅ 验证点支持自动判断
- ✅ 数据准备可自动化
- ✅ 环境清理可自动化

### TDD集成标准
- ✅ 支持测试先行开发模式
- ✅ 测试用例可驱动代码设计
- ✅ 支持快速反馈循环
- ✅ 与持续集成流程兼容
</criteria>
</execution>