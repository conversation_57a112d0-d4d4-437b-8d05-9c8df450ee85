package com.muzi.yichao.module.customer.enums;

import com.muzi.yichao.framework.common.exception.ErrorCode;

public interface ErrorCodeConstants {

    // ========== 客户信息 1_041_000_000 ==========
    ErrorCode INFO_NOT_EXISTS = new ErrorCode(1_041_000_000, "客户信息不存在");
    ErrorCode INFO_ACCOUNT_LOG_NOT_EXISTS = new ErrorCode(1_041_000_001, "客户账户操作日志不存在");
    ErrorCode INFO_ADDRESS_NOT_EXISTS = new ErrorCode(1_041_000_002, "客户收货地址不存在");
    ErrorCode INFO_ADDRESS_EXISTS = new ErrorCode(1_041_000_003, "客户收货地址已存在");
    ErrorCode INFO_INVOICE_INFO_NOT_EXISTS = new ErrorCode(1_041_000_004, "客户发票信息不存在");
    ErrorCode INFO_POINTS_RECORD_NOT_EXISTS = new ErrorCode(1_041_000_005, "客户积分记录不存在");
    ErrorCode INFO_REFERRAL_NOT_EXISTS = new ErrorCode(1_041_000_006, "客户推荐关系不存在");
    ErrorCode INFO_STATUS_LOG_NOT_EXISTS = new ErrorCode(1_041_000_007, "客户状态变更记录不存在");
}
