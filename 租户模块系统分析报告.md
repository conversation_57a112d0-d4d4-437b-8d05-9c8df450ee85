# 易潮智能家居系统 - 租户模块系统分析报告

## 执行摘要

易潮智能家居系统是一个基于Spring Boot 3.4.5的企业级多租户SaaS平台，采用微服务架构设计，支持完整的多租户数据隔离、权限控制和业务管理功能。该系统通过自研的多租户框架实现了数据库级别的行级隔离、Redis缓存隔离、以及完善的安全防护机制。

## 1. 项目架构概览

### 1.1 技术栈

**核心框架：**
- Spring Boot 3.4.5
- Spring Security（安全框架）
- MyBatis Plus 3.5.10.1（ORM框架）
- Java 17

**数据库支持：**
- 主数据库：MySQL
- 多数据库支持：PostgreSQL、Oracle、SQL Server、达梦(DM8)、人大金仓(KingBase)、OpenGauss
- 缓存：Redis + Redisson 3.41.0

**前端技术：**
- Vue 2/Vue 3 管理后台
- UniApp 移动端应用
- Vben Admin（Vue 3版本）

### 1.2 模块结构

```
yichao-智能家居系统/
├── yichao-dependencies/           # 依赖管理
├── yichao-framework/             # 框架层
│   ├── yichao-spring-boot-starter-biz-tenant/  # 多租户框架 ⭐
│   ├── yichao-spring-boot-starter-security/    # 安全框架
│   ├── yichao-spring-boot-starter-mybatis/     # 数据访问框架
│   └── 其他框架组件...
├── yichao-server/                # 服务启动模块
├── 业务模块层/
│   ├── yichao-module-system/     # 系统管理（包含租户管理）
│   ├── yichao-module-member/     # 会员管理
│   ├── yichao-module-mall/       # 商城模块
│   ├── yichao-module-crm/        # 客户关系管理
│   └── 其他业务模块...
└── yichao-ui/                    # 前端应用
    ├── yichao-ui-admin-vue3/     # Vue3管理后台
    ├── yichao-ui-admin-uniapp/   # UniApp管理端
    └── 其他前端应用...
```

## 2. 租户系统核心架构

### 2.1 数据模型设计

**核心表结构：**

1. **system_tenant（租户表）**
```sql
CREATE TABLE `system_tenant` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '租户编号',
  `name` varchar(30) NOT NULL COMMENT '租户名',
  `contact_user_id` bigint NULL COMMENT '联系人的用户编号',
  `contact_name` varchar(30) NOT NULL COMMENT '联系人',
  `contact_mobile` varchar(500) NULL COMMENT '联系手机',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '租户状态（0正常 1停用）',
  `website` varchar(256) NULL COMMENT '绑定域名',
  `package_id` bigint NOT NULL COMMENT '租户套餐编号',
  `expire_time` datetime NOT NULL COMMENT '过期时间',
  `account_count` int NOT NULL COMMENT '账号数量',
  -- 标准字段
  `creator` varchar(64) NOT NULL DEFAULT '',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updater` varchar(64) NULL DEFAULT '',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted` bit(1) NOT NULL DEFAULT b'0',
  PRIMARY KEY (`id`)
) COMMENT = '租户表';
```

2. **system_tenant_package（租户套餐表）**
```sql
CREATE TABLE `system_tenant_package` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '套餐编号',
  `name` varchar(30) NOT NULL COMMENT '套餐名',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '租户状态（0正常 1停用）',
  `remark` varchar(256) NULL COMMENT '备注',
  `menu_ids` varchar(4096) NOT NULL COMMENT '关联的菜单编号',
  -- 标准字段
  PRIMARY KEY (`id`)
) COMMENT = '租户套餐表';
```

### 2.2 核心组件架构

**租户上下文管理：**
- `TenantContextHolder`：基于ThreadLocal的租户上下文管理
- `TenantUtils`：租户工具类，支持指定租户执行业务逻辑
- `TenantBaseDO`：租户基础实体类，包含tenantId字段

**过滤器链：**
1. `TenantContextWebFilter`：解析HTTP Header中的tenant-id
2. `TenantSecurityWebFilter`：租户安全验证和越权防护
3. `TenantVisitContextInterceptor`：支持租户切换功能

**数据隔离：**
- `TenantDatabaseInterceptor`：基于MyBatis Plus的SQL拦截器
- `TenantRedisCacheManager`：Redis缓存的租户隔离

## 3. 数据隔离机制

### 3.1 数据库层面隔离

**实现方式：**
- 基于MyBatis Plus的行级租户隔离
- 自动在SQL中添加`WHERE tenant_id = ?`条件
- 支持配置忽略特定表的租户隔离

**关键代码示例：**
```java
@Data
@EqualsAndHashCode(callSuper = true)
public abstract class TenantBaseDO extends BaseDO {
    /**
     * 多租户编号
     */
    private Long tenantId;
}
```

### 3.2 缓存层面隔离

**Redis隔离策略：**
- 在Redis Key中自动拼接租户ID前缀
- 通过`TenantRedisCacheManager`实现
- 支持配置忽略租户隔离的缓存项

### 3.3 隔离配置

```yaml
yichao:
  tenant:
    enable: true
    ignore-urls:
      - /jmreport/*  # 积木报表，无法携带租户编号
    ignore-visit-urls:
      - /admin-api/system/user/profile/**
      - /admin-api/system/auth/**
    ignore-tables: []
    ignore-caches:
      - user_role_ids
      - permission_menu_ids
      - oauth_client
```

## 4. 权限与安全机制

### 4.1 多层安全防护

**安全过滤器链：**
1. **租户上下文过滤器**：解析和设置租户ID
2. **租户安全过滤器**：验证用户权限和租户状态
3. **租户访问拦截器**：支持跨租户访问控制

### 4.2 越权防护机制

**防护策略：**
- 用户只能访问自己租户的数据
- 支持特权用户跨租户访问（需要特殊权限）
- 自动验证租户状态（是否禁用、过期等）

**权限验证流程：**
1. 解析HTTP Header中的tenant-id
2. 验证用户是否属于该租户
3. 检查租户状态和有效期
4. 验证用户是否有跨租户访问权限
5. 设置租户上下文并继续处理

### 4.3 套餐权限控制

**实现机制：**
- 基于租户套餐限制可访问的菜单
- 动态权限分配和更新
- 支持租户管理员角色自动创建
- 套餐变更时自动更新角色权限

## 5. 业务功能特性

### 5.1 租户生命周期管理

**租户创建流程：**
1. 验证租户名称和域名唯一性
2. 验证租户套餐有效性
3. 创建租户记录
4. 在租户上下文中创建管理员角色
5. 创建租户管理员用户
6. 分配角色权限

**关键代码示例：**
```java
@Override
@DSTransactional
@DataPermission(enable = false)
public Long createTenant(TenantSaveReqVO createReqVO) {
    // 校验租户名称是否重复
    validTenantNameDuplicate(createReqVO.getName(), null);
    // 校验租户域名是否重复
    validTenantWebsiteDuplicate(createReqVO.getWebsite(), null);
    // 校验套餐被禁用
    TenantPackageDO tenantPackage = tenantPackageService.validTenantPackage(createReqVO.getPackageId());

    // 创建租户
    TenantDO tenant = BeanUtils.toBean(createReqVO, TenantDO.class);
    tenantMapper.insert(tenant);
    
    // 创建租户的管理员
    TenantUtils.execute(tenant.getId(), () -> {
        // 创建角色
        Long roleId = createRole(tenantPackage);
        // 创建用户，并分配角色
        Long userId = createUser(roleId, createReqVO);
        // 修改租户的管理员
        tenantMapper.updateById(new TenantDO().setId(tenant.getId()).setContactUserId(userId));
    });
    return tenant.getId();
}
```

### 5.2 租户套餐管理

**套餐功能：**
- 定义不同级别的功能权限
- 控制可访问的菜单和功能
- 支持套餐升级和降级
- 动态权限更新

## 6. 集成点分析

### 6.1 与系统模块集成

**核心集成点：**
- **用户管理**：每个租户有独立的用户体系
- **角色权限**：基于租户套餐的权限分配
- **菜单管理**：租户套餐控制可访问菜单
- **部门管理**：租户内部的组织架构

### 6.2 与业务模块集成

**集成方式：**
- 所有业务实体继承`TenantBaseDO`
- 自动包含`tenant_id`字段
- 通过MyBatis Plus拦截器自动实现数据隔离

**业务模块示例：**
- 会员模块：租户独立的会员体系
- 商城模块：租户独立的商品和订单
- CRM模块：租户独立的客户管理
- ERP模块：租户独立的企业资源

### 6.3 异步处理集成

**异步场景支持：**
- **定时任务**：支持按租户并行执行
- **消息队列**：消息头携带租户信息
- **异步任务**：使用TransmittableThreadLocal保证上下文传递

## 7. 技术特色与优势

### 7.1 技术特色

1. **完整的多租户解决方案**
   - 数据库、缓存、业务逻辑全方位隔离
   - 支持多种数据库类型
   - 灵活的配置选项

2. **高性能设计**
   - 基于MyBatis Plus拦截器，性能损耗最小
   - Redis缓存隔离，提升访问速度
   - 异步任务支持，保证上下文传递

3. **安全可靠**
   - 多层安全防护机制
   - 完善的越权防护
   - 租户状态和有效期验证

4. **易于扩展**
   - 模块化设计，易于集成
   - 丰富的配置选项
   - 支持自定义扩展

### 7.2 架构优势

1. **数据隔离彻底**：从数据库到缓存的完整隔离
2. **性能影响最小**：基于拦截器的轻量级实现
3. **开发友好**：对业务代码侵入性最小
4. **运维简单**：统一的配置管理和监控

## 8. 当前限制与改进建议

### 8.1 当前限制

1. **单数据库架构**：所有租户数据存储在同一数据库中
2. **缓存共享**：Redis实例在租户间共享
3. **文件存储**：文件系统未实现租户隔离
4. **监控粒度**：缺乏租户级别的详细监控

### 8.2 改进建议

1. **数据库隔离增强**
   - 考虑支持租户独立数据库
   - 实现数据库级别的完全隔离
   - 支持租户数据迁移功能

2. **性能优化**
   - 实现租户级别的连接池
   - 优化大租户的查询性能
   - 增加租户级别的缓存预热

3. **监控完善**
   - 增加租户级别的性能监控
   - 实现租户资源使用统计
   - 支持租户级别的告警机制

4. **功能扩展**
   - 支持租户数据备份和恢复
   - 实现租户级别的配置管理
   - 增加租户迁移和合并功能

## 9. 总结

易潮智能家居系统的多租户架构设计完善，技术实现先进，具有以下特点：

1. **架构完整**：从数据层到应用层的全方位多租户支持
2. **技术先进**：基于Spring Boot 3.x和最新技术栈
3. **安全可靠**：多层安全防护和完善的权限控制
4. **性能优秀**：轻量级实现，对性能影响最小
5. **扩展性强**：模块化设计，易于扩展和维护

该系统为企业级SaaS应用提供了一个优秀的多租户解决方案参考，具有很高的实用价值和推广意义。

---

## 附录：三级租户层级改造方案

### A.1 数据库表结构改造

**system_tenant表新增字段：**
```sql
ALTER TABLE system_tenant ADD COLUMN parent_id BIGINT NULL COMMENT '父租户ID';
ALTER TABLE system_tenant ADD COLUMN tenant_level TINYINT NOT NULL DEFAULT 1 COMMENT '租户层级(1=系统管理员,2=分销商,3=分店)';
ALTER TABLE system_tenant ADD COLUMN tenant_path VARCHAR(500) NULL COMMENT '租户路径，如/1/2/3';
ALTER TABLE system_tenant ADD COLUMN root_tenant_id BIGINT NULL COMMENT '根租户ID';
ALTER TABLE system_tenant ADD INDEX idx_parent_id (parent_id);
ALTER TABLE system_tenant ADD INDEX idx_tenant_level (tenant_level);
ALTER TABLE system_tenant ADD INDEX idx_root_tenant_id (root_tenant_id);
```

**新增system_tenant_hierarchy表（闭包表）：**
```sql
CREATE TABLE system_tenant_hierarchy (
  id BIGINT NOT NULL AUTO_INCREMENT COMMENT '层级关系ID',
  ancestor_id BIGINT NOT NULL COMMENT '祖先租户ID',
  descendant_id BIGINT NOT NULL COMMENT '后代租户ID',
  level_diff TINYINT NOT NULL COMMENT '层级差距',
  create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (id),
  UNIQUE KEY uk_ancestor_descendant (ancestor_id, descendant_id),
  KEY idx_ancestor_id (ancestor_id),
  KEY idx_descendant_id (descendant_id),
  KEY idx_level_diff (level_diff)
) COMMENT = '租户层级关系表';
```

---

**报告生成时间**：2025年1月28日
**分析版本**：v2.6.0-SNAPSHOT
**技术栈版本**：Spring Boot 3.4.5 + Java 17
  