# 商品管理模块 - 借出申请新增页面功能设计文档

## 1. 页面概述

### 1.1 页面目的
借出申请新增页面是智能家装管理平台商品管理系统的专业业务页面，负责创建新的商品借出申请，支持非销售出库场景下的物料外借管理。该页面通过规范化的借出申请流程，确保借出业务的有序管理和商品资产的安全控制。

### 1.2 业务价值
- 提供标准化的借出申请创建功能，规范借出业务流程
- 支持多种借出场景，满足展会、试用、测试等不同业务需求
- 建立完整的借出记录，确保借出商品的可追溯性
- 实现借出与归还的闭环管理，保障商品资产安全
- 提供借出费用管理，支持有偿借出业务模式

### 1.3 页面入口
- **主要入口**：商品管理 → 借出管理 → 新增
- **快速入口**：商品管理相关页面的快速借出链接
- **业务入口**：客户服务、展会管理等业务场景的借出申请

### 1.4 功能架构
借出申请新增页面包含四个核心功能区域：
- **申请人信息管理**：借用方身份和借出计划信息
- **商品选择管理**：借出商品的选择和数量设置
- **备注信息管理**：借出用途和附加说明信息
- **操作流程控制**：申请的提交、保存和流程控制

### 1.5 业务流程
借出申请的完整业务流程：
```
借出申请创建 → 审核通过 → 出库（绑定SN） → 借出中 → 用户归还 → 入库登记 → 状态变更为"已归还"
```

## 2. 借出申请新增页面操作流程图

```mermaid
flowchart TD
    A[用户访问借出申请新增页面] --> B[权限验证]
    B --> C{权限验证通过?}
    C -->|否| D[显示权限不足提示]
    C -->|是| E[页面初始化]
    
    E --> F[加载申请人信息区]
    E --> G[加载商品选择区]
    E --> H[加载备注区]
    E --> I[显示操作按钮]
    
    F --> J[客户信息管理]
    J --> K[点击客户选择]
    K --> L[弹出客户选择器]
    L --> M[客户类型筛选]
    M --> N[外部客户]
    M --> O[内部借用对象]
    
    N --> P[客户列表展示]
    O --> Q[项目部/员工列表]
    
    P --> R[客户搜索功能]
    R --> S[姓名搜索]
    R --> T[手机号搜索]
    R --> U[拼音首字母搜索]
    
    Q --> V[内部对象搜索]
    
    S --> W[选择具体客户]
    T --> W
    U --> W
    V --> W
    
    W --> X[客户信息联动]
    X --> Y[客户地址自动填充]
    X --> Z[多地址选择支持]
    
    F --> AA[借出时间管理]
    AA --> BB[预借出日期设置]
    AA --> CC[预计归还日期设置]
    AA --> DD[借出期限计算]
    
    BB --> EE{日期验证}
    CC --> EE
    EE -->|日期冲突| FF[显示日期错误提示]
    EE -->|日期合理| GG[日期验证通过]
    
    F --> HH[费用管理]
    HH --> II[产生费用设置]
    II --> JJ[免费借出]
    II --> KK[按天收费]
    II --> LL[固定费用]
    
    G --> MM[商品选择功能]
    MM --> NN[点击选择商品]
    NN --> OO[弹出商品选择器]
    OO --> PP[商品列表展示]
    PP --> QQ[商品分页显示]
    PP --> RR[商品分类筛选]
    PP --> SS[商品搜索功能]
    
    SS --> TT[商品名称搜索]
    SS --> UU[商品编码搜索]
    SS --> VV[型号规格搜索]
    
    PP --> WW[商品信息展示]
    WW --> XX[商品缩略图]
    WW --> YY[商品编码名称]
    WW --> ZZ[型号规格单位]
    WW --> AAA[当前库存]
    WW --> BBB[SN管理提示]
    
    PP --> CCC[选择商品]
    CCC --> DDD[添加到明细表格]
    
    DDD --> EEE[商品明细管理]
    EEE --> FFF[商品信息展示]
    FFF --> GGG[商品图片]
    FFF --> HHH[商品编码]
    FFF --> III[商品名称]
    FFF --> JJJ[型号规格]
    FFF --> KKK[计量单位]
    
    EEE --> LLL[出借数量录入]
    LLL --> MMM[数量输入验证]
    MMM --> NNN{数量验证}
    NNN -->|数量≤0| OOO[显示数量错误提示]
    NNN -->|数量>库存| PPP[显示库存不足提示]
    NNN -->|数量合理| QQQ[数量验证通过]
    
    EEE --> RRR[商品操作]
    RRR --> SSS[删除商品]
    RRR --> TTT[修改数量]
    
    H --> UUU[备注信息管理]
    UUU --> VVV[借出用途录入]
    VVV --> WWW[用途分类选择]
    WWW --> XXX[展会用途]
    WWW --> YYY[客户试用]
    WWW --> ZZZ[内部测试]
    
    UUU --> AAAA[备注说明录入]
    AAAA --> BBBB[多行文本输入]
    BBBB --> CCCC[常用备注模板]
    
    UUU --> DDDD[附件上传]
    DDDD --> EEEE[借用协议上传]
    DDDD --> FFFF[现场照片上传]
    
    I --> GGGG[操作按钮功能]
    GGGG --> HHHH[草稿保存]
    GGGG --> IIII[提交申请]
    GGGG --> JJJJ[取消操作]
    
    HHHH --> KKKK[保存草稿状态]
    KKKK --> LLLL[定时自动保存]
    
    IIII --> MMMM{提交前校验}
    MMMM -->|校验失败| NNNN[显示校验错误提示]
    MMMM -->|校验通过| OOOO[创建借出申请单]
    
    OOOO --> PPPP[生成申请单号]
    PPPP --> QQQQ[更新申请状态]
    QQQQ --> RRRR[发送审核通知]
    RRRR --> SSSS[返回借出管理列表]
    
    JJJJ --> TTTT{确认取消操作?}
    TTTT -->|否| UUUU[继续编辑]
    TTTT -->|是| VVVV[清除页面数据]
    VVVV --> SSSS
    
    style A fill:#e1f5fe
    style C fill:#fff3e0
    style D fill:#ffebee
    style EE fill:#fff3e0
    style FF fill:#ffebee
    style NNN fill:#fff3e0
    style OOO fill:#ffebee
    style PPP fill:#fff8e1
    style MMMM fill:#fff3e0
    style NNNN fill:#ffebee
    style TTTT fill:#fff3e0
    style RRRR fill:#e8f5e8
```

### 流程说明
借出申请新增页面的操作流程主要包含以下几个核心环节：

1. **权限验证与页面初始化**：验证用户借出申请权限，初始化页面各功能区域
2. **客户信息选择管理**：支持外部客户和内部对象选择，信息自动联动
3. **借出时间计划设置**：设置预借出日期和归还日期，进行时间合理性验证
4. **商品选择和数量管理**：多方式商品选择，库存验证和数量管理
5. **备注和附件管理**：借出用途分类、备注说明和相关附件上传
6. **申请提交和流程控制**：完整性校验后提交申请，进入审核流程

## 3. 详细功能设计

### 3.1 申请人信息管理功能

#### 3.1.1 客户信息选择
**功能描述**：选择借出申请的客户或内部借用对象

**客户类型**：
- **外部客户**：正常的客户借用
  - 客户列表展示
  - 支持客户搜索
  - 客户信息联动
- **内部借用对象**：内部项目部、员工等
  - 项目部列表
  - 员工列表
  - 部门信息联动

**搜索功能**：
- **姓名搜索**：支持客户姓名模糊搜索
- **手机号搜索**：支持手机号精确和模糊搜索
- **拼音首字母搜索**：支持拼音首字母快速定位
- **实时搜索**：输入过程中实时搜索建议

#### 3.1.2 地址信息管理
**功能描述**：管理客户的地址信息

**地址功能**：
- **自动填充**：选择客户后自动带出默认地址
- **多地址支持**：若客户有多个地址，提供下拉选择
- **地址验证**：验证地址信息的完整性
- **新增地址**：支持为客户新增地址信息

#### 3.1.3 借出时间管理
**功能描述**：管理借出的时间计划

**时间字段**：
- **预借出日期**：计划开始借出的日期
  - 日期选择器
  - 不能早于当前日期
  - 支持快速日期选择
- **预计归还日期**：借用物品计划归还时间
  - 日期选择器
  - 不能早于预借出日期
  - 自动计算借出期限

**时间验证**：
- **日期合理性**：预借出日期不晚于预计归还日期
- **期限限制**：借出期限不超过系统设定的最大期限
- **冲突检查**：检查商品在该时间段是否已被借出

#### 3.1.4 费用管理
**功能描述**：管理借出产生的费用

**费用类型**：
- **免费借出**：不产生任何费用
- **按天收费**：按借出天数计算费用
- **固定费用**：固定的借出费用
- **押金管理**：收取借出押金

### 3.2 商品选择管理功能

#### 3.2.1 商品选择器
**功能描述**：弹出式商品选择器，支持多种选择方式

**选择器功能**：
- **分页显示**：大量商品的分页展示
- **分类筛选**：按商品分类筛选
- **搜索功能**：商品名称、编码、型号搜索
- **库存显示**：显示当前可借出库存

**商品信息展示**：
- **商品缩略图**：商品图片展示
- **商品编码名称**：编码和名称信息
- **型号规格单位**：详细规格信息
- **当前库存**：可借出的库存数量
- **SN管理提示**：是否需要序列号管理

#### 3.2.2 商品明细管理
**功能描述**：管理已选择的借出商品明细

**明细信息**：
- **商品基本信息**：图片、编码、名称、型号、规格、单位
- **出借数量**：可手动输入，限制为当前库存上限
- **库存参考**：显示当前可用库存
- **操作功能**：删除商品、修改数量

**数量验证**：
- **非负验证**：出借数量必须大于0
- **库存验证**：不能超过当前可用库存
- **合理性验证**：数量必须为整数

#### 3.2.3 序列号管理
**功能描述**：管理启用SN控制的商品序列号

**SN管理**：
- **SN标识**：商品明细显示SN管理提示
- **序列号预分配**：为借出商品预分配序列号
- **序列号验证**：确保序列号的唯一性
- **序列号跟踪**：建立序列号与借出记录的关联

### 3.3 备注信息管理功能

#### 3.3.1 借出用途管理
**功能描述**：管理借出的用途分类和说明

**用途分类**：
- **展会用途**：用于展会展示的借出
- **客户试用**：客户试用体验的借出
- **内部测试**：内部测试使用的借出
- **其他用途**：自定义的其他用途

#### 3.3.2 备注说明管理
**功能描述**：管理借出的详细说明信息

**备注功能**：
- **多行文本输入**：支持详细的备注说明
- **常用备注模板**：提供常用的备注模板
- **字符限制**：限制备注的字符长度
- **格式验证**：验证备注内容的格式

#### 3.3.3 附件上传管理
**功能描述**：管理借出相关的附件文件

**附件类型**：
- **借用协议**：上传借用协议文件
- **现场照片**：上传相关的现场照片
- **其他文件**：上传其他相关文件

**上传功能**：
- **文件格式限制**：限制上传文件的格式
- **文件大小限制**：限制上传文件的大小
- **文件预览**：支持文件的预览功能
- **文件删除**：支持删除已上传的文件

### 3.4 数据验证功能

#### 3.4.1 提交前校验
**功能描述**：提交申请前的完整性校验

**校验规则**：
- **客户信息完整**：客户信息必须完整填写
- **商品选择验证**：至少选择一种商品
- **数量验证**：所有商品出借数量必须大于0
- **日期验证**：预借出日期不晚于预计归还日期
- **必填项验证**：所有必填项必须填写

#### 3.4.2 业务规则验证
**功能描述**：验证业务规则的合理性

**业务验证**：
- **库存充足性**：验证库存是否充足
- **借出权限**：验证商品的借出权限
- **时间冲突**：验证时间是否冲突
- **客户状态**：验证客户状态是否正常

### 3.5 操作流程控制

#### 3.5.1 草稿保存功能
**功能描述**：保存借出申请草稿

**草稿功能**：
- **手动保存**：用户主动保存草稿
- **自动保存**：定时自动保存草稿
- **草稿恢复**：支持恢复已保存的草稿
- **草稿管理**：管理多个草稿版本

#### 3.5.2 提交申请功能
**功能描述**：正式提交借出申请

**提交流程**：
- **完整性校验**：校验申请信息的完整性
- **业务规则验证**：验证业务规则
- **申请单生成**：生成正式的借出申请单
- **状态更新**：更新申请状态
- **通知发送**：发送审核通知

#### 3.5.3 取消操作功能
**功能描述**：取消当前的申请编辑

**取消流程**：
- **确认提示**：确认是否取消当前操作
- **数据保护**：提醒保存未提交的数据
- **页面清空**：清空页面编辑数据
- **返回列表**：返回借出管理列表页面

## 4. 用户界面设计

### 4.1 页面布局设计
- **申请人信息区**：客户选择、地址信息、时间计划、费用设置
- **商品选择区**：商品选择器和明细表格
- **备注区**：用途分类、备注说明、附件上传
- **操作按钮区**：草稿保存、提交申请、取消操作

### 4.2 交互设计规范
- **智能联动**：客户选择后地址信息自动填充
- **实时验证**：数量输入时实时验证库存
- **友好提示**：清晰的错误提示和操作指导
- **快捷操作**：支持常用操作的快捷方式

### 4.3 响应式设计
- **PC端**：完整功能展示，多区域布局
- **平板端**：适配中等屏幕，关键功能优先
- **移动端**：简化布局，核心功能保留

## 5. 权限控制

### 5.1 功能权限
- **页面访问权限**：借出申请新增页面访问权限
- **客户选择权限**：客户信息查看和选择权限
- **商品借出权限**：商品借出申请权限
- **申请提交权限**：借出申请提交权限

### 5.2 数据权限
- **客户权限**：只能为有权限的客户创建借出申请
- **商品权限**：只能借出有权限的商品类别
- **库存权限**：只能查看和操作有权限的库存

## 6. 异常处理

### 6.1 业务异常
- **库存不足**：借出数量超过可用库存的处理
- **客户异常**：客户信息异常的处理
- **时间冲突**：借出时间冲突的处理
- **数据验证失败**：输入数据不符合业务规则的处理

### 6.2 系统异常
- **网络异常**：网络连接异常的处理和重试
- **数据保存失败**：数据保存失败的处理和恢复
- **权限异常**：权限验证失败的友好提示
- **系统错误**：系统错误的异常处理和用户提示

---

**文档版本**：v1.0  
**编写日期**：2025-07-02  
**编写人员**：AI系统架构师  
**审核状态**：待审核
