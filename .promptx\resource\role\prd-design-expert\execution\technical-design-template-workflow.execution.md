<execution>
  <constraint>
    ## 技术设计模板约束
    - 必须严格遵循Java通用技术设计方案模板的11章节结构
    - 文档元信息必须包含版本、创建日期、作者、评审人、状态、关联需求
    - 修订历史必须记录每次变更的版本、日期、修订人和修订描述
    - 所有技术选型必须有方案比较和选择理由
    - TDD相关内容必须贯穿整个设计文档
  </constraint>
  
  <rule>
    ## 强制性章节规则
    - 引言章节必须包含：问题背景、目标、非目标、关键术语、约束与假设
    - 整体设计必须包含：架构概览、设计原则与考量(含TDD)、方案选型与理由
    - 详细设计每个模块必须包含：概述与职责、交互流程、数据模型、接口设计、TDD实现要点
    - 非功能性需求必须涵盖：性能、可用性、可扩展性、可维护性、安全性
    - 质量保障章节必须详细描述TDD测试策略
  </rule>
  
  <guideline>
    ## 文档生成指导原则
    - 基于PRD分析结果填充技术设计模板各章节
    - 重点关注TDD流程的可测试性设计
    - 确保每个技术决策都有明确的理由说明
    - 运维考量章节要考虑实际部署和监控需求
    - 风险评估要识别技术实现中的关键风险点
  </guideline>
  
  <process>
    ## Java通用技术设计方案模板执行流程
    
    ### Phase 1: 文档元信息与引言 (20分钟)
    ```mermaid
    flowchart TD
        A[基于PRD分析结果] --> B[填充文档元信息]
        B --> C[撰写问题背景和目标]
        C --> D[明确非目标和约束]
        D --> E[定义关键术语表]
    ```
    
    **具体步骤：**
    - 根据PRD内容确定项目名称、版本、关联需求
    - 从PRD中提取问题背景和业务目标
    - 明确技术目标和非功能性目标
    - 识别技术约束和业务假设
    
    ### Phase 2: 整体设计架构 (30分钟)
    ```mermaid
    flowchart TD
        A[系统边界识别] --> B[架构概览设计]
        B --> C[设计原则确定]
        C --> D[TDD设计考量]
        D --> E[技术方案比较]
        E --> F[方案选型决策]
    ```
    
    **设计原则与TDD考量：**
    - 可测试性优先：明确模块边界，易于隔离和Mock
    - 红-绿-重构循环的实践思路
    - 依赖注入和接口设计支持单元测试
    - 分层架构支持测试隔离
    
    ### Phase 3: 详细设计实现 (60分钟)
    ```mermaid
    flowchart TD
        A[核心模块识别] --> B[模块职责定义]
        B --> C[交互流程设计]
        C --> D[数据模型设计]
        D --> E[接口API设计]
        E --> F[TDD实现要点]
        F --> G[下一个模块]
    ```
    
    **每个模块详细设计模板：**
    ```
    ### 3.X [模块名称]
    #### 3.X.1 概述与职责
    #### 3.X.2 交互流程 (含Mermaid图)
    #### 3.X.3 数据模型设计 (含DDL)
    #### 3.X.4 接口设计 (API Design)
    #### 3.X.5 消息队列设计 (如有)
    #### 3.X.6 核心算法/逻辑 (如有)
    #### 3.X.7 TDD 实现要点
    ```
    
    ### Phase 4: 非功能性需求与运维 (30分钟)
    ```mermaid
    flowchart TD
        A[性能需求分析] --> B[可用性设计]
        B --> C[可扩展性考量]
        C --> D[安全性设计]
        D --> E[部署方案设计]
        E --> F[监控告警配置]
        F --> G[回滚方案制定]
    ```
    
    ### Phase 5: 质量保障与TDD策略 (40分钟)
    ```mermaid
    flowchart TD
        A[单元测试策略] --> B[集成测试计划]
        B --> C[端到端测试]
        C --> D[性能测试设计]
        D --> E[安全测试规划]
        E --> F[TDD实践指导]
    ```
    
    **TDD测试策略详细内容：**
    - 单元测试：覆盖范围、工具选择、Mock策略
    - 集成测试：模块交互、外部依赖测试
    - TDD实践：红-绿-重构循环在项目中的应用
    - 测试数据管理：测试环境配置、数据准备策略
    
    ### Phase 6: 风险评估与项目管理 (20分钟)
    ```mermaid
    flowchart TD
        A[技术风险识别] --> B[业务风险评估]
        B --> C[应对措施制定]
        C --> D[人力分工规划]
        D --> E[里程碑制定]
        E --> F[上线清单确认]
    ```
  </process>
  
  <criteria>
    ## 技术设计文档质量标准
    
    ### 结构完整性
    - ✅ 包含技术设计模板要求的11个主要章节
    - ✅ 文档元信息和修订历史完整
    - ✅ 每个章节内容充实且逻辑清晰
    - ✅ 图表配合文字说明，便于理解
    
    ### TDD集成度
    - ✅ 设计原则体现TDD考量
    - ✅ 每个模块包含TDD实现要点
    - ✅ 质量保障章节详细描述测试策略
    - ✅ 可测试性设计贯穿整个架构
    
    ### 技术决策质量
    - ✅ 技术选型有明确的方案比较
    - ✅ 每个设计决策有充分的理由说明
    - ✅ 考虑了性能、安全、可维护性等非功能性需求
    - ✅ 运维考量实用且可操作
    
    ### 文档可用性
    - ✅ 开发团队可以直接基于文档开始编码
    - ✅ 测试团队可以基于文档制定测试计划
    - ✅ 运维团队可以基于文档进行部署和监控
    - ✅ 文档与PRD需求完全对应，可追溯
  </criteria>
</execution>