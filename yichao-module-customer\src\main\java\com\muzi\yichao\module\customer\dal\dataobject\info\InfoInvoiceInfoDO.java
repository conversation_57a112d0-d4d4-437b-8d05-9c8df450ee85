package com.muzi.yichao.module.customer.dal.dataobject.info;

import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.muzi.yichao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 客户发票信息 DO
 *
 * <AUTHOR>
 */
@TableName("customer_info_invoice_info")
@KeySequence("customer_info_invoice_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InfoInvoiceInfoDO extends BaseDO {

    /**
     * 发票信息ID
     */
    @TableId
    private Long id;
    /**
     * 客户ID
     */
    private Long customerId;
    /**
     * 发票类型：1-普通发票，2-专用发票
     *
     * 枚举 {@link TODO invoice_type 对应的类}
     */
    private Integer invoiceType;
    /**
     * 发票抬头
     */
    private String invoiceTitle;
    /**
     * 税号
     */
    private String taxNumber;
    /**
     * 公司地址
     */
    private String companyAddress;
    /**
     * 公司电话
     */
    private String companyPhone;
    /**
     * 开户银行
     */
    private String bankName;
    /**
     * 银行账号
     */
    private String bankAccount;
    /**
     * 收票人姓名
     */
    private String receiverName;
    /**
     * 收票人电话
     */
    private String receiverPhone;
    /**
     * 收票人地址
     */
    private String receiverAddress;
    /**
     * 是否自动开票
     */
    private Boolean autoInvoice;
    /**
     * 开票阈值
     */
    private BigDecimal invoiceThreshold;
    /**
     * 是否默认发票信息
     */
    private Boolean isDefault;

}