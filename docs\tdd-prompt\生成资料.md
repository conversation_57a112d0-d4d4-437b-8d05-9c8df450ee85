
指导AI为Java项目生成.mdc规则文件
使用java-tdd-expert角色创建一个python脚本来自动识别所有Java文件并为其创建对应的 .mdc 规则文件。生成的每个 .mdc 文件都必须高质量，并能支持我的技术设计和测试用例生成工作。请结合使用context7，并直接帮我执行python脚本。

PRD文档分析与设计文档生成
导入PRD文档并生成需求分析文档
使用，使用task-masterer
解析文档 @[PRD文件路径]（ps:最好是markdown格式），
并生成「需求分析文档」 和拆分出「任务清单」



根据「需求分析文档」生成「技术设计文档」
使用 prd-design-expert角色
根据@[需求分析文档路径]，@「任务清单路径」
生成「技术设计文档」

生成测试用例（如需要）
使用test-case-generator角色帮我在当前目录生成一个markdown格式的测试用例文件，主要功能是： @[需求分析文档路径]，@「任务清单路径」，结合@所有Java文件的 .mdc 规则（ps:如需，上面指导AI为Java项目生成.mdc规则文件的产物）进一步查阅枚举类和服务的实际源代码来对状态机流转和外部上下游系统交互进行分析。如果用例文件内容太大，分批写入同一文件中。

