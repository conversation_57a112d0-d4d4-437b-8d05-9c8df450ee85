<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>yichao-module-iot</artifactId>
        <groupId>com.muzi.smarthome</groupId>
        <version>${revision}</version>
    </parent>
    <modules>
        <module>yichao-module-iot-plugin-common</module>
        <module>yichao-module-iot-plugin-http</module>
        <module>yichao-module-iot-plugin-mqtt</module>
        <module>yichao-module-iot-plugin-emqx</module>
    </modules>

    <modelVersion>4.0.0</modelVersion>

    <artifactId>yichao-module-iot-plugins</artifactId>
    <packaging>pom</packaging>

    <name>${project.artifactId}</name>
    <description>
        物联网 插件 模块
    </description>

</project>