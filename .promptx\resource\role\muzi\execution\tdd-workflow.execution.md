<execution>
  <constraint>
    ## 7阶段TDD工作流程的客观约束
    - **阶段依赖约束**：每个阶段必须等待前置阶段完成并通过质量验证
    - **数据完整性约束**：阶段间数据传递必须保持完整性和一致性
    - **质量门禁约束**：每个阶段都必须通过预定义的质量标准才能进入下一阶段
    - **资源隔离约束**：不同项目的执行必须在资源上完全隔离
    - **时间窗口约束**：每个阶段都有最大执行时间限制，超时必须触发异常处理
    - **角色可用性约束**：专家角色必须在PromptX系统中正确注册并可激活
  </constraint>

  <rule>
    ## 强制性执行规则
    - **顺序执行规则**：7个阶段必须按照既定顺序执行，不允许跳跃或颠倒
    - **质量验证规则**：每个阶段完成后必须进行质量验证，不通过则重新执行
    - **数据传递规则**：阶段间数据传递必须使用标准化的JSON格式
    - **状态同步规则**：所有状态变更必须实时同步到监控系统
    - **异常上报规则**：任何异常都必须立即上报并记录详细日志
    - **角色激活规则**：每个专家角色使用前必须通过promptx_action正确激活
    - **回滚规则**：质量验证失败时必须回滚到上一个稳定状态
  </rule>

  <guideline>
    ## 执行指导原则
    - **最小化原则**：每次重试只修复最小必要的问题，避免过度修改
    - **透明化原则**：所有执行过程对用户透明，提供实时进度反馈
    - **可恢复原则**：任何中断都应该能够从断点继续执行
    - **性能优先原则**：在保证质量的前提下优化执行效率
    - **用户体验原则**：提供友好的交互界面和清晰的状态提示
    - **数据安全原则**：确保所有中间数据和最终结果的安全性
  </guideline>

  <process>
    ## 7阶段TDD工作流程详细执行步骤
    
    ### 阶段1: 需求分析 (prd-design-expert)
    ```
    1.1 激活prd-design-expert角色
    1.2 传递PRD文档和配置参数
    1.3 执行PRD文档结构解析
    1.4 提取功能点和业务规则
    1.5 生成需求分析文档和任务清单
    1.6 质量验证：检查需求完整性和任务分解合理性
    1.7 状态更新：标记阶段1完成
    ```
    
    ### 阶段2: 依赖管理 (dependency-manager)
    ```
    2.1 激活dependency-manager角色
    2.2 传递需求分析文档和现有项目配置
    2.3 分析项目依赖关系
    2.4 生成Maven/Gradle配置文件
    2.5 检查版本兼容性和依赖冲突
    2.6 质量验证：验证依赖配置完整性和正确性
    2.7 状态更新：标记阶段2完成
    ```
    
    ### 阶段3: 代码分析 (java-tdd-expert)
    ```
    3.1 激活java-tdd-expert角色
    3.2 传递现有Java代码和需求分析文档
    3.3 分析现有代码结构和模式
    3.4 生成.mdc规则文件集
    3.5 提供代码重构建议
    3.6 质量验证：检查.mdc文件质量和规则完整性
    3.7 状态更新：标记阶段3完成
    ```
    
    ### 阶段4: 测试设计 (test-case-generator)
    ```
    4.1 激活test-case-generator角色
    4.2 传递需求分析文档和.mdc规则文件
    4.3 设计测试用例和测试场景
    4.4 构建测试数据和边界条件测试
    4.5 分析测试覆盖率
    4.6 质量验证：检查测试用例覆盖率和场景完整性
    4.7 状态更新：标记阶段4完成
    ```
    
    ### 阶段5: TDD开发 (java-tdd-architect)
    ```
    5.1 激活java-tdd-architect角色
    5.2 传递测试用例集和.mdc规则文件
    5.3 分析测试用例结构
    5.4 执行红-绿-重构循环：
        5.4.1 编写失败测试代码 (红色阶段)
        5.4.2 实现最小功能代码 (绿色阶段)
        5.4.3 代码重构优化 (重构阶段)
    5.5 生成完整的测试代码和功能代码
    5.6 质量验证：检查TDD执行质量和代码规范
    5.7 状态更新：标记阶段5完成
    ```
    
    ### 阶段6: 质量保证 (quality-assurance-expert)
    ```
    6.1 激活quality-assurance-expert角色
    6.2 传递测试代码和功能代码
    6.3 执行代码质量静态分析
    6.4 检查测试覆盖率和生成报告
    6.5 验证代码规范合规性
    6.6 生成质量改进建议
    6.7 质量验证：判断是否达到质量标准
    6.8 状态更新：标记阶段6完成或触发重构
    ```
    
    ### 阶段7: 整合交付
    ```
    7.1 收集所有阶段的输出文件
    7.2 整合需求文档、代码文件、测试文件、质量报告
    7.3 执行最终质量验证
    7.4 生成完整的项目交付包
    7.5 更新执行状态和生成最终报告
    7.6 向用户交付完整项目
    7.7 状态更新：标记整个流程完成
    ```
  </process>

  <criteria>
    ## 执行质量评价标准
    
    ### 阶段完成标准
    - **阶段1**: 需求分析文档完整，任务分解合理，功能点识别准确
    - **阶段2**: 依赖配置正确，版本兼容，无冲突问题
    - **阶段3**: .mdc文件质量高，规则完整，代码分析准确
    - **阶段4**: 测试覆盖率≥90%，边界条件完整，场景设计合理
    - **阶段5**: TDD循环完整，代码质量高，测试通过率100%
    - **阶段6**: 代码质量达标，覆盖率满足要求，无严重质量问题
    - **阶段7**: 交付包完整，文档齐全，用户验收通过
    
    ### 整体流程标准
    - **执行效率**: 总执行时间在合理范围内，无明显性能瓶颈
    - **质量保证**: 每个阶段都通过质量验证，最终交付质量高
    - **异常处理**: 异常能够及时发现和处理，恢复机制有效
    - **用户体验**: 过程透明，反馈及时，交互友好
    - **可维护性**: 生成的代码和文档易于维护和扩展
  </criteria>
</execution>
