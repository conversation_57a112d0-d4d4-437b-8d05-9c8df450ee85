/*
 SaaS智能家装CRM系统 - 客户管理模块数据迁移脚本
 
 用途：将旧表数据迁移到新表结构中
 版本：v1.0
 创建时间：2025-01-19
 数据库：MySQL 8.0+
 
 说明：
 - 本脚本用于将客户管理模块旧表数据迁移到新表中
 - 执行前请确保新表结构已创建
 - 建议在执行前备份数据库
 - 按照依赖关系顺序迁移数据（先迁移主表，后迁移子表）
 
 迁移映射：
 customer_customer → customer_info
 customer_address → customer_info_address
 customer_referral → customer_info_referral
 customer_status_log → customer_info_status_log
 customer_points_record → customer_info_points_record
 customer_account_log → customer_info_account_log
 customer_invoice_info → customer_info_invoice_info
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ========================================
-- 数据迁移
-- ========================================

-- 注意：按照依赖关系顺序迁移，先迁移主表，后迁移子表

-- ----------------------------
-- 迁移客户主表数据
-- ----------------------------
INSERT INTO `customer_info` (
    `id`, `customer_no`, `store_id`, `category`, `source`, `name`, `mobile`, `wechat`, 
    `sex`, `birthday`, `id_type`, `id_number`, `company_name`, `follower_user_id`, 
    `designer_user_id`, `password`, `issue_date`, `expire_date`, `account_id`, 
    `initial_deposit`, `bonus_amount`, `initial_points`, `is_installer`, `customer_type`, 
    `app_account`, `need_invoice`, `referrer_id`, `remark`, `email`, `position`, 
    `customer_manager_id`, `vip_level`, `customer_tags`, `status`, `creator`, 
    `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`
)
SELECT 
    `id`, `customer_no`, `store_id`, `category`, `source`, `name`, `mobile`, `wechat`, 
    `sex`, `birthday`, `id_type`, `id_number`, `company_name`, `follower_user_id`, 
    `designer_user_id`, `password`, `issue_date`, `expire_date`, `account_id`, 
    `initial_deposit`, `bonus_amount`, `initial_points`, `is_installer`, `customer_type`, 
    `app_account`, `need_invoice`, `referrer_id`, `remark`, `email`, `position`, 
    `customer_manager_id`, `vip_level`, `customer_tags`, `status`, `creator`, 
    `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`
FROM `customer_customer`
WHERE NOT EXISTS (
    SELECT 1 FROM `customer_info` WHERE `customer_info`.`id` = `customer_customer`.`id`
);

-- ----------------------------
-- 迁移客户收货地址表数据
-- ----------------------------
INSERT INTO `customer_info_address` (
    `id`, `customer_id`, `receiver_name`, `receiver_mobile`, `province_id`, `city_id`, 
    `district_id`, `detail_address`, `postal_code`, `address_label`, `is_default`, 
    `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`
)
SELECT 
    `id`, `customer_id`, `receiver_name`, `receiver_mobile`, `province_id`, `city_id`, 
    `district_id`, `detail_address`, `postal_code`, `address_label`, `is_default`, 
    `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`
FROM `customer_address`
WHERE NOT EXISTS (
    SELECT 1 FROM `customer_info_address` WHERE `customer_info_address`.`id` = `customer_address`.`id`
);

-- ----------------------------
-- 迁移客户推荐关系表数据
-- ----------------------------
INSERT INTO `customer_info_referral` (
    `id`, `referrer_id`, `referee_id`, `referral_time`, `status`, `reward_amount`, 
    `reward_status`, `remark`, `creator`, `create_time`, `updater`, `update_time`, 
    `deleted`, `tenant_id`
)
SELECT 
    `id`, `referrer_id`, `referee_id`, `referral_time`, `status`, `reward_amount`, 
    `reward_status`, `remark`, `creator`, `create_time`, `updater`, `update_time`, 
    `deleted`, `tenant_id`
FROM `customer_referral`
WHERE NOT EXISTS (
    SELECT 1 FROM `customer_info_referral` WHERE `customer_info_referral`.`id` = `customer_referral`.`id`
);

-- ----------------------------
-- 迁移客户状态变更记录表数据
-- ----------------------------
INSERT INTO `customer_info_status_log` (
    `id`, `customer_id`, `old_status`, `new_status`, `change_reason`, `operator_id`, 
    `operator_name`, `approval_status`, `approval_user_id`, `approval_time`, 
    `approval_remark`, `creator`, `create_time`, `updater`, `update_time`, 
    `deleted`, `tenant_id`
)
SELECT 
    `id`, `customer_id`, `old_status`, `new_status`, `change_reason`, `operator_id`, 
    `operator_name`, `approval_status`, `approval_user_id`, `approval_time`, 
    `approval_remark`, `creator`, `create_time`, `updater`, `update_time`, 
    `deleted`, `tenant_id`
FROM `customer_status_log`
WHERE NOT EXISTS (
    SELECT 1 FROM `customer_info_status_log` WHERE `customer_info_status_log`.`id` = `customer_status_log`.`id`
);

-- ----------------------------
-- 迁移客户积分记录表数据
-- ----------------------------
INSERT INTO `customer_info_points_record` (
    `id`, `customer_id`, `record_type`, `points`, `balance_before`, `balance_after`,
    `source_type`, `source_id`, `exchange_type`, `exchange_id`, `expire_date`,
    `operator_id`, `operator_name`, `operation_reason`, `creator`, `create_time`,
    `updater`, `update_time`, `deleted`, `tenant_id`
)
SELECT
    `id`, `customer_id`, `record_type`, `points`, `balance_before`, `balance_after`,
    `source_type`, `source_id`, `exchange_type`, `exchange_id`, `expire_date`,
    `operator_id`, `operator_name`, `operation_reason`, `creator`, `create_time`,
    `updater`, `update_time`, `deleted`, `tenant_id`
FROM `customer_points_record`
WHERE NOT EXISTS (
    SELECT 1 FROM `customer_info_points_record` WHERE `customer_info_points_record`.`id` = `customer_points_record`.`id`
);

-- ----------------------------
-- 迁移客户账户操作日志表数据
-- ----------------------------
INSERT INTO `customer_info_account_log` (
    `id`, `customer_id`, `operation_type`, `operation_detail`, `operator_id`,
    `operator_name`, `operation_ip`, `operation_result`, `operation_remark`,
    `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`
)
SELECT
    `id`, `customer_id`, `operation_type`, `operation_detail`, `operator_id`,
    `operator_name`, `operation_ip`, `operation_result`, `operation_remark`,
    `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`
FROM `customer_account_log`
WHERE NOT EXISTS (
    SELECT 1 FROM `customer_info_account_log` WHERE `customer_info_account_log`.`id` = `customer_account_log`.`id`
);

-- ----------------------------
-- 迁移客户发票信息表数据
-- ----------------------------
INSERT INTO `customer_info_invoice_info` (
    `id`, `customer_id`, `invoice_type`, `invoice_title`, `tax_number`, `company_address`,
    `company_phone`, `bank_name`, `bank_account`, `receiver_name`, `receiver_phone`,
    `receiver_address`, `auto_invoice`, `invoice_threshold`, `is_default`,
    `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`
)
SELECT
    `id`, `customer_id`, `invoice_type`, `invoice_title`, `tax_number`, `company_address`,
    `company_phone`, `bank_name`, `bank_account`, `receiver_name`, `receiver_phone`,
    `receiver_address`, `auto_invoice`, `invoice_threshold`, `is_default`,
    `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`
FROM `customer_invoice_info`
WHERE NOT EXISTS (
    SELECT 1 FROM `customer_info_invoice_info` WHERE `customer_info_invoice_info`.`id` = `customer_invoice_info`.`id`
);

SET FOREIGN_KEY_CHECKS = 1;

-- ========================================
-- 执行说明
-- ========================================
/*
执行步骤：
1. 备份数据库（重要！）
2. 确保新表结构已创建（执行 customer-module.sql）
3. 执行本脚本迁移数据
4. 验证数据迁移结果
5. 更新应用程序使用新表名
6. 执行 drop-old-customer-tables.sql 删除旧表

注意事项：
1. 本脚本使用 NOT EXISTS 避免重复插入数据
2. 保持原有的 ID 值，确保关联关系不变
3. 迁移顺序：主表 → 子表，确保外键关系正确
4. 建议在测试环境先验证迁移效果

数据验证脚本：
-- 验证主表数据迁移
SELECT
    '客户主表' AS table_name,
    (SELECT COUNT(*) FROM customer_customer) AS old_count,
    (SELECT COUNT(*) FROM customer_info) AS new_count,
    CASE
        WHEN (SELECT COUNT(*) FROM customer_customer) = (SELECT COUNT(*) FROM customer_info)
        THEN '✓ 数据一致'
        ELSE '✗ 数据不一致'
    END AS status;

-- 验证子表数据迁移
SELECT
    '收货地址表' AS table_name,
    (SELECT COUNT(*) FROM customer_address) AS old_count,
    (SELECT COUNT(*) FROM customer_info_address) AS new_count,
    CASE
        WHEN (SELECT COUNT(*) FROM customer_address) = (SELECT COUNT(*) FROM customer_info_address)
        THEN '✓ 数据一致'
        ELSE '✗ 数据不一致'
    END AS status
UNION ALL
SELECT
    '推荐关系表' AS table_name,
    (SELECT COUNT(*) FROM customer_referral) AS old_count,
    (SELECT COUNT(*) FROM customer_info_referral) AS new_count,
    CASE
        WHEN (SELECT COUNT(*) FROM customer_referral) = (SELECT COUNT(*) FROM customer_info_referral)
        THEN '✓ 数据一致'
        ELSE '✗ 数据不一致'
    END AS status
UNION ALL
SELECT
    '状态日志表' AS table_name,
    (SELECT COUNT(*) FROM customer_status_log) AS old_count,
    (SELECT COUNT(*) FROM customer_info_status_log) AS new_count,
    CASE
        WHEN (SELECT COUNT(*) FROM customer_status_log) = (SELECT COUNT(*) FROM customer_info_status_log)
        THEN '✓ 数据一致'
        ELSE '✗ 数据不一致'
    END AS status
UNION ALL
SELECT
    '积分记录表' AS table_name,
    (SELECT COUNT(*) FROM customer_points_record) AS old_count,
    (SELECT COUNT(*) FROM customer_info_points_record) AS new_count,
    CASE
        WHEN (SELECT COUNT(*) FROM customer_points_record) = (SELECT COUNT(*) FROM customer_info_points_record)
        THEN '✓ 数据一致'
        ELSE '✗ 数据不一致'
    END AS status
UNION ALL
SELECT
    '账户日志表' AS table_name,
    (SELECT COUNT(*) FROM customer_account_log) AS old_count,
    (SELECT COUNT(*) FROM customer_info_account_log) AS new_count,
    CASE
        WHEN (SELECT COUNT(*) FROM customer_account_log) = (SELECT COUNT(*) FROM customer_info_account_log)
        THEN '✓ 数据一致'
        ELSE '✗ 数据不一致'
    END AS status
UNION ALL
SELECT
    '发票信息表' AS table_name,
    (SELECT COUNT(*) FROM customer_invoice_info) AS old_count,
    (SELECT COUNT(*) FROM customer_info_invoice_info) AS new_count,
    CASE
        WHEN (SELECT COUNT(*) FROM customer_invoice_info) = (SELECT COUNT(*) FROM customer_info_invoice_info)
        THEN '✓ 数据一致'
        ELSE '✗ 数据不一致'
    END AS status;
*/
