<execution>
<constraint>
## 质量保证系统约束
- **量化评估强制**：必须使用文档规定的量化指标进行质量评估
- **四维度检查强制**：结构完整性、业务准确性、测试完整性、可执行性四个维度必须全部检查
- **质量门禁约束**：未达到合格标准(70分)的转换结果不得输出
- **持续改进约束**：每次质量检查必须提供具体的改进建议
- **透明度约束**：质量检查过程和结果必须对用户完全透明
</constraint>

<rule>
## 质量评估执行规则
- **评分标准统一**：严格按照文档规定的评分标准执行
- **问题记录完整**：发现的每个质量问题都必须详细记录
- **改进建议具体**：提供的改进建议必须具体可操作
- **追溯关系建立**：建立质量问题与原始需求的追溯关系
- **基准对比执行**：与历史数据和行业标准进行对比分析
</rule>

<guideline>
## 质量保证指导原则
- **数据驱动决策**：基于量化数据进行质量判断和改进决策
- **预防胜于检测**：通过流程优化减少质量问题的产生
- **持续改进文化**：建立PDCA循环，推动质量持续提升
- **全过程质量**：从输入分析到输出交付的全过程质量管控
- **用户满意导向**：以最终用户的满意度为质量标准
- **风险驱动优先**：优先解决高风险和高影响的质量问题
</guideline>

<process>
## 自动质量保证系统完整流程

### Step 1: 质量检查准备 (5%)
```mermaid
flowchart TD
    A[转换完成] --> B[收集转换数据]
    B --> C[建立检查基准]
    C --> D[准备评估工具]
    D --> E[设置质量门禁]
    E --> F[开始四维度检查]
```

**检查准备清单**：
```yaml
数据收集:
  - 转换前测试点总数
  - 转换后测试用例总数
  - 业务规则识别数量
  - 功能点覆盖数量

基准设置:
  - 结构完整率目标: > 95%
  - 功能覆盖率目标: > 90%
  - 约束识别率目标: > 85%
  - 可执行性率目标: = 100%

工具准备:
  - 结构检查工具
  - 业务规则验证工具
  - 覆盖率分析工具
  - 可执行性验证工具
```

### Step 2: 结构完整性检查 (25%)
```mermaid
graph TD
    A[结构检查开始] --> B[Given部分检查]
    B --> C[When部分检查]
    C --> D[Then部分检查]
    D --> E[命名规范检查]
    E --> F[格式一致性检查]
    F --> G[结构评分计算]
```

**结构完整性检查详细流程**：

#### Given部分检查标准
```yaml
必需要素检查:
  系统状态 (权重20%):
    ✓ 用户登录状态描述
    ✓ 系统运行模式说明
    ✓ 模块访问权限确认
    
  测试数据准备 (权重25%):
    ✓ 有效测试数据示例
    ✓ 边界值数据设定
    ✓ 多格式数据准备
    
  环境条件 (权重15%):
    ✓ 网络连接状态
    ✓ 外部依赖可用性
    ✓ 数据库连接状态
    
  业务上下文 (权重20%):
    ✓ 业务场景描述
    ✓ 用户操作阶段
    ✓ 数据关联关系
    
  业务约束 (权重20%):
    ✓ 功能使用限制
    ✓ 操作模式限制
    ✓ 权限边界约束

评分公式:
Given得分 = Σ(要素完整度 × 权重) × 100
```

#### When部分检查标准
```yaml
必需要素检查:
  操作步骤 (权重40%):
    ✓ 界面元素操作明确
    ✓ 操作顺序逻辑清晰
    ✓ 多步骤操作完整
    
  输入规范 (权重35%):
    ✓ 输入内容格式明确
    ✓ 多种格式测试包含
    ✓ 特殊字符处理说明
    
  模式操作 (权重25%):
    ✓ 搜索模式切换描述
    ✓ 显示模式调整说明
    ✓ 批量操作模式描述

评分公式:
When得分 = Σ(要素完整度 × 权重) × 100
```

#### Then部分检查标准
```yaml
必需要素检查:
  功能验证 (权重25%):
    ✓ 返回数据准确性
    ✓ 业务逻辑正确性
    ✓ 操作结果符合性
    
  界面验证 (权重20%):
    ✓ 视觉反馈正确性
    ✓ 状态指示准确性
    ✓ 提示信息恰当性
    
  数据验证 (权重20%):
    ✓ 数据变更正确性
    ✓ 操作历史完整性
    ✓ 审计追踪准确性
    
  用户体验验证 (权重20%):
    ✓ 加载反馈验证
    ✓ 错误处理验证
    ✓ 界面状态验证
    
  业务规则验证 (权重15%):
    ✓ 业务约束执行
    ✓ 权限控制准确性
    ✓ 默认行为正确性

评分公式:
Then得分 = Σ(要素完整度 × 权重) × 100
```

#### 命名规范检查
```yaml
命名格式验证:
  标准格式: "功能模块-操作类型-数据类型-测试分类"
  
  组件检查:
    功能模块合规性: 是否使用规范的功能模块名称
    操作类型准确性: 是否准确反映操作类型
    数据类型明确性: 是否明确标识数据类型
    测试分类正确性: 是否正确分类测试类型
    
  命名示例验证:
    ✓ "任务单号搜索-精准搜索-单条记录-正常数据测试"
    ✓ "SKC搜索-模糊搜索-批量记录-边界值测试"
    ✗ "搜索功能测试" (不符合格式)
    ✗ "测试用例1" (无意义命名)

评分标准:
- 完全符合: 100分
- 基本符合: 80分
- 部分符合: 60分
- 不符合: 0分
```

### Step 3: 业务准确性检查 (25%)
```mermaid
flowchart TD
    A[业务检查开始] --> B[业务规则识别验证]
    B --> C[权限控制理解验证]
    C --> D[默认行为验证]
    D --> E[操作流程符合性]
    E --> F[业务准确性评分]
```

**业务准确性检查详细标准**：

#### 业务规则识别验证
```yaml
约束识别检查:
  关键词识别准确性:
    限制性词汇: ["不支持", "仅限", "禁止", "无法", "限制"]
    权限性词汇: ["DPS用户", "管理员", "普通用户", "审核人员"]
    默认性词汇: ["默认", "自动", "初始", "预设"]
    
  转换完整性验证:
    ✓ 每个识别的约束都有对应测试用例
    ✓ 约束验证覆盖正面和负面场景
    ✓ 约束描述准确反映原始需求
    
  业务逻辑一致性:
    ✓ 业务规则间无冲突
    ✓ 约束条件逻辑合理
    ✓ 异常处理机制完善

评分计算:
约束识别率 = (正确识别约束数 / 实际约束总数) × 100%
```

#### 权限控制理解验证
```yaml
权限边界检查:
  水平权限验证:
    ✓ 同级用户数据访问边界
    ✓ 部门间数据隔离验证
    ✓ 业务范围权限控制
    
  垂直权限验证:
    ✓ 上下级权限继承关系
    ✓ 管理员特权验证
    ✓ 审核权限独立性
    
  功能权限验证:
    ✓ 操作功能访问控制
    ✓ 数据导出权限限制
    ✓ 批量操作权限管控
    
  数据权限验证:
    ✓ 敏感数据访问控制
    ✓ 历史数据查看权限
    ✓ 跨模块数据权限

权限理解得分:
权限控制准确率 = (正确理解权限点数 / 总权限点数) × 100%
```

### Step 4: 测试完整性检查 (25%)
```mermaid
graph TD
    A[完整性检查] --> B[功能覆盖分析]
    B --> C[场景覆盖分析]
    C --> D[边界值覆盖分析]
    D --> E[异常场景覆盖分析]
    E --> F[用户体验覆盖分析]
    F --> G[完整性评分]
```

**测试完整性评估矩阵**：

#### 功能覆盖度分析
```yaml
功能点识别:
  核心功能 (权重40%):
    - 任务单号搜索: 精准搜索、模糊搜索
    - SKC搜索: 单条搜索、批量搜索
    - 设计款号搜索: 格式验证、关联查询
    - 拍摄单号搜索: 状态筛选、时间范围
    
  辅助功能 (权重30%):
    - 创建人筛选: 下拉选择、自动填充
    - 审核人筛选: 权限验证、多选支持
    - 时间筛选: 日期范围、快捷选择
    
  扩展功能 (权重30%):
    - 结果导出: 格式选择、权限控制
    - 历史记录: 查询历史、快速重放
    - 高级筛选: 组合条件、保存设置

覆盖率计算:
功能覆盖率 = (已覆盖功能点数 / 总功能点数) × 100%
目标: > 90%
```

#### 场景覆盖度分析
```yaml
场景类型分布:
  正常场景 (权重35%):
    - 标准业务流程验证
    - 常见用户操作场景
    - 典型数据处理场景
    
  边界场景 (权重30%):
    - 输入边界值测试
    - 数量限制边界测试
    - 时间边界条件测试
    
  异常场景 (权重25%):
    - 网络异常处理
    - 系统错误恢复
    - 并发冲突处理
    
  用户体验场景 (权重10%):
    - 界面响应性测试
    - 操作便利性测试
    - 错误友好性测试

场景覆盖评估:
各类场景覆盖率 = (已覆盖场景数 / 该类场景总数) × 100%
综合场景覆盖率 = Σ(各类场景覆盖率 × 权重)
```

### Step 5: 可执行性检查 (20%)
```mermaid
flowchart TD
    A[可执行性检查] --> B[操作步骤明确性]
    B --> C[验证要求量化性]
    C --> D[测试数据充分性]
    D --> E[环境要求清晰性]
    E --> F[可执行性评分]
```

**可执行性检查标准**：

#### 操作步骤明确性检查
```yaml
步骤清晰度验证:
  具体性检查:
    ✓ 每个步骤都有具体的界面元素描述
    ✓ 操作动作明确(点击、输入、选择、拖拽等)
    ✓ 参数值具体明确，避免模糊描述
    
  顺序性检查:
    ✓ 步骤顺序逻辑正确
    ✓ 前后步骤依赖关系清晰
    ✓ 分支操作路径明确标识
    
  完整性检查:
    ✓ 操作流程从开始到结束完整
    ✓ 异常情况处理步骤包含
    ✓ 清理和恢复步骤考虑周全

明确性评分:
步骤明确率 = (明确步骤数 / 总步骤数) × 100%
目标: = 100%
```

#### 验证要求量化性检查
```yaml
量化标准验证:
  可测量性检查:
    ✓ 验证点有明确的判断标准
    ✓ 数值型验证有具体的期望值
    ✓ 状态型验证有清晰的状态描述
    
  可观察性检查:
    ✓ 验证结果在界面上可见
    ✓ 数据变化可以通过工具查询
    ✓ 系统行为可以通过日志跟踪
    
  可重复性检查:
    ✓ 验证结果在相同条件下一致
    ✓ 验证方法不依赖主观判断
    ✓ 验证过程可以自动化执行

量化性评分:
验证量化率 = (可量化验证点数 / 总验证点数) × 100%
目标: > 95%
```

### Step 6: 综合质量评估与报告生成 (5%)
```mermaid
graph TD
    A[四维度检查完成] --> B[加权综合评分]
    B --> C[质量等级评定]
    C --> D[问题汇总分析]
    D --> E[改进建议生成]
    E --> F[质量报告输出]
```

**综合评分算法**：
```yaml
权重设置:
  结构完整性: 30%
  业务准确性: 25%
  测试完整性: 25%
  可执行性: 20%

综合评分公式:
总分 = 结构得分×0.3 + 业务得分×0.25 + 完整性得分×0.25 + 可执行性得分×0.2

质量等级划分:
  优秀 (90-100分):
    - 所有维度都达到优秀标准
    - 可以直接投入使用
    
  良好 (80-89分):
    - 大部分维度达到良好标准
    - 少量优化后可投入使用
    
  合格 (70-79分):
    - 基本达到可用标准
    - 需要重点改进后使用
    
  需改进 (<70分):
    - 存在重大质量问题
    - 需要重新转换或大幅修改
```

## 质量报告生成标准

### 报告结构模板
```markdown
# 半结构化测试用例质量检查报告

## 总体评估
- **质量等级**: [优秀/良好/合格/需改进]
- **综合得分**: [分数]/100
- **转换成功率**: [成功用例数]/[总用例数] = [百分比]
- **质量门禁**: [通过/未通过]

## 四维度详细分析

### 1. 结构完整性分析 (权重30%)
- **维度得分**: [分数]/100
- **结构完整率**: [百分比]
- **命名规范率**: [百分比]
- **要素包含率**: [百分比]

**详细指标**:
- Given部分完整性: [分数]/100
- When部分完整性: [分数]/100  
- Then部分完整性: [分数]/100
- 命名规范符合度: [分数]/100

**发现问题**:
- [具体结构问题列表]
- [影响程度评估]

### 2. 业务准确性分析 (权重25%)
- **维度得分**: [分数]/100
- **业务规则识别率**: [百分比]
- **权限理解准确率**: [百分比]
- **流程符合度**: [百分比]

**详细指标**:
- 约束条件识别: [正确数]/[总数] = [百分比]
- 权限边界理解: [正确数]/[总数] = [百分比]
- 默认行为验证: [覆盖数]/[总数] = [百分比]

**发现问题**:
- [业务理解偏差列表]
- [风险评估]

### 3. 测试完整性分析 (权重25%)
- **维度得分**: [分数]/100
- **功能覆盖率**: [百分比]
- **场景覆盖率**: [百分比]
- **边界值覆盖率**: [百分比]

**覆盖度矩阵**:
| 功能模块 | 正常场景 | 边界场景 | 异常场景 | 覆盖率 |
|---------|---------|---------|---------|--------|
| 任务单搜索 | [数量] | [数量] | [数量] | [百分比] |
| SKC搜索 | [数量] | [数量] | [数量] | [百分比] |
| 设计款搜索 | [数量] | [数量] | [数量] | [百分比] |

**发现问题**:
- [覆盖缺口列表]
- [补充建议]

### 4. 可执行性分析 (权重20%)
- **维度得分**: [分数]/100
- **操作明确率**: [百分比]
- **验证量化率**: [百分比]
- **数据充分率**: [百分比]

**详细指标**:
- 步骤可执行性: [可执行数]/[总步骤数] = [百分比]
- 验证可量化性: [可量化数]/[总验证点数] = [百分比]
- 环境要求清晰性: [清晰数]/[总要求数] = [百分比]

**发现问题**:
- [可执行性问题列表]
- [改进方向]

## 问题汇总与风险评估

### 高风险问题 (影响质量等级)
1. [问题描述]
   - 影响范围: [具体范围]
   - 风险等级: 高/中/低
   - 建议措施: [具体建议]

### 中风险问题 (影响使用效果)
[问题列表及建议]

### 低风险问题 (优化建议)
[问题列表及建议]

## 改进建议

### 立即改进项 (必须解决)
1. [具体改进项]
   - 改进方案: [详细方案]
   - 预期效果: [期望改进效果]
   - 实施难度: [难度评估]

### 优化改进项 (建议解决)
[改进项列表]

### 扩展改进项 (未来考虑)
[扩展建议]

## 质量趋势分析
- 与历史数据对比: [对比结果]
- 质量改进趋势: [趋势分析]
- 预期质量目标: [目标设定]
```
</process>

<criteria>
## 质量保证系统评价标准

### 检查系统质量标准
- ✅ 四维度检查覆盖完整 = 100%
- ✅ 量化指标计算准确 > 99%
- ✅ 质量等级评定合理 > 95%
- ✅ 问题识别准确率 > 90%

### 报告质量标准
- ✅ 报告结构完整清晰 = 100%
- ✅ 数据分析准确可靠 > 99%
- ✅ 问题描述具体明确 > 95%
- ✅ 改进建议可操作性 > 90%

### 改进建议质量标准
- ✅ 建议针对性强 > 95%
- ✅ 实施方案可行性 > 90%
- ✅ 预期效果合理性 > 95%
- ✅ 优先级排序准确性 > 90%

### 系统性能标准
- ✅ 检查执行效率 < 2分钟
- ✅ 报告生成时间 < 30秒
- ✅ 准确性稳定性 > 99%
- ✅ 用户满意度 > 90%
</criteria>
</execution>