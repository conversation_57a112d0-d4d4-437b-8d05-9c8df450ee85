<execution>
<constraint>
## 基于学习文档的转换约束
- **MCP工具集成强制**：必须优先检查和使用xmind-analysis相关MCP工具
- **Given-When-Then结构强制**：95%以上测试用例必须采用标准结构
- **业务规则识别强制**：所有"不支持"、"仅限"、"禁止"等约束必须转换为专门测试用例
- **命名规范强制**：必须遵循"功能模块-操作类型-数据类型-测试分类"格式
- **质量检查强制**：转换完成后必须执行四维度质量检查
- **可执行性强制**：100%测试用例必须具备明确操作步骤和验证标准
</constraint>

<rule>
## 转换执行强制规则
- **工具优先级规则**：优先使用可用MCP工具，工具不可用时不阻塞流程
- **结构完整性规则**：每个测试用例必须包含完整的Given-When-Then三部分
- **业务约束转换规则**：每个业务约束必须对应至少一个验证测试用例
- **边界值扩展规则**：基于边界值分析为每种输入格式创建专门测试用例
- **用户体验补充规则**：必须包含界面反馈、错误处理、操作便利性验证
- **质量达标规则**：必须达到文档规定的质量标准才能输出结果
</rule>

<guideline>
## 转换指导原则
- **系统性优先**：运用系统化测试设计方法，确保覆盖完整性
- **标准化优先**：使用统一的模板和格式，保证一致性
- **可执行性优先**：确保每个测试用例都具备实际可操作性
- **质量导向**：以质量检查结果为导向，持续优化转换结果
- **用户体验关注**：重视用户交互体验的验证要求
- **业务规则敏感**：对业务约束和限制保持高度敏感性
</guideline>

<process>
## 半结构化测试用例转换完整流程

### Step 1: MCP工具检查与格式识别 (10%)
```mermaid
flowchart TD
    A[开始转换] --> B[检查可用MCP工具]
    B --> C{发现xmind工具?}
    C -->|是| D[使用工具解析XMind]
    C -->|否| E[提供配置建议]
    D --> F[获取结构化内容]
    E --> G[处理文本内容]
    F --> H[统一内容格式]
    G --> H
    H --> I[进入分析阶段]
```

**MCP工具检查策略**：
- **工具发现**：扫描包含"xmind"关键词的MCP工具
- **优先使用**：mcp_xmind-analysis_read_xmind等系列工具
- **容错处理**：工具不可用时继续处理文本内容
- **透明反馈**：清晰报告工具使用状态

### Step 2: 输入分析与预检查 (15%)
```mermaid
graph TD
    A[获取内容] --> B[测试点统计]
    B --> C[功能模块识别]
    C --> D[业务规则提取]
    D --> E[用户权限分析]
    E --> F[输入格式识别]
    F --> G[生成分析报告]
```

**分析重点**：
```yaml
功能点识别:
  - 搜索功能：任务单号、SKC、设计款号、拍摄单号
  - 筛选功能：创建人、审核人、时间范围
  - 操作模式：精准搜索、模糊搜索、批量操作

业务规则挖掘:
  关键词模式: ["不支持", "仅限", "禁止", "无法", "限制"]
  权限控制: ["DPS用户", "管理员", "普通用户"]
  默认行为: ["默认", "自动", "初始", "预设"]

输入格式变化:
  - 分隔符类型：逗号、空格、混合分隔符
  - 数据边界：空值、最小值、最大值、超长
  - 特殊字符：SQL注入字符、XSS攻击字符
```

### Step 3: Given-When-Then结构设计 (40%)
```mermaid
flowchart TD
    A[分析结果] --> B[设计Given部分]
    B --> C[设计When部分]
    C --> D[设计Then部分]
    D --> E[命名规范化]
    E --> F[结构完整性检查]
    
    B --> B1[系统状态]
    B --> B2[测试数据]
    B --> B3[环境条件]
    B --> B4[业务上下文]
    B --> B5[业务约束]
    
    C --> C1[操作步骤]
    C --> C2[输入规范]
    C --> C3[模式操作]
    
    D --> D1[功能验证]
    D --> D2[界面验证]
    D --> D3[数据验证]
    D --> D4[用户体验验证]
    D --> D5[业务规则验证]
```

**Given部分设计标准**：
```yaml
系统状态 (必填):
  - 用户登录状态: "已登录的DPS用户"
  - 系统运行模式: "系统正常运行，搜索功能可用"
  - 模块访问权限: "具备任务单搜索功能访问权限"

测试数据准备 (必填):
  - 有效数据: "存在任务单号: T202401001, T202401002"
  - 边界数据: "最短单号(6位)，最长单号(20位)"
  - 格式数据: "逗号分隔: T001,T002；空格分隔: T001 T002"

业务约束 (重要):
  - 搜索限制: "单次搜索不超过100条记录"
  - 权限限制: "只能搜索当前用户有权限的任务单"
```

**When部分设计标准**：
```yaml
操作步骤 (必填):
  - 界面操作: "1. 点击任务单号搜索框"
  - 输入操作: "2. 输入搜索条件: T202401001,T202401002"
  - 执行操作: "3. 点击搜索按钮"

输入规范 (必填):
  - 格式要求: "多个单号用逗号分隔"
  - 数据示例: "T202401001,T202401002,T202401003"
  - 边界测试: "输入超长单号(21位字符)"

模式操作 (如适用):
  - 模式切换: "选择精准搜索模式"
  - 批量操作: "启用批量搜索模式"
```

**Then部分设计标准**：
```yaml
功能验证 (必填):
  - 数据准确性: "返回3条完全匹配的任务单记录"
  - 数据完整性: "每条记录包含任务单号、状态、创建时间等完整信息"
  - 业务逻辑: "只返回当前用户有权限查看的任务单"

界面验证 (必填):
  - 加载反馈: "显示搜索进度指示器"
  - 结果展示: "搜索结果以表格形式清晰展示"
  - 状态更新: "搜索框状态更新为已完成"

用户体验验证 (重要):
  - 操作反馈: "搜索过程中提供明确的进度反馈"
  - 错误处理: "无匹配结果时显示友好提示信息"
  - 操作便利性: "支持快捷键操作，提升操作效率"

业务规则验证 (重要):
  - 权限控制: "验证用户只能看到授权范围内的任务单"
  - 数量限制: "验证单次搜索不超过100条记录限制"
  - 格式容错: "验证系统对不同分隔符格式的正确处理"
```

### Step 4: 测试用例扩展与补充 (20%)
```mermaid
graph TD
    A[基础用例] --> B[边界值扩展]
    B --> C[异常场景补充]
    C --> D[用户体验用例]
    D --> E[业务约束用例]
    E --> F[性能验证用例]
```

**扩展策略**：
```yaml
边界值测试扩展:
  - 输入长度边界: 空输入、最短输入、最长输入、超长输入
  - 数量边界: 单条、多条、最大限制、超出限制
  - 时间边界: 当日、跨日、历史数据、未来数据

异常场景补充:
  - 网络异常: 搜索过程中网络中断
  - 并发冲突: 多用户同时搜索相同条件
  - 系统异常: 数据库连接异常、服务不可用

用户体验专项:
  - 加载体验: 长时间搜索的用户反馈
  - 错误体验: 各种错误情况的友好提示
  - 操作体验: 快捷操作和批量操作的便利性

业务约束专项:
  - 权限验证: 不同用户角色的权限边界
  - 功能限制: 各种业务限制的正确执行
  - 默认行为: 系统默认设置的正确性
```

### Step 5: 自动质量检查 (15%)
```mermaid
flowchart TD
    A[转换完成] --> B[结构完整性检查]
    B --> C[业务准确性检查]
    C --> D[测试完整性检查]
    D --> E[可执行性检查]
    E --> F[质量评分]
    F --> G{达标?}
    G -->|是| H[输出结果]
    G -->|否| I[优化建议]
    I --> J[重新转换]
```

**四维度质量检查**：

#### 结构完整性检查
```yaml
检查项目:
  ✓ Given-When-Then结构完整率 > 95%
  ✓ 命名规范符合率 > 95%
  ✓ 必需要素包含率 = 100%
  ✓ 描述清晰无歧义率 > 90%

评估标准:
  - Given包含: 系统状态、测试数据、环境条件、业务上下文、业务约束
  - When包含: 操作步骤、输入规范、模式操作
  - Then包含: 功能验证、界面验证、数据验证、用户体验验证、业务规则验证
```

#### 业务准确性检查
```yaml
检查项目:
  ✓ 业务规则识别准确率 > 85%
  ✓ 权限控制理解正确率 > 90%
  ✓ 默认行为验证覆盖率 > 80%
  ✓ 操作流程符合实际业务率 > 95%

重点验证:
  - 所有约束性描述都有对应测试用例
  - 用户角色和权限都有专门验证
  - 默认行为和自动填充都有验证用例
```

#### 测试完整性检查
```yaml
检查项目:
  ✓ 功能覆盖率 > 90%
  ✓ 边界值测试覆盖率 > 80%
  ✓ 异常场景覆盖率 > 70%
  ✓ 用户体验验证覆盖率 > 75%

覆盖度验证:
  - 正常场景、边界值、异常场景都有覆盖
  - 不同输入格式都有专门测试
  - 界面反馈、错误处理都有验证
```

#### 可执行性检查
```yaml
检查项目:
  ✓ 操作步骤具体明确率 = 100%
  ✓ 验证要求可量化率 > 95%
  ✓ 测试数据准备充分率 = 100%
  ✓ 环境要求清晰率 = 100%

实用性验证:
  - 每个步骤都可以被具体执行
  - 每个验证点都有明确判断标准
  - 测试数据都有具体示例值
```

## 输出格式标准

### 转换结果文档结构
```markdown
# 半结构化测试用例文档

## 文档信息
- 文档标题：[功能模块]半结构化测试用例
- 版本信息：v1.0
- 创建时间：[YYYY-MM-DD]
- 转换来源：[XMind解析/文本输入]
- 测试用例总数：[数量]统计
- MCP工具状态：[工具使用情况]

## 测试用例列表

### [用例编号] [标准化标题]
**Given (前置条件):**
- 系统状态：[具体状态描述]
- 测试数据：[具体数据准备]
- 环境条件：[环境要求]
- 业务上下文：[业务场景]
- 业务约束：[相关限制]

**When (执行操作):**
- 操作步骤：[具体步骤序列]
- 输入规范：[输入格式要求]
- 模式操作：[如适用]

**Then (预期结果):**
- 功能验证：[功能正确性验证]
- 界面验证：[界面反馈验证]
- 数据验证：[数据准确性验证]
- 用户体验验证：[体验相关验证]
- 业务规则验证：[业务约束验证]

**备注：**[必要的补充说明]
```

### 质量检查报告格式
```markdown
## 质量检查报告

### 总体评估
- 质量等级：[优秀/良好/合格/需改进]
- 综合得分：[分数]/100
- 转换成功率：[百分比]
- 达标用例数：[数量]/[总数]

### 详细分析
#### 结构完整性分析
- 结构完整率：[百分比]
- 命名规范率：[百分比]
- 要素包含率：[百分比]

#### 业务准确性分析
- 规则识别率：[百分比]
- 权限理解率：[百分比]
- 流程符合率：[百分比]

#### 测试完整性分析
- 功能覆盖率：[百分比]
- 边界值覆盖率：[百分比]
- 异常场景覆盖率：[百分比]

#### 可执行性分析
- 操作明确率：[百分比]
- 验证量化率：[百分比]
- 数据充分率：[百分比]

### 发现问题
- [具体问题描述]
- [影响和风险]
- [建议改进方案]

### 改进建议
- [具体优化建议]
- [补充测试场景]
- [质量提升方向]
```
</process>

<criteria>
## 转换质量评价标准

### 结构质量标准
- ✅ Given-When-Then结构完整率 > 95%
- ✅ 命名遵循标准格式 > 95%
- ✅ 必需要素包含率 = 100%
- ✅ 描述清晰无歧义 > 90%

### 业务质量标准
- ✅ 业务规则识别准确率 > 85%
- ✅ 权限控制理解正确率 > 90%
- ✅ 操作流程符合实际业务 > 95%
- ✅ 默认行为验证覆盖 > 80%

### 测试质量标准
- ✅ 功能点覆盖率 > 90%
- ✅ 边界值测试覆盖率 > 80%
- ✅ 异常场景覆盖率 > 70%
- ✅ 用户体验验证覆盖率 > 75%

### 可执行性标准
- ✅ 操作步骤具体明确 = 100%
- ✅ 验证要求可量化 > 95%
- ✅ 测试数据准备充分 = 100%
- ✅ 环境要求清晰明确 = 100%

### 工具集成标准
- ✅ MCP工具检查和使用正确
- ✅ 透明报告工具状态
- ✅ 容错处理机制有效
- ✅ 处理结果准确可靠
</criteria>
</execution>