# 易潮智能家居系统 - 三级租户层级改造方案

## 项目概述

### 改造目标
将现有的单层租户系统改造为三级租户层级体系：**系统管理员 → 分销商 → 分店**，实现上级租户可以查看和管理下级所有租户数据的层级化权限管理。

### 核心需求
1. **租户层级结构**：建立三级租户层级关系
2. **数据访问权限**：上级租户可访问下级所有租户数据
3. **权限继承机制**：实现层级化的权限管理和数据访问控制

## 1. 现状分析与差距识别

### 1.1 当前系统能力

**✅ 已具备的能力：**
- 完整的租户CRUD操作和生命周期管理
- 基于MyBatis Plus的行级数据隔离机制
- ThreadLocal-based的租户上下文管理
- 多层安全过滤器和越权防护机制
- Redis Key级别的租户缓存隔离
- 基于TenantPackage的权限套餐系统

**❌ 缺失的能力（需要改造）：**
- 租户层级关系：当前TenantDO没有父子关系字段
- 层级数据访问：无法实现上级访问下级数据
- 层级权限继承：缺乏层级化的权限管理机制
- 跨层级上下文：TenantContextHolder只支持单租户上下文
- 层级化数据隔离：MyBatis拦截器不支持层级查询

### 1.2 技术架构差距

当前系统采用严格的租户隔离策略，每个租户只能访问自己的数据。需要改造为支持层级化访问的灵活隔离机制。

## 2. 数据库设计改造方案

### 2.1 核心表结构改造

**system_tenant表新增字段：**
```sql
ALTER TABLE system_tenant ADD COLUMN parent_id BIGINT NULL COMMENT '父租户ID';
ALTER TABLE system_tenant ADD COLUMN tenant_level TINYINT NOT NULL DEFAULT 1 COMMENT '租户层级(1=系统管理员,2=分销商,3=分店)';
ALTER TABLE system_tenant ADD COLUMN tenant_path VARCHAR(500) NULL COMMENT '租户路径，如/1/2/3';
ALTER TABLE system_tenant ADD COLUMN root_tenant_id BIGINT NULL COMMENT '根租户ID';

-- 添加索引
ALTER TABLE system_tenant ADD INDEX idx_parent_id (parent_id);
ALTER TABLE system_tenant ADD INDEX idx_tenant_level (tenant_level);
ALTER TABLE system_tenant ADD INDEX idx_root_tenant_id (root_tenant_id);
```

**新增system_tenant_hierarchy表（闭包表）：**
```sql
CREATE TABLE system_tenant_hierarchy (
  id BIGINT NOT NULL AUTO_INCREMENT COMMENT '层级关系ID',
  ancestor_id BIGINT NOT NULL COMMENT '祖先租户ID',
  descendant_id BIGINT NOT NULL COMMENT '后代租户ID',
  level_diff TINYINT NOT NULL COMMENT '层级差距',
  create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (id),
  UNIQUE KEY uk_ancestor_descendant (ancestor_id, descendant_id),
  KEY idx_ancestor_id (ancestor_id),
  KEY idx_descendant_id (descendant_id),
  KEY idx_level_diff (level_diff)
) COMMENT = '租户层级关系表';
```

### 2.2 数据模型设计

**层级定义：**
- Level 1：系统管理员（顶级租户）- 可管理所有分销商和分店
- Level 2：分销商（中级租户）- 可管理下属分店
- Level 3：分店（底级租户）- 只能管理自己的数据

**设计模式组合：**
1. **邻接列表模型**：parent_id存储直接父子关系
2. **闭包表模型**：system_tenant_hierarchy存储所有祖先-后代关系
3. **路径枚举模型**：tenant_path存储完整路径，便于快速查询

## 3. 数据隔离机制改造

### 3.1 层级化数据库拦截器

**核心改造：TenantDatabaseInterceptor**
```java
public class HierarchicalTenantDatabaseInterceptor implements TenantLineHandler {
    
    @Override
    public Expression getTenantId() {
        TenantAccessMode mode = TenantContextHolder.getAccessMode();
        
        switch (mode) {
            case SELF_ONLY:
                return new LongValue(TenantContextHolder.getRequiredTenantId());
                
            case INCLUDE_CHILDREN:
                Set<Long> accessibleIds = TenantContextHolder.getAccessibleTenantIds();
                return buildInExpression(accessibleIds);
                
            case CROSS_TENANT:
                return handleCrossTenantAccess();
                
            default:
                return new LongValue(TenantContextHolder.getRequiredTenantId());
        }
    }
    
    private Expression buildInExpression(Set<Long> tenantIds) {
        List<Expression> expressions = tenantIds.stream()
            .map(LongValue::new)
            .collect(Collectors.toList());
        return new InExpression(new Column("tenant_id"), new ExpressionList(expressions));
    }
}
```

### 3.2 访问模式定义

```java
public enum TenantAccessMode {
    SELF_ONLY,          // 仅访问自己租户数据
    INCLUDE_CHILDREN,   // 包含下级租户数据
    CROSS_TENANT,       // 跨租户访问（特殊权限）
    HIERARCHY_VIEW      // 层级视图（用于管理界面）
}
```

## 4. 权限控制机制设计

### 4.1 层级化权限注解

```java
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface HierarchicalDataPermission {
    TenantAccessMode accessMode() default TenantAccessMode.SELF_ONLY;
    boolean enable() default true;
    int minTenantLevel() default 3;  // 需要的最小租户层级
    String condition() default "";   // 权限检查表达式（SpEL）
}
```

### 4.2 权限继承规则

**数据访问权限：**
- 系统管理员：可访问所有租户数据
- 分销商：可访问自己和下属分店数据
- 分店：仅可访问自己的数据

**管理权限：**
- 系统管理员：可管理所有租户
- 分销商：可管理下属分店
- 分店：仅可管理自己

### 4.3 使用示例

```java
@RestController
@RequestMapping("/admin-api/business")
public class BusinessController {
    
    @GetMapping("/list-with-children")
    @HierarchicalDataPermission(
        accessMode = TenantAccessMode.INCLUDE_CHILDREN,
        minTenantLevel = 2  // 最低需要分销商级别
    )
    public CommonResult<List<BusinessDataVO>> getBusinessDataWithChildren() {
        List<BusinessDataDO> dataList = businessService.getBusinessDataIncludeChildren();
        return success(BusinessConvert.INSTANCE.convertList(dataList));
    }
}
```

## 5. 租户上下文管理改造

### 5.1 增强的TenantContextHolder

**新增字段：**
```java
public class TenantContextHolder {
    // 原有字段
    private static final ThreadLocal<Long> TENANT_ID = new TransmittableThreadLocal<>();
    private static final ThreadLocal<Boolean> IGNORE = new TransmittableThreadLocal<>();
    
    // 新增字段
    private static final ThreadLocal<TenantAccessMode> ACCESS_MODE = new TransmittableThreadLocal<>();
    private static final ThreadLocal<Set<Long>> ACCESSIBLE_TENANT_IDS = new TransmittableThreadLocal<>();
    private static final ThreadLocal<TenantHierarchyInfo> HIERARCHY_INFO = new TransmittableThreadLocal<>();
    
    // 新增方法
    public static boolean canAccessTenant(Long targetTenantId) {
        TenantHierarchyInfo hierarchyInfo = getHierarchyInfo();
        return hierarchyInfo != null && hierarchyInfo.canAccess(targetTenantId);
    }
}
```

### 5.2 层级化租户工具类

```java
@Component
public class HierarchicalTenantUtils {
    
    /**
     * 在指定租户上下文中执行任务，包含其所有下级租户数据
     */
    public static void executeWithChildren(Long tenantId, Runnable runnable) {
        executeWithHierarchy(tenantId, TenantAccessMode.INCLUDE_CHILDREN, runnable);
    }
    
    /**
     * 切换到子租户上下文执行任务
     */
    public static void switchToChildTenant(Long childTenantId, Runnable runnable) {
        // 验证权限后执行切换
        executeWithHierarchy(childTenantId, TenantAccessMode.SELF_ONLY, runnable);
    }
}
```

## 6. 实现难点与风险分析

### 6.1 技术难点

**数据库层面：**
- MyBatis Plus拦截器需要支持复杂的IN条件表达式
- 闭包表在租户层级变更时的级联更新复杂
- 多租户IN查询的性能优化挑战

**应用层面：**
- 多种访问模式的上下文管理复杂性
- ThreadLocal的正确清理和异步场景处理
- 现有业务代码的兼容性改造

**系统架构：**
- 租户层级信息的缓存一致性
- 跨租户操作的事务边界处理
- 分布式环境下的数据同步

### 6.2 风险缓解策略

**性能风险：**
```sql
-- 优化索引策略
CREATE INDEX idx_tenant_id_composite ON business_table (tenant_id, create_time, id);
CREATE INDEX idx_hierarchy_ancestor ON system_tenant_hierarchy (ancestor_id, level_diff);
```

**内存泄漏风险：**
```java
@Component
public class TenantContextCleanupFilter implements Filter {
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) 
            throws IOException, ServletException {
        try {
            chain.doFilter(request, response);
        } finally {
            TenantContextHolder.clear(); // 确保清理ThreadLocal
        }
    }
}
```

**安全风险：**
- 实现完整的访问审计日志
- 添加异常访问行为检测
- 敏感数据的分级脱敏处理

## 7. 工作量评估与实施计划

### 7.1 总体工作量

**总工作量：121人天（约6个月，2人团队）**

| 阶段 | 工作量 | 风险等级 | 关键产出 |
|------|--------|----------|----------|
| 第一阶段：基础设施改造 | 18天 | 中 | 数据模型和基础服务 |
| 第二阶段：核心框架改造 | 34天 | 高 | 多租户框架升级 |
| 第三阶段：业务功能开发 | 43天 | 中 | 完整业务功能 |
| 第四阶段：性能优化与上线 | 26天 | 高 | 生产就绪系统 |

### 7.2 分阶段实施策略

**阶段一：基础设施改造（3周）**
- 完成数据库表结构设计和创建
- 实现租户层级关系的CRUD操作
- 完成现有数据的平滑迁移

**阶段二：核心框架改造（5周）**
- TenantContextHolder支持多种访问模式
- 数据库拦截器支持层级查询
- 权限控制机制完整实现

**阶段三：业务功能开发（6周）**
- 租户管理界面支持层级展示
- API接口支持层级化数据访问
- 现有业务代码完成适配

**阶段四：性能优化与上线（4周）**
- 性能测试达到预期指标
- 安全测试无高危漏洞
- 生产环境成功部署

### 7.3 关键里程碑

- [ ] 数据库表结构改造完成
- [ ] 租户层级服务开发完成
- [ ] 数据隔离机制改造完成
- [ ] 权限控制机制实现完成
- [ ] 业务功能开发完成
- [ ] 性能测试通过
- [ ] 生产环境部署成功

## 8. 成功标准

### 8.1 功能指标
- 支持三级租户层级管理
- 上级租户可查看下级所有数据
- 权限控制准确无误
- 现有功能完全兼容

### 8.2 性能指标
- 层级查询响应时间 < 500ms（95%分位）
- 数据库连接池使用率 < 80%
- 内存使用增长 < 20%
- 缓存命中率 > 85%

### 8.3 安全指标
- 无权限提升漏洞
- 完整的访问审计日志
- 敏感数据正确脱敏
- 异常访问及时告警

## 9. 总结与建议

### 9.1 方案可行性评估

**技术可行性：高**
- 基于现有成熟的多租户框架进行增强
- 采用业界成熟的层级数据模型设计
- 渐进式改造，风险可控

**业务价值：高**
- 满足分销商业务模式需求
- 提升数据管理效率
- 增强系统商业竞争力

### 9.2 关键成功因素

1. **充分的测试验证**：确保数据迁移和功能改造的正确性
2. **性能优化**：重点关注多租户查询的性能影响
3. **安全加固**：严格的权限控制和访问审计
4. **平滑过渡**：保持现有功能的完全兼容

### 9.3 后续优化方向

- 考虑支持更深层级的租户结构
- 实现租户级别的资源配额管理
- 增加租户数据的自动备份和恢复
- 支持租户间的数据迁移和合并

---

**文档版本**：v1.0  
**编制日期**：2025年1月28日  
**技术栈**：Spring Boot 3.4.5 + Java 17 + MyBatis Plus 3.5.10.1
