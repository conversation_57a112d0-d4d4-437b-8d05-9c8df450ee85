package com.muzi.yichao.module.infra.controller.admin.demo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import java.util.*;

@Schema(description = "管理后台 - 分类新增/修改 Request VO")
@Data
public class InfraCategorySaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "名字", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋头")
    @NotEmpty(message = "名字不能为空")
    private String name;

    @Schema(description = "父编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "2048")
    @NotNull(message = "父编号不能为空")
    private Long parentId;

}