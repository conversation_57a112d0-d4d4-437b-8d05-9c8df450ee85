# SaaS智能家装CRM系统 - 方案列表页面功能设计文档

## 1. 页面概述

### 1.1 页面目的
方案列表页面是智能家居模块的方案管理中心，用于展示、管理和操作所有智能家居设计方案。该页面提供方案的列表展示、筛选搜索、状态管理和快速操作入口，是方案管理的核心页面。

### 1.2 业务价值
- 提供方案的统一管理视图，支持快速查找和定位方案
- 建立方案状态跟踪体系，实时掌握方案进度
- 提供方案的快速操作入口，提升工作效率
- 支持方案数据的统计分析，为业务决策提供支持

### 1.3 页面位置
- **所属模块**: 智能家居模块
- **业务流程位置**: 需求收集 → **方案设计** → 方案实施
- **关联页面**: 
  - 客户需求页面（需求数据来源）
  - 图纸页面、场景页面、工单页面、报价单页面、订单页面（详细管理页面）

## 2. 方案列表页面操作流程图

```mermaid
flowchart TD
    A[用户访问方案列表页面] --> B[权限验证]
    B --> C{权限验证通过?}
    C -->|否| D[显示权限不足提示]
    C -->|是| E[加载方案列表数据]

    E --> F[显示方案列表]
    E --> G[显示筛选条件]
    E --> H[显示操作工具栏]

    F --> I[方案卡片展示]
    I --> J[方案基本信息]
    I --> K[方案状态标识]
    I --> L[操作按钮组]

    G --> M[客户筛选]
    G --> N[状态筛选]
    G --> O[设计师筛选]
    G --> P[时间筛选]

    H --> Q[新增方案]
    H --> R[批量操作]
    H --> S[导入导出]

    L --> T[查看详情]
    L --> U[编辑方案]
    L --> V[图纸管理]
    L --> W[场景配置]
    L --> X[工单管理]
    L --> Y[报价单]
    L --> Z[订单管理]
    L --> AA[加入待办]

    Q --> BB[跳转新增方案页面]
    V --> CC[跳转图纸页面]
    W --> DD[跳转场景页面]
    X --> EE[跳转工单页面]
    Y --> FF[跳转报价单页面]
    Z --> GG[跳转订单页面]
    AA --> HH[跳转待办页面]

    M --> II[实时筛选更新]
    N --> II
    O --> II
    P --> II

    II --> JJ[更新方案列表]
    JJ --> F

    style A fill:#e1f5fe
    style C fill:#fff3e0
    style D fill:#ffebee
    style II fill:#f3e5f5
    style JJ fill:#e8f5e8
```

### 流程说明
方案列表页面的操作流程包含以下核心环节：

1. **权限验证与数据加载**：用户访问页面时进行权限验证，加载方案列表数据
2. **方案展示与筛选**：以卡片形式展示方案信息，支持多维度筛选
3. **方案操作管理**：提供方案的各种操作入口和管理功能
4. **页面跳转导航**：支持跳转到相关的详细管理页面
5. **实时数据更新**：筛选条件变化时实时更新方案列表

## 3. 详细功能设计

### 3.1 方案列表展示功能

#### 3.1.1 方案卡片展示
**功能描述**: 以卡片形式展示方案的核心信息

**卡片信息**:
- **方案名称**: 方案的名称和简要描述
- **客户信息**: 关联客户的姓名和联系方式
- **方案类型**: 全屋智能/局部改造/新房装修/旧房改造
- **设计师**: 负责的设计师姓名
- **方案状态**: 设计中/待审核/已审核/实施中/已完成
- **创建时间**: 方案创建的时间
- **预算金额**: 方案的预算金额
- **完成进度**: 方案实施的完成进度百分比

#### 3.1.2 状态标识系统
**功能描述**: 清晰的方案状态标识和进度显示

**状态类型**:
- **设计中**: 蓝色标签，方案正在设计阶段
- **待审核**: 橙色标签，方案等待审核
- **已审核**: 绿色标签，方案审核通过
- **实施中**: 紫色标签，方案正在实施
- **已完成**: 灰色标签，方案已完成
- **已暂停**: 红色标签，方案暂停执行

#### 3.1.3 快速操作按钮
**功能描述**: 每个方案卡片提供的快速操作按钮

**操作按钮**:
- **查看详情**: 查看方案的详细信息
- **编辑方案**: 编辑方案的基本信息
- **图纸**: 跳转到图纸管理页面
- **场景**: 跳转到场景配置页面
- **工单**: 跳转到工单管理页面
- **报价单**: 跳转到报价单页面
- **订单**: 跳转到订单管理页面
- **加入待办**: 将方案加入待办事项

### 3.2 筛选搜索功能

#### 3.2.1 基础筛选条件
**功能描述**: 提供常用的筛选条件

**筛选字段**:
- **客户筛选**: 按客户姓名或联系方式筛选
- **状态筛选**: 按方案状态筛选
- **设计师筛选**: 按负责设计师筛选
- **方案类型**: 按方案类型筛选
- **时间范围**: 按创建时间范围筛选

#### 3.2.2 高级筛选功能
**功能描述**: 提供更多的筛选维度

**高级筛选**:
- **预算范围**: 按预算金额范围筛选
- **完成进度**: 按完成进度筛选
- **标签筛选**: 按方案标签筛选
- **优先级**: 按方案优先级筛选

#### 3.2.3 搜索功能
**功能描述**: 支持关键词搜索

**搜索范围**:
- **方案名称**: 方案名称的模糊搜索
- **客户信息**: 客户姓名和联系方式搜索
- **方案编号**: 方案编号的精确搜索
- **备注信息**: 方案备注的关键词搜索

### 3.3 操作工具栏功能

#### 3.3.1 新增方案功能
**功能描述**: 创建新的智能家居方案

**操作流程**:
- 点击"新增方案"按钮
- 跳转到新增方案页面
- 填写方案基本信息
- 保存并返回方案列表

#### 3.3.2 批量操作功能
**功能描述**: 对多个方案进行批量操作

**批量操作**:
- **批量删除**: 删除选中的多个方案
- **批量修改状态**: 批量修改方案状态
- **批量分配**: 批量分配设计师
- **批量导出**: 导出选中方案的信息

#### 3.3.3 导入导出功能
**功能描述**: 方案数据的导入和导出

**导入功能**:
- **Excel导入**: 批量导入方案基本信息
- **模板下载**: 提供导入模板下载

**导出功能**:
- **Excel导出**: 导出方案列表数据
- **PDF报告**: 生成方案汇总报告

### 3.4 页面跳转功能

#### 3.4.1 详细管理页面跳转
**功能描述**: 跳转到方案的详细管理页面

**跳转页面**:
- **图纸页面**: 管理方案相关的设计图纸
- **场景页面**: 配置智能家居场景
- **工单页面**: 管理方案实施工单
- **报价单页面**: 管理方案报价信息
- **订单页面**: 管理方案相关订单（关联到销售模块）
- **待办页面**: 管理方案相关的待办事项

#### 3.4.2 页面间数据传递
**功能描述**: 页面跳转时的数据传递机制

**传递数据**:
- **方案ID**: 当前方案的唯一标识
- **客户信息**: 关联的客户基本信息
- **方案状态**: 当前方案的状态信息
- **权限信息**: 用户对该方案的操作权限

## 4. 用户界面设计

### 4.1 页面布局设计
- **顶部工具栏**: 新增、批量操作、导入导出等功能按钮
- **筛选条件区**: 多维度筛选条件和搜索框
- **方案列表区**: 主要的方案卡片展示区域
- **分页导航区**: 底部分页导航和页面信息

### 4.2 卡片设计规范
- **卡片布局**: 统一的卡片布局和信息层次
- **状态标识**: 清晰的状态标签和进度条
- **操作按钮**: 统一的按钮样式和布局
- **响应式设计**: 适配不同屏幕尺寸的响应式布局

### 4.3 交互设计规范
- **悬停效果**: 卡片悬停时的阴影和高亮效果
- **点击反馈**: 按钮点击时的视觉反馈
- **加载状态**: 数据加载时的骨架屏显示
- **空状态**: 无数据时的友好提示

## 5. 数据流向

### 5.1 数据输入
- **来源**: 客户需求页面的需求数据
- **格式**: 结构化的方案数据

### 5.2 数据输出
- **流向**: 各详细管理页面（图纸、场景、工单、报价、订单）
- **关联**: 销售模块的销售单管理页面（订单数据）

### 5.3 业务关联
- **前置页面**: 客户需求页面（需求转化为方案）
- **后续页面**: 各专业管理页面（方案细化和实施）
- **关联模块**: 销售管理模块（订单管理）

## 6. 权限控制

### 6.1 数据权限
- **设计师权限**: 只能查看和操作自己负责的方案
- **部门权限**: 部门经理可以查看部门内所有方案
- **客户权限**: 客户只能查看自己的方案信息

### 6.2 操作权限
- **查看权限**: 方案信息的查看权限
- **编辑权限**: 方案信息的编辑权限
- **删除权限**: 方案删除权限
- **状态权限**: 方案状态修改权限

## 7. 异常处理

### 7.1 数据异常
- **加载失败**: 方案数据加载失败的重试机制
- **筛选异常**: 筛选条件异常的处理
- **跳转失败**: 页面跳转失败的处理

### 7.2 操作异常
- **权限不足**: 权限不足的友好提示
- **网络异常**: 网络连接异常的处理
- **并发冲突**: 多人操作冲突的处理

---

**文档版本**: v1.0
**编写日期**: 2025-07-14
**编写人员**: AI系统架构师
**审核状态**: 待审核
**对应图片**: 方案列表页面.png
