package com.muzi.yichao.module.trade.convert.order;

import com.muzi.yichao.module.trade.dal.dataobject.order.TradeOrderLogDO;
import com.muzi.yichao.module.trade.service.order.bo.TradeOrderLogCreateReqBO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface TradeOrderLogConvert {

    TradeOrderLogConvert INSTANCE = Mappers.getMapper(TradeOrderLogConvert.class);

    TradeOrderLogDO convert(TradeOrderLogCreateReqBO bean);

}
