# SaaS智能家装CRM系统 - 新增场景页面功能设计文档

## 1. 页面概述

### 1.1 页面目的
新增场景页面用于创建新的智能家居场景配置，提供场景基本信息设置、触发条件配置、执行动作设置和逻辑关系定义功能。该页面通过可视化的配置界面，让用户能够轻松创建复杂的智能场景。

### 1.2 业务价值
- 提供直观的场景创建界面，降低智能场景配置门槛
- 标准化场景创建流程，确保场景配置的完整性和正确性
- 支持复杂逻辑的可视化配置，提升配置效率
- 建立场景配置的验证机制，确保场景的可执行性

### 1.3 页面位置
- **所属模块**: 智能家居模块
- **业务流程位置**: 方案设计 → 场景配置 → **新增场景**
- **关联页面**: 
  - 场景页面（入口页面）
  - 方案列表页面（方案关联）

## 2. 新增场景页面操作流程图

```mermaid
flowchart TD
    A[用户点击新增场景] --> B[跳转新增场景页面]
    B --> C[权限验证]
    C --> D{权限验证通过?}
    D -->|否| E[显示权限不足提示]
    D -->|是| F[加载页面表单]

    F --> G[显示场景基本信息区]
    F --> H[显示触发条件配置区]
    F --> I[显示执行动作配置区]
    F --> J[显示逻辑关系配置区]

    G --> K[场景名称输入]
    G --> L[场景类型选择]
    G --> M[场景描述输入]
    G --> N[适用房间选择]

    H --> O[触发方式选择]
    O --> P[时间触发配置]
    O --> Q[设备触发配置]
    O --> R[环境触发配置]
    O --> S[手动触发配置]

    P --> T[设置触发时间]
    Q --> U[选择触发设备]
    R --> V[设置环境条件]
    S --> W[设置手动方式]

    I --> X[动作类型选择]
    X --> Y[设备控制动作]
    X --> Z[场景联动动作]
    X --> AA[通知提醒动作]

    Y --> BB[选择控制设备]
    Y --> CC[设置控制参数]
    Z --> DD[选择联动场景]
    AA --> EE[设置通知方式]

    J --> FF[逻辑关系设置]
    FF --> GG[AND/OR逻辑]
    FF --> HH[优先级设置]
    FF --> II[冲突处理]

    K --> JJ[配置验证]
    L --> JJ
    T --> JJ
    U --> JJ
    BB --> JJ

    JJ --> KK{验证通过?}
    KK -->|否| LL[显示错误提示]
    KK -->|是| MM[场景预览]

    MM --> NN[显示场景配置]
    NN --> OO[用户确认]
    OO --> PP{确认保存?}
    PP -->|否| G
    PP -->|是| QQ[保存场景配置]

    QQ --> RR[生成场景编号]
    RR --> SS[关联方案]
    SS --> TT[更新场景库]
    TT --> UU[保存成功提示]
    UU --> VV[跳转场景页面]

    style A fill:#e1f5fe
    style D fill:#fff3e0
    style E fill:#ffebee
    style KK fill:#f3e5f5
    style PP fill:#f3e5f5
    style QQ fill:#e8f5e8
```

### 流程说明
新增场景页面的操作流程包含以下核心环节：

1. **页面访问与权限验证**：从场景页面跳转，进行权限验证
2. **场景基本信息设置**：设置场景的名称、类型、描述等基本信息
3. **触发条件配置**：配置场景的触发条件和触发方式
4. **执行动作设置**：设置场景执行时的具体动作和参数
5. **逻辑关系定义**：定义复杂的逻辑关系和冲突处理
6. **配置验证与保存**：验证配置的正确性并保存场景

## 3. 详细功能设计

### 3.1 场景基本信息设置

#### 3.1.1 基本信息录入
**功能描述**: 录入场景的基本信息

**录入字段**:
- **场景名称**: 场景的名称，必填字段，支持中英文
- **场景描述**: 场景的详细描述和说明
- **场景类型**: 下拉选择（生活场景/安全场景/娱乐场景/节能场景/自定义场景）
- **场景图标**: 选择场景的显示图标
- **适用房间**: 多选场景适用的房间
- **场景标签**: 添加场景标签便于分类管理

#### 3.1.2 场景属性设置
**功能描述**: 设置场景的属性和特性

**属性设置**:
- **优先级**: 设置场景的执行优先级（高/中/低）
- **有效期**: 设置场景的有效时间范围
- **执行模式**: 设置场景的执行模式（立即执行/延迟执行/条件执行）
- **重复执行**: 设置场景是否可重复执行
- **冲突策略**: 设置与其他场景冲突时的处理策略

### 3.2 触发条件配置功能

#### 3.2.1 时间触发配置
**功能描述**: 配置基于时间的触发条件

**时间触发类型**:
- **定时触发**: 设置具体的触发时间（小时:分钟）
- **周期触发**: 设置重复的触发周期（每天/每周/每月）
- **日出日落**: 基于日出日落时间的触发
- **节假日**: 基于节假日和特殊日期的触发
- **时间范围**: 设置触发的有效时间范围

#### 3.2.2 设备触发配置
**功能描述**: 配置基于设备状态的触发条件

**设备触发类型**:
- **开关状态**: 基于开关设备的状态变化触发
- **传感器数据**: 基于传感器数据的阈值触发
- **设备故障**: 基于设备故障状态的触发
- **设备联动**: 基于其他设备状态变化的触发
- **数据变化**: 基于设备数据变化的触发

#### 3.2.3 环境触发配置
**功能描述**: 配置基于环境条件的触发

**环境触发类型**:
- **温度条件**: 基于温度变化的触发（高于/低于/等于）
- **湿度条件**: 基于湿度变化的触发
- **光照条件**: 基于光照强度的触发
- **空气质量**: 基于空气质量指数的触发
- **天气条件**: 基于天气状况的触发

#### 3.2.4 人员触发配置
**功能描述**: 配置基于人员活动的触发

**人员触发类型**:
- **人体感应**: 基于人体感应器的检测触发
- **门窗状态**: 基于门窗开关状态的触发
- **位置信息**: 基于手机位置信息的触发
- **人脸识别**: 基于人脸识别结果的触发
- **语音指令**: 基于语音指令的触发

### 3.3 执行动作设置功能

#### 3.3.1 设备控制动作
**功能描述**: 设置对智能设备的控制动作

**控制动作类型**:
- **开关控制**: 控制设备的开关状态
- **调节控制**: 调节设备的参数（亮度/音量/温度等）
- **模式切换**: 切换设备的工作模式
- **定时控制**: 设置设备的定时操作
- **渐变控制**: 设置设备参数的渐变变化

#### 3.3.2 场景联动动作
**功能描述**: 设置与其他场景的联动动作

**联动动作类型**:
- **启动场景**: 启动其他相关场景
- **停止场景**: 停止正在执行的场景
- **延迟执行**: 延迟执行特定动作
- **条件执行**: 基于条件决定是否执行动作
- **循环执行**: 设置动作的循环执行

#### 3.3.3 通知提醒动作
**功能描述**: 设置通知和提醒动作

**通知动作类型**:
- **手机推送**: 发送手机推送通知
- **语音播报**: 通过音响播报信息
- **屏幕显示**: 在智能屏幕显示信息
- **邮件通知**: 发送邮件通知
- **短信通知**: 发送短信通知

### 3.4 逻辑关系配置功能

#### 3.4.1 条件逻辑设置
**功能描述**: 设置复杂的条件逻辑关系

**逻辑关系类型**:
- **AND逻辑**: 所有条件都满足时触发
- **OR逻辑**: 任一条件满足时触发
- **NOT逻辑**: 条件不满足时触发
- **复合逻辑**: 多种逻辑关系的组合
- **时间窗口**: 在特定时间窗口内的条件判断

#### 3.4.2 执行顺序设置
**功能描述**: 设置动作的执行顺序和时序

**时序控制**:
- **并行执行**: 多个动作同时执行
- **串行执行**: 动作按顺序依次执行
- **延迟执行**: 设置动作的延迟时间
- **条件执行**: 基于条件决定执行顺序
- **优先级执行**: 按优先级顺序执行

#### 3.4.3 冲突处理设置
**功能描述**: 设置场景冲突的处理策略

**冲突处理策略**:
- **优先级覆盖**: 高优先级场景覆盖低优先级
- **互斥处理**: 互斥场景不能同时执行
- **合并执行**: 兼容场景可以合并执行
- **队列等待**: 冲突场景排队等待执行
- **用户选择**: 由用户选择冲突处理方式

### 3.5 配置验证功能

#### 3.5.1 配置完整性验证
**功能描述**: 验证场景配置的完整性

**验证内容**:
- **必填字段**: 检查必填字段是否完整
- **设备可用性**: 检查相关设备是否可用
- **逻辑正确性**: 检查逻辑配置是否正确
- **参数有效性**: 检查参数设置是否有效

#### 3.5.2 场景预览功能
**功能描述**: 预览场景的配置和执行效果

**预览内容**:
- **配置摘要**: 显示场景配置的摘要信息
- **执行流程**: 显示场景的执行流程图
- **设备状态**: 预览设备状态的变化
- **执行时间**: 预估场景的执行时间

## 4. 用户界面设计

### 4.1 页面布局设计
- **顶部导航区**: 页面标题、步骤指示和返回按钮
- **配置向导区**: 分步骤的配置向导界面
- **可视化配置区**: 拖拽式的可视化配置界面
- **预览确认区**: 配置预览和确认区域
- **底部操作区**: 保存、取消、预览等操作按钮

### 4.2 配置界面设计
- **步骤指示**: 清晰的配置步骤指示器
- **拖拽配置**: 支持拖拽的可视化配置
- **参数面板**: 动态的参数配置面板
- **实时预览**: 配置过程中的实时预览

### 4.3 交互设计规范
- **向导式配置**: 分步骤引导用户完成配置
- **智能提示**: 根据配置提供智能建议
- **错误提示**: 清晰的错误信息和修正建议
- **保存提醒**: 配置变更时的保存提醒

## 5. 数据流向

### 5.1 数据输入
- **来源**: 用户通过界面配置的场景数据
- **格式**: 结构化的场景配置数据

### 5.2 数据输出
- **流向**: 场景页面（新增的场景记录）
- **存储**: 场景配置数据库

### 5.3 业务关联
- **前置页面**: 场景页面（入口）
- **关联数据**: 方案信息、设备信息
- **后续应用**: 智能家居控制系统中的场景执行

## 6. 权限控制

### 6.1 访问权限
- **设计师**: 可以为负责方案创建场景
- **技术人员**: 可以创建和配置技术场景
- **项目经理**: 可以创建项目相关场景

### 6.2 操作权限
- **创建权限**: 场景创建权限控制
- **设备权限**: 设备控制权限验证
- **场景权限**: 场景联动权限控制
- **发布权限**: 场景发布权限控制

## 7. 异常处理

### 7.1 配置异常
- **设备不可用**: 设备不可用时的提示和处理
- **逻辑错误**: 逻辑配置错误的检测和提示
- **参数无效**: 参数无效时的错误提示

### 7.2 操作异常
- **权限不足**: 权限不足的友好提示
- **网络异常**: 网络连接异常的处理
- **数据丢失**: 意外关闭页面时的数据恢复

---

**文档版本**: v1.0
**编写日期**: 2025-07-14
**编写人员**: AI系统架构师
**审核状态**: 待审核
**对应图片**: 方案列表-点击场景-新增场景页面.png
