package com.muzi.yichao.module.customer.dal.dataobject.info;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.muzi.yichao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 客户推荐关系 DO
 *
 * <AUTHOR>
 */
@TableName("customer_info_referral")
@KeySequence("customer_info_referral_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InfoReferralDO extends BaseDO {

    /**
     * 关系ID
     */
    @TableId
    private Long id;
    /**
     * 推荐人ID
     */
    private Long referrerId;
    /**
     * 被推荐人ID
     */
    private Long refereeId;
    /**
     * 推荐时间
     */
    private LocalDateTime referralTime;
    /**
     * 状态：1-有效，2-无效
     */
    private Integer status;
    /**
     * 推荐奖励金额
     */
    private BigDecimal rewardAmount;
    /**
     * 奖励状态：0-未发放，1-已发放
     */
    private Integer rewardStatus;
    /**
     * 备注
     */
    private String remark;

}