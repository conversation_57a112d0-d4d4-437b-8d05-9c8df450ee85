package ${basePackage}.module.${table.moduleName}.dal.dataobject.${table.businessName};

import lombok.*;
import java.util.*;
#foreach ($column in $columns)
#if (${column.javaType} == "BigDecimal")
import java.math.BigDecimal;
#end
#if (${column.javaType} == "LocalDateTime")
import java.time.LocalDateTime;
#end
#end
import com.baomidou.mybatisplus.annotation.*;
import ${BaseDOClassName};
## 处理 Excel 导出 + Schema 注解（仅 DO 模式）
#if ($voType == 20)
import io.swagger.v3.oas.annotations.media.Schema;
import com.alibaba.excel.annotation.*;
#foreach ($column in $columns)
    #if ("$!column.dictType" != "")## 有设置数据字典
		import ${DictFormatClassName};
		import ${DictConvertClassName};
        #break
    #end
#end
#end

/**
 * ${table.classComment} DO
 *
 * <AUTHOR>
 */
@TableName("${table.tableName.toLowerCase()}")
@KeySequence("${table.tableName.toLowerCase()}_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
## 处理 Excel 导出 + Schema 注解（仅 DO 模式）
#if ($voType == 20)
@Schema(description = "${sceneEnum.name} - ${table.classComment} Response VO")
@ExcelIgnoreUnannotated
#end
public class ${table.className}DO extends BaseDO {

## 特殊：树表专属逻辑
#if ( $table.templateType == 2 )
    public static final Long ${treeParentColumn_javaField_underlineCase.toUpperCase()}_ROOT = 0L;

#end
#foreach ($column in $columns)
#if (!${baseDOFields.contains(${column.javaField})})##排除 BaseDO 的字段
    /**
     * ${column.columnComment}
    #if ("$!column.dictType" != "")##处理枚举值
     *
     * 枚举 {@link TODO ${column.dictType} 对应的类}
    #end
     */
    #if (${column.primaryKey})##处理主键
    @TableId#if (${column.javaType} == 'String')(type = IdType.INPUT)#end
    #end
#if ($voType == 20)
## 1. 处理 Swagger 注解
    @Schema(description = "${column.columnComment}"#if (!${column.nullable}), requiredMode = Schema.RequiredMode.REQUIRED#end#if ("$!column.example" != ""), example = "${column.example}"#end)
## 2. 处理 Excel 导出
#if ("$!column.dictType" != "")##处理枚举值
    @ExcelProperty(value = "${column.columnComment}", converter = DictConvert.class)
    @DictFormat("${column.dictType}") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
#else
    @ExcelProperty("${column.columnComment}")
#end
#end
## 3. 处理字段定义
    private ${column.javaType} ${column.javaField};
#end
#end

## 特殊：主子表专属逻辑（非 ERP 模式）
#if ( $voType == 20 && $subTables && $subTables.size() > 0 && $table.templateType != 11 )
    #foreach ($subTable in $subTables)
        #set ($index = $foreach.count - 1)
        #if ( $subTable.subJoinMany)
    /**
    * ${subTable.classComment}列表
    */
    @Schema(description = "${subTable.classComment}列表")
    @TableField(exist = false)
    private List<${subTable.className}DO> ${subClassNameVars.get($index)}s;
        #else
    /**
    * ${subTable.classComment}
    */
    @Schema(description = "${subTable.classComment}")
	@TableField(exist = false)
    private ${subTable.className}DO ${subClassNameVars.get($index)};
        #end
    #end
#end

}