-- 顶级菜单 SQL - 客户管理（目录类型，不显示页面）
INSERT INTO system_menu(
    name, permission, type, sort, parent_id,
    path, icon, component, status
)
VALUES (
    '客户管理', '', 1, 0, 0,
    'customer', 'ep:avatar', '', 0
);

-- 获取顶级菜单ID
-- 暂时只支持 MySQL。如果你是 Oracle、PostgreSQL、SQLServer 的话，需要手动修改 @topMenuId 的部分的代码
SELECT @topMenuId := LAST_INSERT_ID();

-- 子菜单 SQL - 客户数据
INSERT INTO system_menu(
    name, permission, type, sort, parent_id,
    path, icon, component, status, component_name
)
VALUES (
    '客户数据', '', 2, 1, @topMenuId,
    'info', 'fa:address-book-o', 'customer/info/index', 0, 'Info'
);

-- 按钮父菜单ID（客户数据菜单）
-- 暂时只支持 MySQL。如果你是 Oracle、PostgreSQL、SQLServer 的话，需要手动修改 @parentId 的部分的代码
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL（基于客户数据菜单）
INSERT INTO system_menu(
    name, permission, type, sort, parent_id,
    path, icon, component, status
)
VALUES (
    '客户数据查询', 'customer:info:query', 3, 1, @parentId,
    '', '', '', 0
);
INSERT INTO system_menu(
    name, permission, type, sort, parent_id,
    path, icon, component, status
)
VALUES (
    '客户数据创建', 'customer:info:create', 3, 2, @parentId,
    '', '', '', 0
);
INSERT INTO system_menu(
    name, permission, type, sort, parent_id,
    path, icon, component, status
)
VALUES (
    '客户数据更新', 'customer:info:update', 3, 3, @parentId,
    '', '', '', 0
);
INSERT INTO system_menu(
    name, permission, type, sort, parent_id,
    path, icon, component, status
)
VALUES (
    '客户数据删除', 'customer:info:delete', 3, 4, @parentId,
    '', '', '', 0
);
INSERT INTO system_menu(
    name, permission, type, sort, parent_id,
    path, icon, component, status
)
VALUES (
    '客户数据导出', 'customer:info:export', 3, 5, @parentId,
    '', '', '', 0
);
