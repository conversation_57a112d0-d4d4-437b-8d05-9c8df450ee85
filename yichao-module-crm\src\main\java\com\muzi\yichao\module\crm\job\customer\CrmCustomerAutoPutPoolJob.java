package com.muzi.yichao.module.crm.job.customer;

import com.muzi.yichao.framework.quartz.core.handler.JobHandler;
import com.muzi.yichao.framework.tenant.core.job.TenantJob;
import com.muzi.yichao.module.crm.service.customer.CrmCustomerService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * 客户自动掉入公海 Job
 *
 * <AUTHOR>
 */
@Component
public class CrmCustomerAutoPutPoolJob implements JobHandler {

    @Resource
    private CrmCustomerService customerService;

    @Override
    @TenantJob
    public String execute(String param) {
        int count = customerService.autoPutCustomerPool();
        return String.format("掉入公海客户 %s 个", count);
    }

}