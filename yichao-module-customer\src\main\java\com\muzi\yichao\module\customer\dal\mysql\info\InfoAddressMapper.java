package com.muzi.yichao.module.customer.dal.mysql.info;

import java.util.*;

import com.muzi.yichao.framework.common.pojo.PageResult;
import com.muzi.yichao.framework.common.pojo.PageParam;
import com.muzi.yichao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.muzi.yichao.framework.mybatis.core.mapper.BaseMapperX;
import com.muzi.yichao.module.customer.dal.dataobject.info.InfoAddressDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 客户收货地址 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InfoAddressMapper extends BaseMapperX<InfoAddressDO> {

    default PageResult<InfoAddressDO> selectPage(PageParam reqVO, Long customerId) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InfoAddressDO>()
            .eq(InfoAddressDO::getCustomerId, customerId)
            .orderByDesc(InfoAddressDO::getId));
    }
        default InfoAddressDO selectByCustomerId(Long customerId) {
        return selectOne(InfoAddressDO::getCustomerId, customerId);
        }

    default int deleteByCustomerId(Long customerId) {
        return delete(InfoAddressDO::getCustomerId, customerId);
    }

	default int deleteByCustomerIds(List<Long> customerIds) {
	    return deleteBatch(InfoAddressDO::getCustomerId, customerIds);
	}

}
