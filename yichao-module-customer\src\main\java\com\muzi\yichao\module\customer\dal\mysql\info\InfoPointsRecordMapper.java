package com.muzi.yichao.module.customer.dal.mysql.info;

import java.util.*;

import com.muzi.yichao.framework.common.pojo.PageResult;
import com.muzi.yichao.framework.common.pojo.PageParam;
import com.muzi.yichao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.muzi.yichao.framework.mybatis.core.mapper.BaseMapperX;
import com.muzi.yichao.module.customer.dal.dataobject.info.InfoPointsRecordDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 客户积分记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InfoPointsRecordMapper extends BaseMapperX<InfoPointsRecordDO> {

    default PageResult<InfoPointsRecordDO> selectPage(PageParam reqVO, Long customerId) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InfoPointsRecordDO>()
            .eq(InfoPointsRecordDO::getCustomerId, customerId)
            .orderByDesc(InfoPointsRecordDO::getId));
    }

    default int deleteByCustomerId(Long customerId) {
        return delete(InfoPointsRecordDO::getCustomerId, customerId);
    }

	default int deleteByCustomerIds(List<Long> customerIds) {
	    return deleteBatch(InfoPointsRecordDO::getCustomerId, customerIds);
	}

}
