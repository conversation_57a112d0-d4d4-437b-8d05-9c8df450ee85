<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.muzi.smarthome</groupId>
        <artifactId>yichao-framework</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>yichao-spring-boot-starter-protection</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>服务保证，提供分布式锁、幂等、限流、熔断、API 签名等等功能</description>
    <url>https://github.com/YunaiV/ruoyi-vue-pro</url>

    <dependencies>
        <!-- Web 相关 -->
        <dependency>
            <groupId>com.muzi.smarthome</groupId>
            <artifactId>yichao-spring-boot-starter-web</artifactId>
            <scope>provided</scope> <!-- 设置为 provided，只有限流、幂等使用到 -->
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.muzi.smarthome</groupId>
            <artifactId>yichao-spring-boot-starter-redis</artifactId>
        </dependency>

        <!-- 服务保障相关 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>lock4j-redisson-spring-boot-starter</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>com.muzi.smarthome</groupId>
            <artifactId>yichao-spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>
